package com.czur.starry.device.voiceassistant.widget

import android.animation.Animator
import android.animation.AnimatorListenerAdapter
import android.animation.ObjectAnimator
import android.content.Context
import android.graphics.drawable.Drawable
import android.os.Handler
import android.util.AttributeSet
import android.view.View
import androidx.core.content.ContextCompat

/**
 *  author : <PERSON><PERSON><PERSON>
 *  time   :2025/02/13
 *  Studio 星空背景动画
 */


class AnimatedImageBgView @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
) : View(context, attrs, defStyleAttr) {

    private val handler = Handler()
    private var currentImageIndex = 60
    private val loopStart = 60
    private val loopEnd = 309
    private val packageName = context.packageName
    private var isQ2Series: Boolean = false

    private fun updateImage() {
        val resourceName = "bg_%05d".format(currentImageIndex)
        val resourceId = context.resources.getIdentifier(resourceName, "drawable", packageName)
        val drawable: Drawable? = ContextCompat.getDrawable(context, resourceId)
        drawable?.let {
            background = it
        }

        // 更新索引
        if (currentImageIndex < loopEnd) {
            currentImageIndex++
        } else {
            // 循环到末尾，回到循环起始点
            currentImageIndex = loopStart
        }
    }

    fun initAnimation(isQ2Series: Boolean = false) {
        this.isQ2Series = isQ2Series
        visibility = VISIBLE
        if (isQ2Series) {

        } else {
            updateImage()
            animation(true)
        }

    }

    private fun startAnimation(interval: Long = 30L) {
        handler.postDelayed({
            updateImage()
            startAnimation() // 递归调用保持动画
        }, interval)
    }

    fun stopAnimation() {
        handler.removeCallbacksAndMessages(null)
        animation(false)
    }

    /**
     * 动画,改变透明度
     */
    private fun animation(isShow: Boolean = true) {
        var animator: ObjectAnimator = if (isShow) {
            ObjectAnimator.ofFloat(this, "alpha", 0f, 1f)
       }else {
            ObjectAnimator.ofFloat(this, "alpha", 1f, 0f)
       }
        animator.duration = 300 // 目前设置为300毫秒
        animator.addListener(object : AnimatorListenerAdapter() {
            override fun onAnimationEnd(animation: Animator) {
                if (isShow) {
                    startAnimation()
                }else {
                    visibility = GONE
                }

            }
        })
        animator.start()
    }

    // 重写onDetachedFromWindow以在View被销毁时停止动画
    override fun onDetachedFromWindow() {
        super.onDetachedFromWindow()
        stopAnimation()
    }
}