package com.czur.starry.device.voiceassistant.util

import android.app.ActivityManager
import android.app.Instrumentation
import android.content.Context
import android.content.Intent
import android.media.AudioManager
import android.os.Handler
import android.os.Looper
import android.os.PowerManager
import android.os.SystemClock
import android.view.KeyEvent
import com.czur.czurutils.log.logTagE
import com.czur.starry.device.baselib.base.CZURAtyManager
import com.czur.starry.device.baselib.utils.ONE_SECOND
import com.czur.starry.device.file.filelib.FileHandlerLive.fileKeyCodeStatus
import com.czur.starry.device.voiceassistant.VoiceAssistantService
import com.czur.starry.device.voiceassistant.helper.Adjustable
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.MainScope
import kotlinx.coroutines.launch

/**
 *  author : <PERSON><PERSON><PERSON>
 *  time   :2025/02/27
 */


object SystemUtil {
    private const val SCREEN_SHOT_ACTION = "android.intent.action.SCREENSHOT"
    private const val SHUTDOWN_ACTION = "android.intent.action.SHUTDOWN_UI"
    private val handler = Handler(Looper.getMainLooper())
    private var powerManager: PowerManager? = null
    private val audioManager by lazy {
        CZURAtyManager.appContext.getSystemService(Context.AUDIO_SERVICE) as AudioManager
    }
    private val instrumentation by lazy {
        Instrumentation()
    }

    private fun getPowerManager(): PowerManager {
        if (powerManager == null) {
            powerManager = CZURAtyManager.appContext
                .getSystemService<PowerManager>(PowerManager::class.java)
        }
        return powerManager!!
    }

    /**
     * 返回桌面
     */
    fun goBackHome() {
        val homeIntent = Intent(Intent.ACTION_MAIN).apply {
            addCategory(Intent.CATEGORY_HOME)
            flags = Intent.FLAG_ACTIVITY_NEW_TASK
        }
        CZURAtyManager.appContext.startActivity(homeIntent)
    }

    /**
     * 屏幕息屏
     */
    fun onScreenOff() {
        MainScope().launch {
            getPowerManager().goToSleep(SystemClock.uptimeMillis())
        }
    }

    /**
     * 屏幕截图
     */
    fun onScreenShort() {
        handler.postDelayed(Runnable {
            val intent = Intent(SCREEN_SHOT_ACTION)
            CZURAtyManager.appContext.sendBroadcast(intent)
        }, ONE_SECOND)
    }


    /**
     * PAGE UP / PAGE DOWN
     */
    fun pageUpDown(keyCode: Int) {
        MainScope().launch(Dispatchers.IO) {
            if (isWebActivity()) {
                fileKeyCodeStatus = keyCode
            } else {
                instrumentation.sendKeyDownUpSync(keyCode)
            }
        }
    }

    /**
     * 判断当前Activity
     */
    private const val CLASS_NAME_WEB =
        "com.czur.starry.device.file.view.activity.WebActivity"
    private val activityManager by lazy { CZURAtyManager.appContext.getSystemService(Context.ACTIVITY_SERVICE) as ActivityManager }
    private fun isWebActivity(): Boolean {
        val tasks = activityManager.getRunningTasks(Int.MAX_VALUE) ?: return false
        if (tasks.isNotEmpty()) {
            for (task in tasks) {
                if (task.topActivity?.className == CLASS_NAME_WEB) {
                    return true
                }
            }
        }
        return false
    }

    /**
     * 长按电源键
     */
    fun simulateLongPressPowerKey() {
        MainScope().launch(Dispatchers.IO) {
            // 发送 DOWN 事件
            val downTime = SystemClock.uptimeMillis()
            val downEvent = KeyEvent(
                downTime,
                downTime,
                KeyEvent.ACTION_DOWN,
                KeyEvent.KEYCODE_POWER,
                0
            )
            instrumentation.sendKeySync(downEvent)

            // 模拟长按延迟（如 1000ms）
            SystemClock.sleep(1000)

            // 发送 UP 事件
            val upEvent = KeyEvent(
                downTime,
                SystemClock.uptimeMillis(),
                KeyEvent.ACTION_UP,
                KeyEvent.KEYCODE_POWER,
                0
            )
            instrumentation.sendKeySync(upEvent)
        }
    }


    /**
     * 声音大小
     * @param paramsRes
     */
    fun updateVolume(paramsRes: Adjustable = Adjustable.MIN, percent: String = "") {
        when (paramsRes) {
            Adjustable.ADD -> {
                val currentVolume = audioManager.getStreamVolume(AudioManager.STREAM_MUSIC)
                val maxVolume = audioManager.getStreamMaxVolume(AudioManager.STREAM_MUSIC)
                // 增加音量，但不超过最大音量
                val newVolume = if (currentVolume < maxVolume) currentVolume + 1 else maxVolume
                audioManager.setStreamVolume(
                    AudioManager.STREAM_MUSIC,
                    newVolume,
                    AudioManager.FLAG_SHOW_UI
                )
            }

            Adjustable.SUB -> {
                val currentVolume = audioManager.getStreamVolume(AudioManager.STREAM_MUSIC)
                // 增加音量，但不超过最大音量
                val newVolume = if (currentVolume > 0) currentVolume - 1 else 0
                audioManager.setStreamVolume(
                    AudioManager.STREAM_MUSIC,
                    newVolume,
                    AudioManager.FLAG_SHOW_UI
                )
            }

            Adjustable.MAX -> {
                val maxVolume = audioManager.getStreamMaxVolume(AudioManager.STREAM_MUSIC)
                audioManager.setStreamVolume(
                    AudioManager.STREAM_MUSIC,
                    maxVolume,
                    AudioManager.FLAG_SHOW_UI
                )
            }

            Adjustable.MIN -> {
                audioManager.setStreamVolume(
                    AudioManager.STREAM_MUSIC,
                    0,
                    AudioManager.FLAG_SHOW_UI
                )
            }

            else -> {
                if (percent.isEmpty()) return
                var targetVolume = 5
                var processedPercent = percent
                val maxVolume = audioManager.getStreamMaxVolume(AudioManager.STREAM_MUSIC)
                try {
                    if (percent.contains("%")) {
                        processedPercent = processedPercent.replace("%", "")
                        targetVolume = (maxVolume * processedPercent.toInt() / 100).toInt()
                    } else {
                        targetVolume = processedPercent.toInt()
                    }
                } catch (e: Exception) {
                    logTagE(VoiceAssistantService.TAG, "percent===$percent")
                    e.printStackTrace()
                }

                targetVolume = targetVolume.coerceIn(0, maxVolume)

                audioManager.setStreamVolume(
                    AudioManager.STREAM_MUSIC,
                    targetVolume,
                    AudioManager.FLAG_SHOW_UI
                )
            }
        }

    }

    /**
     * 静音
     */
    fun mediaMute() {
        audioManager.setStreamVolume(
            AudioManager.STREAM_MUSIC,
            0,
            AudioManager.FLAG_SHOW_UI
        )
    }

}