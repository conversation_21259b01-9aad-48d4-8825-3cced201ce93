package com.czur.starry.device.voiceassistant.helper

import android.content.Context
import android.content.Intent
import android.content.pm.PackageManager
import com.czur.starry.device.baselib.common.BootParam.BOOT_KEY_PAGE_MENU_NAME
import com.czur.starry.device.baselib.common.BootParam.BOOT_KEY_PAGE_MENU_NAVIGATE
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext

/**
 *  author : <PERSON><PERSON><PERSON>
 *  time   :2025/02/07
 */


class InstallAppHelper(private val context: Context, private val packageManager: PackageManager) {

    companion object {
        private const val PACKAGE_NAME_APPSTORE = "com.czur.starry.device.appstore"
    }


    /**
     *先根据包名获取启动入口
     */
    fun startAppStoreForInstall(pageMenu: String = "", navigate: String = "") {
        val packages = packageManager.getInstalledApplications(PackageManager.GET_META_DATA)
        val matchingPackages = packages.filter { it.packageName == PACKAGE_NAME_APPSTORE }
        val intent =
            packageManager.getLaunchIntentForPackage(matchingPackages.first().packageName)
        if (intent != null) {
            if (navigate.isNotEmpty()) {
                intent.putExtra(BOOT_KEY_PAGE_MENU_NAME, pageMenu)
            }
            if (navigate.isNotEmpty()) {
                intent.putExtra(BOOT_KEY_PAGE_MENU_NAVIGATE, navigate)
            }
            intent.addFlags(Intent.FLAG_ACTIVITY_SINGLE_TOP)
            context.startActivity(intent)
        }
    }

}