package com.czur.starry.device.voiceassistant.common

/**
 *  author : <PERSON><PERSON><PERSON>
 *  time   :2025/05/27
 */


object Constants {

    const val WAKE_UP_SWITCH = "wake_up_switch"//总开关
    const val WAKE_UP_EVENT = "wake_up"
    const val WAKE_UP_PLAY_VIDEO = "wake_up_play_video"
    const val AUDIO_EVENT = "audioMeta"
    const val TTS_FINISH_EVENT = "tts_message_final"
    const val CHAT_FINAL_EVENT = "chat_final"//快捷键关闭
    const val POWER_OFF_EVENT = "power_off"//息屏键关闭
    const val KEY_MESSAGE = "key_message"
    const val VOICE_ACTIVITY_DETECTED = "voice_activity_detected"
    const val TIME_OUT_EVENT = "timeout_not_response"
    const val TIME_OUT_MILLIS = 3000L

    const val COMMAND_HERE = "here"
    const val COMMAND_OK = "okay"
    const val COMMAND_CANNOT = "cant_do_that_yet"
    const val COMMAND_APP_NOT_INSTALL = "app_not_installed"
    const val COMMAND_NETWORK_ERROR = "network_error"
    const val COMMAND_NOT_FOUND = "not_found"
    const val COMMAND_CANNOT_SLEEP = "cannot_execute_sleep"
}