package com.czur.starry.device.file.view.common

import android.os.Bundle
import com.czur.czurutils.log.logTagI
import com.czur.starry.device.baselib.base.v2.fragment.CZViewBindingFragment
import com.czur.starry.device.baselib.data.provider.UserHandler
import com.czur.starry.device.baselib.utils.setOnDebounceClickListener
import com.czur.starry.device.file.databinding.FragmentNeedLoginBinding

/**
 * Created by 陈丰尧 on 2025/4/9
 */
class NeedLoginFragment : CZViewBindingFragment<FragmentNeedLoginBinding>() {
    companion object {
        private const val TAG = "NeedLoginFragment"
        private const val KEY_INFO = "info"
        fun getInstance(neelLoginInfoRes: Int): NeedLoginFragment {
            return NeedLoginFragment().apply {
                arguments = Bundle().apply {
                    putInt(KEY_INFO, neelLoginInfoRes)
                }
            }
        }
    }

    override fun FragmentNeedLoginBinding.initBindingViews() {
        val infoRes = arguments?.getInt(KEY_INFO)
        infoRes?.let {
            noLoginInfoTv.setText(it)
        }
        loginBtn.setOnDebounceClickListener {
            logTagI(TAG, "点击登录")
            // 跳转到登录页面
            UserHandler.bootPersonalCenter(requireActivity())
        }

    }
}