package com.czur.starry.device.file.view.encryption.setting

import android.os.Bundle
import androidx.core.widget.doAfterTextChanged
import androidx.fragment.app.viewModels
import com.czur.starry.device.baselib.network.core.exception.MiaoHttpException
import com.czur.starry.device.baselib.network.core.exception.MiaoManyMailRequestExp
import com.czur.starry.device.baselib.utils.VerifyException
import com.czur.starry.device.baselib.utils.clearContentText
import com.czur.starry.device.baselib.utils.gone
import com.czur.starry.device.baselib.utils.launch
import com.czur.starry.device.baselib.utils.repeatCollectOnResume
import com.czur.starry.device.baselib.utils.setOnDebounceClickListener
import com.czur.starry.device.baselib.utils.toast
import com.czur.starry.device.file.R
import com.czur.starry.device.file.databinding.FragmentEncryptionSettingResetEmailBinding
import com.czur.starry.device.file.view.encryption.setting.common.EmailProcessStatus
import com.czur.starry.device.file.view.encryption.setting.vm.ResetEmailViewModel

/**
 * Created by 陈丰尧 on 2024/12/12
 * 修改密保邮箱页面
 */
class EncryptionSettingResetEmailFragment :
    AbsPwdSettingFragment<FragmentEncryptionSettingResetEmailBinding>() {
    private val resetEmailViewModel: ResetEmailViewModel by viewModels()

    override fun FragmentEncryptionSettingResetEmailBinding.initBindingViews() {
        verificationCodeOriginalEt.doAfterTextChanged {
            resetEmailViewModel.onUserInputVerificationCodeOriginalChanged(it.toString())
        }
        verificationCodeNewEt.doAfterTextChanged {
            resetEmailViewModel.onUserInputVerificationCodeNewChanged(it.toString())
        }
        emailNewEt.doAfterTextChanged {
            resetEmailViewModel.onUserInputEmailNewChanged(it.toString())
        }

        // 获取验证码
        getCodeOriginalTv.setOnClickListener {
            launch {
                val email = settingsViewModel.getEncryptionEmail()
                resetEmailViewModel.sendEmailOriginal(email).onFailure {
                    when (it) {
                        is MiaoManyMailRequestExp -> {
                            toast(R.string.toast_encryption_many_mail_request)
                        }
                        is MiaoHttpException -> {
                            toast(R.string.toast_no_network_operation_failure)
                        }
                    }
                }
            }
        }

        // 新邮箱获取验证码
        getCodeNewTv.setOnClickListener {
            launch {
                resetEmailViewModel.sendEmailNew(emailNewEt.text.toString()).onFailure {
                    when (it) {
                        is VerifyException -> {
                            toast(R.string.toast_illegal_email_new)
                        }

                        is MiaoManyMailRequestExp -> {
                            toast(R.string.toast_encryption_many_mail_request)
                        }

                        is MiaoHttpException -> {
                            toast(R.string.toast_no_network_operation_failure)
                        }
                    }
                }
            }
        }

        finishBtn.setOnDebounceClickListener {
            launch {
                resetEmailViewModel.verificationEncryptionEmail().onSuccess {
                    resetEmailViewModel.clearVerifyData()
                    EncryptionSettingResetEmailFragmentDirections
                        .actionResetEmailFragmentToResetPwdFragment(resetEmailViewModel.emailNew)
                        .nav()
                    // 清空输入
                    emailNewEt.clearContentText()
                    verificationCodeOriginalEt.clearContentText()
                    verificationCodeNewEt.clearContentText()
                }.onFailure {
                    it.message?.let { msg ->
                        toast(msg)
                    }
                }
            }
        }
    }

    override fun initData(savedInstanceState: Bundle?) {
        super.initData(savedInstanceState)

        repeatCollectOnResume(settingsViewModel.showEmailFlow){
            // 原邮箱
            binding.emailTv.text = it
        }

        // 完成按钮可见性
        repeatCollectOnResume(resetEmailViewModel.finishEnableFlow) {
            binding.finishBtn.isEnabled = it
        }

        repeatCollectOnResume(resetEmailViewModel.processStatusOriginalFlow) {
            binding.getCodeOriginalTv.gone(it != EmailProcessStatus.IDLE)
            binding.progressBarOriginal.gone(it != EmailProcessStatus.SENDING_EMAIL)
            binding.countdownOriginalTv.gone(it != EmailProcessStatus.COLD_DOWN)
        }

        repeatCollectOnResume(resetEmailViewModel.processStatusNewFlow) {
            binding.getCodeNewTv.gone(it != EmailProcessStatus.IDLE)
            binding.progressBarNew.gone(it != EmailProcessStatus.SENDING_EMAIL)
            binding.countdownNewTv.gone(it != EmailProcessStatus.COLD_DOWN)
        }

        // 倒计时
        repeatCollectOnResume(resetEmailViewModel.emailColdDownTimeStrFlowOriginal) {
            binding.countdownOriginalTv.text = it
        }
        repeatCollectOnResume(resetEmailViewModel.emailColdDownTimeStrFlowNew) {
            binding.countdownNewTv.text = it
        }
    }
}