package com.czur.starry.device.file.server.download

import com.czur.czurutils.log.logTagW
import com.czur.starry.device.file.server.common.FileShareConstant
import com.czur.starry.device.file.server.msg.ResultMessage
import com.czur.starry.device.file.server.msg.ServerHandler
import com.czur.starry.device.file.server.util.ResponseUtil
import io.netty.channel.ChannelHandlerContext
import io.netty.channel.ChannelInboundHandlerAdapter
import io.netty.handler.codec.http.HttpRequest

/**
 *  author : WangHao
 *  time   :2024/02/21
 */

private const val TAG = "DownloadHandler"

class NotFoundHandler : ChannelInboundHandlerAdapter() {

    override fun channelRead(ctx: ChannelHandlerContext, msg: Any?) {
        try {
            if (msg is HttpRequest) {
                ResponseUtil.response(
                    ctx,
                    ResultMessage(FileShareConstant.UNKNOWN_ERROR, content = "url error")
                )
            }
        } catch (e: Exception) {
            logTagW(TAG, "channelRead error", tr = e)
            ResponseUtil.response(
                ctx, ResultMessage(content = e.message.toString())
            )
        }
    }

    override fun exceptionCaught(ctx: ChannelHandlerContext?, cause: Throwable?) {
        ctx?.close()
        ServerHandler.semaphore.release()
        ctx?.let {
            ResponseUtil.response(
                it,
                ResultMessage(FileShareConstant.UNKNOWN_ERROR, content = cause.toString())
            )
        }
    }

}