package com.czur.starry.device.file.utils

import com.czur.czurutils.log.logTagE
import com.czur.czurutils.log.logTagW
import com.czur.starry.device.baselib.utils.prop.getBooleanSystemProp
import java.io.File

/**
 * Created by 陈丰尧 on 2021/9/28
 * Rk 在播放视频时做的一些操作
 */
object RkUtil {
    private const val TAG = "RkUtil"

    private val governor_freqFile = File("/sys/devices/system/cpu/cpu0/cpufreq/scaling_governor")
    private val setspeed_freqFile = File("/sys/devices/system/cpu/cpu0/cpufreq/scaling_setspeed")

    const val USERSPACE_MODE = "userspace"
    const val INTERACTIVE_MODE = "interactive"

    val setFixFrequency: Boolean by lazy {
        getBooleanSystemProp("ro.videoplayer.fixfreq", false)
    }
    const val FIX_FREQUENCY_VALUE = 312000


    fun setGovernorMode(mode: String) {
        pmWriteFile(governor_freqFile, mode)
    }

    fun setSpeedFreq(value: Int) {
        pmWriteFile(setspeed_freqFile, value.toString())
    }

    private fun pmWriteFile(file: File, message: String): Boolean {
        if (!file.exists()) {
            logTagW(TAG, "$file 不存在")
            return false
        }
        return if (file.canWrite()) {
            file.writeText(message)
            true
        } else {
            logTagE(TAG, "${file} can not write")
            false
        }
    }
}