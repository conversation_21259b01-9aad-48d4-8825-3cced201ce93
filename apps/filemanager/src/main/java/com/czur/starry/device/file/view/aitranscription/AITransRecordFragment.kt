package com.czur.starry.device.file.view.aitranscription

import android.os.Bundle
import androidx.fragment.app.commit
import androidx.fragment.app.viewModels
import com.czur.czurutils.log.logTagD
import com.czur.starry.device.baselib.base.v2.fragment.CZViewBindingFragment
import com.czur.starry.device.baselib.utils.repeatCollectOnResume
import com.czur.starry.device.file.R
import com.czur.starry.device.file.adapter.TAG
import com.czur.starry.device.file.databinding.FragmentAiTransRecordBinding
import com.czur.starry.device.file.view.common.ErrorNetworkFragment
import com.czur.starry.device.file.view.common.NeedLoginFragment
import com.czur.starry.device.file.view.common.NoNetworkFragment

/**
 * Created by 陈丰尧 on 2025/4/9
 */
private const val FRAG_TAG_LOGIN = "login"
private const val FRAG_TAG_CONTENT = "content"
private const val FRAG_TAG_NO_NETWORK = "no_network"
private const val FRAG_TAG_ERROR_NETWORK = "error_network"

class AITransRecordFragment : CZViewBindingFragment<FragmentAiTransRecordBinding>() {

    private val aiTransRecordViewModel: AITransRecordViewModel by viewModels()

    override fun FragmentAiTransRecordBinding.initBindingViews() {
        childFragmentManager.commit {
            val loginFragment = NeedLoginFragment.getInstance(R.string.str_no_login_info_ai_trans)
            val contentFragment = AITransRecordContentFragment()
            val noNetworkFragment = NoNetworkFragment()
            val errorNetworkFragment = ErrorNetworkFragment()

            // 设置错误网络页面的重试按钮点击事件
            errorNetworkFragment.setOnRetryClickListener(object : ErrorNetworkFragment.OnRetryClickListener {
                override fun onRetryClick() {
                    // 点击重试按钮时，显示AITransRecordContentFragment并刷新列表
                    showContentFragment()
                }
            })

            add(
                R.id.containerView,
                loginFragment,
                FRAG_TAG_LOGIN
            )
            add(
                R.id.containerView,
                contentFragment,
                FRAG_TAG_CONTENT
            )
            add(R.id.containerView, noNetworkFragment, FRAG_TAG_NO_NETWORK)
            add(R.id.containerView, errorNetworkFragment, FRAG_TAG_ERROR_NETWORK)
            hide(loginFragment)
            hide(contentFragment)
            hide(noNetworkFragment)
            hide(errorNetworkFragment)
        }
    }

    // 显示内容页面并刷新列表
    private fun showContentFragment() {
        val contentFragment = childFragmentManager.findFragmentByTag(FRAG_TAG_CONTENT) as? AITransRecordContentFragment ?: return
        val loginFragment = childFragmentManager.findFragmentByTag(FRAG_TAG_LOGIN) ?: return
        val noNetworkFragment = childFragmentManager.findFragmentByTag(FRAG_TAG_NO_NETWORK) ?: return
        val errorNetworkFragment = childFragmentManager.findFragmentByTag(FRAG_TAG_ERROR_NETWORK) ?: return

        childFragmentManager.commit(allowStateLoss = true) {
            hide(noNetworkFragment)
            hide(loginFragment)
            hide(errorNetworkFragment)
            show(contentFragment)
        }
        contentFragment.refresh()
    }

    // 显示错误网络页面
    fun showErrorNetworkFragment() {
        val contentFragment = childFragmentManager.findFragmentByTag(FRAG_TAG_CONTENT) as? AITransRecordContentFragment ?: return
        val loginFragment = childFragmentManager.findFragmentByTag(FRAG_TAG_LOGIN) ?: return
        val noNetworkFragment = childFragmentManager.findFragmentByTag(FRAG_TAG_NO_NETWORK) ?: return
        val errorNetworkFragment = childFragmentManager.findFragmentByTag(FRAG_TAG_ERROR_NETWORK) ?: return

        childFragmentManager.commit(allowStateLoss = true) {
            hide(noNetworkFragment)
            hide(loginFragment)
            hide(contentFragment)
            show(errorNetworkFragment)
        }
        contentFragment.selNone()
    }

    override fun initData(savedInstanceState: Bundle?) {
        super.initData(savedInstanceState)

        repeatCollectOnResume(aiTransRecordViewModel.uiStateFlow) { state ->
            logTagD(TAG, "UIstate:${state}")
            val loginFragment = childFragmentManager.findFragmentByTag(FRAG_TAG_LOGIN)!!
            val contentFragment =
                childFragmentManager.findFragmentByTag(FRAG_TAG_CONTENT) as AITransRecordContentFragment
            val noNetworkFragment = childFragmentManager.findFragmentByTag(FRAG_TAG_NO_NETWORK)!!

            if (state >= (1 shl 1)) {
                // 无网络
                childFragmentManager.commit(allowStateLoss = true) {
                    show(noNetworkFragment)
                    hide(loginFragment)
                    hide(contentFragment)
                }
                contentFragment.selNone()
            } else if (state >= 1) {
                // 未登录
                childFragmentManager.commit(allowStateLoss = true) {
                    show(loginFragment)
                    hide(noNetworkFragment)
                    hide(contentFragment)
                }
                contentFragment.selNone()
            } else {
                // 正常
                childFragmentManager.commit(allowStateLoss = true) {
                    hide(noNetworkFragment)
                    hide(loginFragment)
                    show(contentFragment)
                }
                contentFragment.refresh()
            }
        }

    }
}