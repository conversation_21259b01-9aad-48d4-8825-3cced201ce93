package com.czur.starry.device.file.view.dialog

import android.os.Bundle
import androidx.fragment.app.activityViewModels
import com.czur.starry.device.baselib.base.v2.fragment.floating.CZVBFloatingFragment
import com.czur.starry.device.file.databinding.FloatFileShareQrCodeBinding
import com.czur.starry.device.file.view.czurshare.vm.FileShareViwModel

/**
 * Created by 陈丰尧 on 2021/12/27
 */
class FileShareQRCodeFloat : CZVBFloatingFragment<FloatFileShareQrCodeBinding>() {
    private val shareVM: FileShareViwModel by activityViewModels()

    override fun FloatFileShareQrCodeBinding.initBindingViews() {
        closeBtn.setOnClickListener {
            dismiss()
        }

    }

    override fun initData(savedInstanceState: Bundle?) {
        super.initData(savedInstanceState)
        shareVM.qrCodeImgLive.observe(this) {
            it?.let {
                binding.qrCodeIv.setImageBitmap(it)
            }
        }
    }
}