package com.czur.starry.device.file.server.component.servlet.get

import com.czur.czurutils.log.logTagV
import com.czur.czurutils.log.logTagW
import com.czur.starry.device.file.server.common.FileShareConstant
import com.czur.starry.device.file.server.component.anno.Servlet
import com.czur.starry.device.file.server.component.servlet.HttpServlet
import com.czur.starry.device.file.server.download.DownloadHelper
import com.czur.starry.device.file.server.msg.ResultMessage
import com.czur.starry.device.file.server.util.CZHttpRequest
import com.czur.starry.device.file.server.util.CZHttpResponse
import io.netty.handler.codec.http.HttpHeaderNames
import kotlinx.coroutines.sync.Semaphore
import kotlinx.coroutines.sync.withPermit
import java.io.File

/**
 * Created by 陈丰尧 on 2024/12/17
 */
private const val TAG = "ThumbServlet"
private const val PARAM_KEY_PATH = "path"


private val imgSemaphore = Semaphore(3)
@Servlet(FileShareConstant.GET_THUMB)
class ThumbServlet : HttpServlet() {
    override suspend fun onConnect(request: CZHttpRequest, response: CZHttpResponse) {
        val path = request.getPathParam(PARAM_KEY_PATH) ?: ""
        logTagV(TAG, "ThumbServlet onConnect path=$path")
        if (path.isEmpty()) {
            // 没有参数
            response.content = ResultMessage(FileShareConstant.FILE_NOT_EXIST, content = "file is not exist")
            return
        }

        val file = File(path)
        if (!file.exists() || !file.isFile) {
            // 文件不存在
            logTagW(TAG, "ThumbServlet onConnect file is not exist")
            response.content = ResultMessage(FileShareConstant.FILE_NOT_EXIST, content = "file is not exist")
            return
        }

        val imgByteArray = imgSemaphore.withPermit {
            DownloadHelper.getThumbnail(file)
        }
        if (imgByteArray.isEmpty()) {
            // 获取缩略图失败
            response.content = ResultMessage(FileShareConstant.FILE_NOT_EXIST, content = "file is not exist")
            return
        }
        response.header[HttpHeaderNames.CONTENT_TYPE.toString()] = "image/jpeg"
        response.content = imgByteArray

    }
}