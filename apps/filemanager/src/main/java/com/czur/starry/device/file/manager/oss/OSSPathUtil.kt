package com.czur.starry.device.file.manager.oss

import com.alibaba.sdk.android.oss.OSS
import com.alibaba.sdk.android.oss.model.ListObjectsRequest
import com.czur.czurutils.log.logTagD
import com.czur.czurutils.log.logTagV
import com.czur.czurutils.log.logTagW

import com.czur.starry.device.file.bean.FileEntity
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.isActive
import kotlinx.coroutines.withContext
import java.io.File

/**
 * Created by 陈丰尧 on 2022/3/9
 */
class OSSPathUtil(
    private val fileOss: OSS,
    private val bucketName: String,
) {
    companion object {
        private const val TAG = "OSSPathUtil"
    }

    /**
     * 递归方式获取指定路径下的全部文件/文件夹 path
     */
    suspend fun getAllCloudFilePath(fileEntities: List<FileEntity>): List<String> {
        val resultSet = mutableSetOf<String>()

        return withContext(Dispatchers.IO) {
            run loop@{
                fileEntities.forEach { entity ->
                    if (!isActive) {
                        logTagW(TAG, "协程取消(getAllFilePath)")
                        return@loop
                    }
                    if (entity.isDir()) {
                        val prefix = entity.absPathWithSuffix
                        getObjectList(prefix, resultSet)
                    } else {
                        resultSet.add(entity.absPathWithSuffix)
                    }
                }
            }

            resultSet.toList()
        }
    }

    /**
     * 获取上传后, 在OSS上"预期"的路径
     */
    suspend fun getExpectFilePathByLocal(
        localFiles: List<FileEntity>,
        cloudDir: FileEntity
    ): List<String> {
        if (localFiles.isEmpty()) {
            return emptyList()
        }
        return withContext(Dispatchers.IO) {
            // 云文件的前缀
            val cloudDirPrefix = cloudDir.absPathWithSuffix
            val localFilePrefix = localFiles.first().parentPath

            logTagD(TAG, "cloudDirPrefix:${cloudDirPrefix}")
            logTagD(TAG, "localFilePrefix:${localFilePrefix}")
            val localPaths = mutableSetOf<String>()
            localFiles.forEach {
                getLocalFilePath(File(it.absPath), localPaths)
            }
            localPaths.map {
                val cloudPath = it.replace(localFilePrefix, cloudDirPrefix)
                logTagV(TAG, "path:${cloudPath}")
                cloudPath
            }
        }
    }


    private fun getObjectList(
        prefix: String,
        pathSet: MutableSet<String>
    ) {
        var nextMarker: String? = null
        do {
            val listObjectsRequest = ListObjectsRequest(
                bucketName, prefix, nextMarker, null, null
            )
            val objectListing = fileOss.listObjects(listObjectsRequest)
            if (objectListing.objectSummaries.size > 0) {
                for (s in objectListing.objectSummaries) {
                    logTagD(TAG, "getObjectList:" + s.key)
                    pathSet.add(s.key)
                }
            }
            nextMarker = objectListing.nextMarker
        } while (objectListing.isTruncated)
    }

    private fun getLocalFilePath(file: File, paths: MutableSet<String>) {
        if (!file.isDirectory) {
            paths.add(file.absolutePath)
        } else {
            paths.add(file.absolutePath + "/")
            file.walkTopDown().forEach {
                if (!it.isDirectory) {
                    paths.add(it.absolutePath)
                } else {
                    paths.add(it.absolutePath + "/")
                }
            }
        }
    }
}