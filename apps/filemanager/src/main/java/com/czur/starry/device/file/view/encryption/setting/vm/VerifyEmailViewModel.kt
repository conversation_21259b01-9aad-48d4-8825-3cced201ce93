package com.czur.starry.device.file.view.encryption.setting.vm

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.czur.starry.device.baselib.utils.VerifyException
import com.czur.starry.device.baselib.utils.getString
import com.czur.starry.device.file.R
import com.czur.starry.device.file.manager.LocalEncryptionManager
import com.czur.starry.device.file.view.encryption.setting.common.EmailStateMachine
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.map

/**
 * Created by 陈丰尧 on 2024/12/12
 */
class VerifyEmailViewModel : ViewModel() {
    private val _userInputVerifyCodeFlow = MutableStateFlow("")
    val userInputVerifyCodeFlow = _userInputVerifyCodeFlow.asStateFlow()

    private val emailStateMachine = EmailStateMachine(viewModelScope)
    val emailProcessStatusFlow
        get() = emailStateMachine.processStatusFlow
    val emailColdDownTimeStrFlow
        get() = emailStateMachine.coldDownTimeStrFlow

    /**
     * 下一步按钮的可用性
     */
    val nextStepEnableFlow =
        userInputVerifyCodeFlow.map { it.length == LocalEncryptionManager.EMAIL_VERIFY_CODE_LENGTH }


    /**
     * 用户输入的验证码变化
     */
    fun onUserInputVerifyCodeChanged(verifyCode: String) {
        _userInputVerifyCodeFlow.value = verifyCode
    }

    /**
     * 校验验证码
     * @param email 邮箱
     * @param verificationCode 验证码
     */
    fun checkVerifyCode(email: String, verificationCode: String): Result<Unit> =
        emailStateMachine.checkVerifyCode(email, verificationCode)

    /**
     * 清除校验数据
     */
    fun clearVerifyData() = emailStateMachine.clearVerifyData()

    /**
     * 发送邮件
     */
    suspend fun sendEmail(email: String): Result<Unit> {
        val code = LocalEncryptionManager.createEmailVerificationCode()
        return emailStateMachine.sendEmail(email, code)
    }

    override fun onCleared() {
        super.onCleared()
        emailStateMachine.clear()
    }
}