package com.czur.starry.device.file.view.activity

import android.content.Context
import android.content.Intent
import android.graphics.Color
import android.os.Bundle
import android.view.ContextMenu
import android.view.KeyEvent
import android.view.Menu
import android.view.MenuItem
import android.view.View
import android.webkit.WebResourceError
import android.webkit.WebResourceRequest
import android.webkit.WebView
import android.webkit.WebViewClient
import android.widget.FrameLayout
import androidx.core.view.MenuCompat
import androidx.lifecycle.lifecycleScope
import com.czur.czurutils.log.logTagD
import com.czur.czurutils.log.logTagW
import com.czur.starry.device.baselib.base.CZURAtyManager
import com.czur.starry.device.baselib.base.v2.aty.CZViewBindingAty
import com.czur.starry.device.baselib.utils.WebViewUtil
import com.czur.starry.device.baselib.utils.keyboard.injectKey
import com.czur.starry.device.baselib.utils.launch
import com.czur.starry.device.baselib.view.dialog.LoadingDialog
import com.czur.starry.device.baselib.view.floating.FloatShowMode
import com.czur.starry.device.baselib.view.floating.common.DoubleBtnCommonFloat
import com.czur.starry.device.baselib.view.floating.common.SingleBtnCommonFloat
import com.czur.starry.device.baselib.widget.ScrollWebFrameLayout
import com.czur.starry.device.file.R
import com.czur.starry.device.file.databinding.ActivityWebBinding
import com.czur.starry.device.file.filelib.FileHandlerLive.fileKeyCodeStatus
import com.czur.starry.device.file.filelib.FileHandlerLive.fileKeyCodeStatusLive
import com.czur.starry.device.file.filelib.FileType
import com.czur.starry.device.file.utils.getOffice365URL
import com.just.agentweb.AgentWeb
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch

/**
 * Created by 陈丰尧 on 3/22/21
 */
class WebActivity : CZViewBindingAty<ActivityWebBinding>() {
    lateinit var url: String
    private var fileType: FileType = FileType.OTHER
    private lateinit var mAgentWeb: AgentWeb

    private val loadingDialog by lazy { LoadingDialog() }
    private var officeIntent: Intent? = null

    private var analysisExceptionsDialog: DoubleBtnCommonFloat? = null
    private var isSelectContinue: Boolean = false

    private var onMenuCloseInjectKeyCode: Int? = null

    companion object {
        public const val KEY_PARAM_URL = "url"
        private const val KEY_PARAM_IS_PPT = "isPPT"
        public const val KEY_PARAM_FILE_TYPE = "fileType"
        public const val KEY_PARAM_FILE_PATH = "filePath"
        private const val TAG = "WebActivity"

        /**
         * 启动WebActivity
         * @param url: 要显示的URL
         * @param context: 默认是当前显示的Activity
         */
        fun start(
            url: String,
            fileType: FileType,
            context: Context = CZURAtyManager.currentActivity()
        ) {
            val intent = Intent(context, WebActivity::class.java)
            intent.putExtra(KEY_PARAM_URL, url)
            intent.putExtra(KEY_PARAM_FILE_TYPE, fileType.name)
            logTagD(TAG, "启动WebViewActivity")
            context.startActivity(intent)
        }
    }

    override fun AtyParams.initAtyParams() {
        finishWhenStop = true
    }


    override fun handlePreIntent(preIntent: Intent) {
        super.handlePreIntent(preIntent)
        officeIntent = preIntent
        url = preIntent.getStringExtra(KEY_PARAM_URL) ?: ""
        val fileTypeName = preIntent.getStringExtra(KEY_PARAM_FILE_TYPE) ?: ""
        fileType = try {
            FileType.valueOf(fileTypeName)
        } catch (e: Exception) {
            FileType.OTHER
        }

        logTagD(TAG, "fileType:${fileType}  url:$url")

        WebViewUtil.hookWebView()
    }

    override fun ActivityWebBinding.initBindingViews() {
        mAgentWeb = AgentWeb.with(this@WebActivity)
            .setAgentWebParent(webContainer, FrameLayout.LayoutParams(-1, -1))
            .useDefaultIndicator()
            .setMainFrameErrorView(R.layout.view_web_err, R.id.errorTv)
            .createAgentWeb()
            .get()
        mAgentWeb.webCreator.webView.setBackgroundColor(Color.TRANSPARENT)
        mAgentWeb.webCreator.webView.setWebViewClient(object : WebViewClient() {
            override fun onReceivedError(
                view: WebView?,
                request: WebResourceRequest?,
                error: WebResourceError?
            ) {
                super.onReceivedError(view, request, error)
                showPartAbnormalTipDialog()
            }
        })


        webContainer.hScrollTurnPage = fileType == FileType.PPT
        webContainer.blockScrollMode = when (fileType) {
            FileType.DOCUMENT -> ScrollWebFrameLayout.BlockScrollMode.SCROLL
            else -> ScrollWebFrameLayout.BlockScrollMode.KEY_INJECT
        }

        registerForContextMenu(webContainer)

        webContainer.requestFocus()

        fileKeyCodeStatusLive.observe(this@WebActivity) {
            when (it) {
                KeyEvent.KEYCODE_PAGE_UP -> {
                    launch {
                        delay(100)  // 不delay, 事件会被Menu劫持掉
                        injectKey(getPageUpKeyCode())
                        fileKeyCodeStatus = -1
                    }
                }

                KeyEvent.KEYCODE_PAGE_DOWN -> {
                    launch {
                        delay(100)  // 不delay, 事件会被Menu劫持掉
                        injectKey(getPageDownKeyCode())
                        fileKeyCodeStatus = -1
                    }
                }
            }
        }
    }

    override fun onContextMenuClosed(menu: Menu) {
        super.onContextMenuClosed(menu)
        onMenuCloseInjectKeyCode?.let {
            launch {
                delay(100)  // 不delay, 事件会被Menu劫持掉
                injectKey(it)
                onMenuCloseInjectKeyCode = null
            }
        }
    }


    override fun onCreateContextMenu(
        menu: ContextMenu,
        v: View?,
        menuInfo: ContextMenu.ContextMenuInfo?
    ) {
        menuInflater.inflate(R.menu.menu_document, menu)
        MenuCompat.setGroupDividerEnabled(menu, true)
        super.onCreateContextMenu(menu, v, menuInfo)
    }

    override fun onContextItemSelected(item: MenuItem): Boolean {
        return when (item.itemId) {
            R.id.menuDocumentPageUp -> {
                onMenuCloseInjectKeyCode = getPageUpKeyCode()
                true
            }

            R.id.menuDocumentPageDown -> {
                onMenuCloseInjectKeyCode = getPageDownKeyCode()
                true
            }

            R.id.menuExit -> {
                finish()
                true
            }

            else -> super.onContextItemSelected(item)
        }
    }

    private fun getPageUpKeyCode(): Int {
        return when (fileType) {
            FileType.EXCEL -> KeyEvent.KEYCODE_PAGE_UP
            else -> KeyEvent.KEYCODE_DPAD_LEFT
        }
    }

    private fun getPageDownKeyCode(): Int {
        return when (fileType) {
            FileType.EXCEL -> KeyEvent.KEYCODE_PAGE_DOWN
            else -> KeyEvent.KEYCODE_DPAD_RIGHT
        }
    }

    override fun initData(savedInstanceState: Bundle?) {
        super.initData(savedInstanceState)
        if (url.isBlank()) {
            logTagW(TAG, "url 为空, 获取office365url")
            lifecycleScope.launch {
                loadingDialog.show()
                val localPath = officeIntent?.getStringExtra(KEY_PARAM_FILE_PATH) ?: ""
                val office365URL = getOffice365URL(localPath)
                if (office365URL.isEmpty()) {
                    //dialog
                    showTipDialog()
                } else {
                    url = office365URL
                    logTagD(TAG, "url:${office365URL}")
                    mAgentWeb.urlLoader.loadUrl(url)
                }
                loadingDialog.dismiss()
            }

        } else {
            mAgentWeb.urlLoader.loadUrl(url)
        }


    }

    override fun onDestroy() {
        super.onDestroy()
        mAgentWeb.destroy() // 销毁webview
    }

    fun showTipDialog() {
        lifecycleScope.launch {
            SingleBtnCommonFloat(
                content = getString(R.string.toast_open_file_err)
            ) { commonFloat ->
                commonFloat.dismiss()
                finish()
            }.show()
        }
    }

    /// 当内容解析异常时，若用户选择继续,可继续浏览而非强制关闭页面。
    fun showPartAbnormalTipDialog() {
        lifecycleScope.launch {
            if (!isSelectContinue) {
                analysisExceptionsDialog = DoubleBtnCommonFloat(
                    content = getString(R.string.dialog_analysis_exceptions_content),
                    cancelBtnText = getString(R.string.dialog_analysis_exceptions_btn_close),
                    confirmBtnText = getString(R.string.dialog_analysis_exceptions_btn_continue),
                    showMode = FloatShowMode.SINGLE,
                    onCommonClick = { commonFloat, position ->
                        commonFloat.dismiss()
                        if (position == DoubleBtnCommonFloat.DOUBLE_FLOAT_BTN_CANCEL) {
                            finish()
                        } else {
                            isSelectContinue = true
                        }
                    }
                ).apply {
                    setOnDismissListener {
                        analysisExceptionsDialog = null
                    }
                    show()
                }
            }
        }
    }
}