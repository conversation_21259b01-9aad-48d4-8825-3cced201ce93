package com.czur.starry.device.file.manager.transfer.task

import com.czur.czurutils.log.logTagD
import com.czur.czurutils.log.logTagV
import com.czur.starry.device.baselib.utils.toSizeStr
import com.czur.starry.device.file.bean.FileEntity
import com.czur.starry.device.file.filelib.AccessType
import com.czur.starry.device.file.manager.transfer.del.IDeleter
import com.czur.starry.device.file.manager.transfer.del.LocalDeleter
import com.czur.starry.device.file.manager.transfer.inter.*
import com.czur.starry.device.file.utils.copyTo
import com.czur.starry.device.file.utils.syncFileSystem
import java.io.File

/**
 * Created by 陈丰尧 on 3/23/21
 */
private const val TAG = "LocalTask"

class LocalTask : CopyTask, IComputeSumSize by LocalComputeSumSize(),
    ICheckSpace by LocalCheckSpace(), IDeleter by LocalDeleter() {


    /**
     * 检测是否有同名文件,耗时操作
     */
    override suspend fun checkExist(src: FileEntity, destDir: FileEntity): Boolean {
        val dirFile = File(destDir.absPath)
        val destFile = File(dirFile, src.name) // 复制后的文件
        return destFile.exists()
    }

    /**
     * 执行复制操作
     * @param src       要复制的文件
     * @param destDir   目的地文件夹
     * @param size      src所占用的空间,为了子类计算进度使用
     * @param progressListener  用于更新进度,完成了的百分比 0-1之间
     */
    override suspend fun doCopy(
        src: FileEntity,
        destDir: FileEntity,
        size: Long,
        delOnFinish: Boolean,
        progressListener: (progress: Float) -> Unit,
    ) {
        logTagD(TAG, "本地->本地开始复制:${src.name}->${destDir.name}")

        val dirFile = File(destDir.absPath)
        val destFile = File(dirFile, src.name) // 复制后的文件

        val theFile = File(src.absPath)
        logTagV(TAG, "sumSize${size.toSizeStr()}")
        var finishSize = 0L

        if (delOnFinish && src.belongTo == AccessType.LOCAL
            && destDir.belongTo == AccessType.LOCAL
            && !src.absPath.contains("storage") && !destDir.absPath.contains("storage")
        ) {
            logTagV(TAG, "移动文件, 并且都属于本地文件, 使用rename进行操作")
            theFile.renameTo(destFile)
            progressListener(1F)
        } else {
            logTagV(TAG, "开始复制")
            // 正常复制
            theFile.copyTo(destFile) {
                // 屏蔽掉之前代码中0的情况
                finishSize += it
                // 更新进度信息
                progressListener(finishSize.toFloat() / size)

            }
            if (destDir.belongTo == AccessType.USB) {
                logTagV(TAG, "向U盘写入, 同步文件缓存")
                syncFileSystem()
            }
            if (delOnFinish) {
                logTagV(TAG, "复制完成后删除源文件")
                del(src)
                if (src.belongTo == AccessType.USB) {
                    logTagV(TAG, "删除U盘源文件, 同步文件缓存")
                    syncFileSystem()
                }
            }
        }
        logTagD(TAG, "本地->本地复制完成")
    }

}