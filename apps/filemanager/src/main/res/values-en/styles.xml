<resources>
    <style name="tv_record_qrcode_guide">
        <item name="android:layout_marginTop">10px</item>
        <item name="android:layout_width">wrap_content</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:layout_gravity">center</item>
        <item name="android:text">@string/share_scan_text</item>
        <item name="android:textColor">@color/white</item>
        <item name="android:gravity">center</item>
        <item name="android:textSize">30px</item>
    </style>

    <style name="tv_music_speed">
        <item name="android:layout_width">160px</item>
        <item name="android:layout_height">40px</item>
        <item name="android:layout_centerVertical">true</item>
        <item name="android:background">@drawable/bg_music_speed</item>
        <item name="android:gravity">center</item>
        <item name="android:text">@string/music_speed_1</item>
        <item name="android:textColor">@color/white</item>
        <item name="android:textSize">23px</item>
    </style>

    <style name="style_speed_pop_text">
        <item name="android:layout_width">130px</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:textSize">23px</item>
        <item name="android:textColor">@color/colorMusicSpeed</item>
    </style>

    <style name="local_info_title_tv">
        <item name="android:layout_width">wrap_content</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:layout_marginBottom">15px</item>
        <item name="android:gravity">center_vertical</item>
        <item name="android:textSize">18px</item>
        <item name="android:textColor">@color/white</item>
        <item name="android:textStyle">bold</item>
    </style>
</resources>
