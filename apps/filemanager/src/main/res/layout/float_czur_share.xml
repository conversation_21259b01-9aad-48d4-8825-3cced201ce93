<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/floatCzurShare"
    android:layout_width="1777px"
    android:layout_height="1000px"
    tools:ignore="PxUsage,MissingPrefix,RtlHardcoded,ContentDescription">

    <androidx.constraintlayout.utils.widget.ImageFilterView
        android:id="@+id/bg_imageView"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:scaleType="fitXY"
        android:src="@drawable/share_bg"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:round="10px" />

    <View
        android:id="@+id/bgTopView"
        android:layout_width="match_parent"
        android:layout_height="100px"
        app:bl_corners_topLeftRadius="10px"
        app:bl_corners_topRightRadius="10px"
        app:bl_solid_color="#1A000000"
        app:layout_constraintTop_toTopOf="parent"
        tools:background="#1A000000" />

    <TextView
        android:id="@+id/shareTitleTv"
        android:layout_width="wrap_content"
        android:layout_height="100px"
        android:layout_marginLeft="30px"
        android:gravity="center_vertical"
        android:text="@string/czur_share_file_title"
        android:textColor="@color/white"
        android:textSize="48px"
        android:textStyle="bold"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <ImageView
        android:id="@+id/closeFloatIv"
        style="@style/icon_copy_dialog"
        android:layout_marginRight="30px"
        android:src="@drawable/file_icon_close"
        app:layout_constraintBottom_toBottomOf="@+id/shareTitleTv"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="@+id/shareTitleTv" />

    <!--  二维码  -->
    <androidx.constraintlayout.utils.widget.ImageFilterView
        android:id="@+id/shareAppEncodeIv"
        android:layout_width="260px"
        android:layout_height="260px"
        android:layout_marginBottom="15px"
        android:background="@color/white"
        android:padding="10px"
        app:layout_constraintBottom_toTopOf="@+id/shareAppEncodeTv"
        app:layout_constraintLeft_toLeftOf="@+id/shareAppEncodeTv"
        app:layout_constraintRight_toRightOf="@+id/shareAppEncodeTv"
        app:round="10px" />

    <TextView
        android:id="@+id/shareAppEncodeTv"
        style="@style/bottom_text_style"
        android:layout_marginLeft="40px"
        android:gravity="center_horizontal"
        android:lineSpacingExtra="10px"
        android:lineSpacingMultiplier="1"
        android:text="@string/czur_share_encode_ps"
        android:textColor="@color/white"
        android:textSize="22px"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent" />

    <!--  中间名称  -->
    <TextView
        android:id="@+id/deviceNameLabelTv"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="240px"
        android:text="@string/str_device_name"
        android:textColor="@color/white"
        android:textSize="30px"
        android:textStyle="bold"
        app:layout_constraintHorizontal_chainStyle="packed"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <androidx.constraintlayout.utils.widget.ImageFilterView
        android:id="@+id/deviceNameEditIv"
        android:layout_width="40px"
        android:layout_height="40px"
        android:layout_marginLeft="17px"
        android:background="@color/white"
        android:scaleType="center"
        android:src="@drawable/ic_device_name_edit"
        app:layout_constraintBottom_toBottomOf="@id/deviceNameLabelTv"
        app:layout_constraintLeft_toRightOf="@id/deviceNameLabelTv"
        app:layout_constraintTop_toTopOf="@id/deviceNameLabelTv"
        app:roundPercent="1" />

    <TextView
        android:id="@+id/deviceNamePsTv"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="10px"
        android:gravity="center"
        android:text="@string/str_device_name_ps"
        android:textColor="@color/white"
        android:textSize="20px"
        app:layout_constraintLeft_toLeftOf="@+id/deviceNameLabelTv"
        app:layout_constraintRight_toRightOf="@+id/deviceNameLabelTv"
        app:layout_constraintTop_toBottomOf="@+id/deviceNameLabelTv" />

    <TextView
        android:id="@+id/deviceNameTv"
        style="@style/device_name_tv_style"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginLeft="15px"
        android:layout_marginTop="15px"
        android:includeFontPadding="false"
        android:maxWidth="800px"
        android:singleLine="true"
        android:textColor="@color/white"
        android:textSize="60px"
        android:textStyle="bold"
        app:layout_constraintLeft_toLeftOf="@id/deviceNamePsTv"
        app:layout_constraintRight_toRightOf="@id/deviceNamePsTv"
        app:layout_constraintTop_toBottomOf="@id/deviceNamePsTv"
        tools:text="FELIX" />

    <EditText
        android:id="@+id/deviceNameEt"
        android:layout_width="450px"
        android:layout_height="80px"
        android:gravity="center"
        android:imeOptions="actionDone"
        android:includeFontPadding="false"
        android:inputType="textNoSuggestions"
        android:nextFocusDown="@id/deviceNameEt"
        android:scrollHorizontally="true"
        android:singleLine="true"
        android:textColor="@color/white"
        android:textSize="36px"
        android:textStyle="bold"
        app:bl_corners_radius="10px"
        app:bl_solid_color="#4C000000"
        app:layout_constraintBottom_toBottomOf="@+id/deviceNameTv"
        app:layout_constraintLeft_toLeftOf="@id/deviceNamePsTv"
        app:layout_constraintRight_toRightOf="@id/deviceNamePsTv"
        app:layout_constraintTop_toTopOf="@id/deviceNameTv"
        tools:background="#3317283F" />

    <androidx.constraintlayout.utils.widget.ImageFilterView
        android:id="@+id/editNameConfirmIv"
        android:layout_width="60px"
        android:layout_height="60px"
        android:layout_marginLeft="30px"
        android:background="#33FFFFFF"
        android:scaleType="center"
        android:src="@drawable/ic_edit_confirm_file"
        app:layout_constraintBottom_toBottomOf="@id/deviceNameEt"
        app:layout_constraintLeft_toRightOf="@id/deviceNameEt"
        app:layout_constraintTop_toTopOf="@id/deviceNameEt"
        app:roundPercent="1" />

    <androidx.constraintlayout.utils.widget.ImageFilterView
        android:id="@+id/editNameCancelIv"
        android:layout_width="60px"
        android:layout_height="60px"
        android:layout_marginLeft="30px"
        android:background="#33FFFFFF"
        android:scaleType="center"
        android:src="@drawable/ic_edit_cancel_file"
        app:layout_constraintLeft_toRightOf="@id/editNameConfirmIv"
        app:layout_constraintTop_toTopOf="@id/editNameConfirmIv"
        app:roundPercent="1" />

    <androidx.constraintlayout.widget.Group
        android:id="@+id/editGroup"
        android:layout_width="0px"
        android:layout_height="0px"
        app:constraint_referenced_ids="deviceNameEt,editNameConfirmIv,editNameCancelIv" />
    <!--  传输校验码  -->
    <TextView
        android:id="@+id/deviceEncodeLabelTv"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="420px"
        android:text="@string/str_device_encode"
        android:textColor="@color/white"
        android:textSize="32px"
        android:textStyle="bold"
        app:layout_constraintHorizontal_chainStyle="packed"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@id/bgTopView" />

    <androidx.constraintlayout.utils.widget.ImageFilterView
        android:id="@+id/deviceEncodeEditIv"
        android:layout_width="40px"
        android:layout_height="40px"
        android:layout_marginLeft="17px"
        android:background="@color/white"
        android:scaleType="center"
        android:src="@drawable/ic_device_name_edit"
        app:layout_constraintBottom_toBottomOf="@id/deviceEncodeLabelTv"
        app:layout_constraintLeft_toRightOf="@id/deviceEncodeLabelTv"
        app:layout_constraintTop_toTopOf="@id/deviceEncodeLabelTv"
        app:roundPercent="1" />

    <TextView
        android:id="@+id/device_code_tv"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="15px"
        android:includeFontPadding="false"
        android:textColor="@color/white"
        android:textSize="100px"
        android:textStyle="bold"
        app:layout_constraintLeft_toLeftOf="@id/deviceEncodeLabelTv"
        app:layout_constraintRight_toRightOf="@id/deviceEncodeLabelTv"
        app:layout_constraintTop_toBottomOf="@id/deviceEncodeLabelTv" />

    <androidx.constraintlayout.utils.widget.ImageFilterView
        android:id="@+id/deviceEncodeRefreshIv"
        android:layout_width="40px"
        android:layout_height="40px"
        android:layout_marginLeft="17px"
        android:background="@color/white"
        android:padding="1px"
        android:scaleType="center"
        android:src="@drawable/ic_device_name_refresh"
        app:layout_constraintBottom_toBottomOf="@id/device_code_tv"
        app:layout_constraintLeft_toRightOf="@id/device_code_tv"
        app:layout_constraintTop_toTopOf="@id/device_code_tv"
        app:roundPercent="1" />

    <EditText
        android:id="@+id/deviceCodeEt"
        android:layout_width="450px"
        android:layout_height="80px"
        android:gravity="center"
        android:imeOptions="actionDone"
        android:includeFontPadding="false"
        android:inputType="number"
        android:nextFocusDown="@id/deviceCodeEt"
        android:scrollHorizontally="true"
        android:singleLine="true"
        android:textColor="@color/white"
        android:textSize="36px"
        android:textStyle="bold"
        app:bl_corners_radius="10px"
        app:bl_solid_color="#4C000000"
        app:layout_constraintLeft_toLeftOf="@id/deviceEncodeLabelTv"
        app:layout_constraintRight_toRightOf="@id/deviceEncodeLabelTv"
        app:layout_constraintTop_toTopOf="@+id/device_code_tv"
        tools:background="#3317283F" />

    <androidx.constraintlayout.utils.widget.ImageFilterView
        android:id="@+id/edit_code_confirm_iv"
        android:layout_width="60px"
        android:layout_height="60px"
        android:layout_marginLeft="30px"
        android:background="#33FFFFFF"
        android:scaleType="center"
        android:src="@drawable/ic_edit_confirm_file"
        app:layout_constraintBottom_toBottomOf="@id/deviceCodeEt"
        app:layout_constraintLeft_toRightOf="@id/deviceCodeEt"
        app:layout_constraintTop_toTopOf="@id/deviceCodeEt"
        app:roundPercent="1" />

    <androidx.constraintlayout.utils.widget.ImageFilterView
        android:id="@+id/edit_code_cancel_iv"
        android:layout_width="60px"
        android:layout_height="60px"
        android:layout_marginLeft="30px"
        android:background="#33FFFFFF"
        android:scaleType="center"
        android:src="@drawable/ic_edit_cancel_file"
        app:layout_constraintLeft_toRightOf="@id/edit_code_confirm_iv"
        app:layout_constraintTop_toTopOf="@id/edit_code_confirm_iv"
        app:roundPercent="1" />

    <androidx.constraintlayout.widget.Group
        android:id="@+id/edit_code_group"
        android:layout_width="0px"
        android:layout_height="0px"
        app:constraint_referenced_ids="deviceCodeEt,edit_code_confirm_iv,edit_code_cancel_iv" />


    <androidx.constraintlayout.widget.Group
        android:id="@+id/enCodeGroup"
        android:layout_width="0px"
        android:layout_height="0px"
        app:constraint_referenced_ids="deviceEncodeLabelTv,device_code_tv,deviceEncodeEditIv,deviceEncodeRefreshIv" />

    <!--  右上角开关  -->
    <TextView
        android:id="@+id/enableShareTv"
        style="@style/style_czur_share_switch_text"
        android:text="@string/str_device_enable_share"
        app:layout_constraintBottom_toBottomOf="@+id/enableShareSwitch"
        app:layout_constraintEnd_toStartOf="@+id/enableShareSwitch"
        app:layout_constraintTop_toTopOf="@+id/enableShareSwitch" />

    <com.czur.starry.device.baselib.widget.toggle.SwitchIcon
        android:id="@+id/enableShareSwitch"
        style="@style/style_czur_share_switch"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/bgTopView" />

    <TextView
        android:id="@+id/enableCodeTv"
        style="@style/style_czur_share_switch_text"
        android:text="@string/str_device_encode"
        app:layout_constraintBottom_toBottomOf="@+id/enableCodeSwitch"
        app:layout_constraintLeft_toLeftOf="@+id/enableShareTv"
        app:layout_constraintTop_toTopOf="@+id/enableCodeSwitch" />

    <com.czur.starry.device.baselib.widget.toggle.SwitchIcon
        android:id="@+id/enableCodeSwitch"
        style="@style/style_czur_share_switch"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/enableShareSwitch" />

    <TextView
        android:id="@+id/localMeetingTitleTv"
        style="@style/style_czur_share_delete_text"
        android:layout_width="320px"
        android:text="@string/str_device_meeting_record"
        app:layout_constraintBottom_toBottomOf="@+id/enableMeetingSwitch"
        app:layout_constraintLeft_toLeftOf="@+id/enableShareTv"
        app:layout_constraintTop_toTopOf="@+id/enableMeetingSwitch" />

    <com.czur.starry.device.baselib.widget.toggle.SwitchIcon
        android:id="@+id/enableMeetingSwitch"
        style="@style/style_czur_share_switch"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/enableCodeSwitch" />

    <TextView
        android:id="@+id/timeSortTitleTv"
        style="@style/style_czur_share_delete_text"
        android:layout_width="240px"
        android:text="@string/str_delete_time_auto"
        app:layout_constraintLeft_toLeftOf="@+id/enableShareTv"
        app:layout_constraintTop_toTopOf="@+id/timeSortTv" />

    <TextView
        android:id="@+id/timeSortTv"
        style="@style/style_czur_share_sort_text"
        android:layout_width="150px"
        android:layout_marginTop="40px"
        android:layout_marginRight="56px"
        android:ellipsize="end"
        android:gravity="end"
        android:text="@string/str_delete_time_item2"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/localMeetingTitleTv" />

    <ImageView
        android:id="@+id/timeSortIv"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginRight="40px"
        android:src="@drawable/file_time_sort_off"
        app:layout_constraintBottom_toBottomOf="@+id/timeSortTv"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="@+id/timeSortTv" />

    <androidx.constraintlayout.helper.widget.Layer
        android:id="@+id/timeCodeGroup"
        android:layout_width="0px"
        android:layout_height="0px"
        app:constraint_referenced_ids="timeSortTv,timeSortIv"
        tools:ignore="MissingConstraints" />

    <androidx.constraintlayout.utils.widget.ImageFilterView
        android:id="@+id/deviceCountHintIv"
        android:layout_width="30px"
        android:layout_height="30px"
        android:layout_marginTop="2px"
        android:layout_marginEnd="20px"
        android:background="@color/white"
        android:scaleType="center"
        android:src="@drawable/ic_device_share_hint"
        app:layout_constraintRight_toLeftOf="@+id/shareHintTv"
        app:layout_constraintTop_toTopOf="@+id/shareHintTv"
        app:roundPercent="1" />

    <TextView
        android:id="@+id/shareHintTv"
        style="@style/bottom_text_style"
        android:layout_marginEnd="40px"
        android:lineSpacingExtra="10px"
        android:lineSpacingMultiplier="1"
        android:text="@string/str_file_share_hint2"
        android:textColor="@color/white"
        android:textSize="22px"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintRight_toRightOf="parent" />
</androidx.constraintlayout.widget.ConstraintLayout>