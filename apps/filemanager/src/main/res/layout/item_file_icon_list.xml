<?xml version="1.0" encoding="utf-8"?>
<merge xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <ImageView
        android:id="@+id/fileItemSelectIv"
        android:layout_width="80px"
        android:layout_height="80px"
        android:layout_marginLeft="30px"
        android:src="@drawable/file_select"
        android:visibility="invisible"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <com.czur.starry.device.file.widget.IconImageView
        android:id="@+id/fileItemIconIv"
        android:layout_width="80px"
        android:layout_height="130px"
        android:paddingVertical="25px"
        android:scaleType="fitCenter"
        app:layout_constraintBottom_toBottomOf="@id/fileItemSelectIv"
        app:layout_constraintEnd_toEndOf="@+id/fileItemSelectIv"
        app:layout_constraintStart_toStartOf="@+id/fileItemSelectIv"
        app:layout_constraintTop_toTopOf="@+id/fileItemSelectIv"
        app:round="4.5px" />

    <androidx.constraintlayout.utils.widget.ImageFilterView
        android:id="@+id/fileItemUploadIv"
        android:visibility="gone"
        android:layout_width="80px"
        android:layout_height="130px"
        android:paddingVertical="25px"
        android:scaleType="fitCenter"
        app:layout_constraintBottom_toBottomOf="@id/fileItemSelectIv"
        app:layout_constraintEnd_toEndOf="@+id/fileItemSelectIv"
        app:layout_constraintStart_toStartOf="@+id/fileItemSelectIv"
        app:layout_constraintTop_toTopOf="@+id/fileItemSelectIv"
        app:round="4.5px"/>
    <TextView
        android:id="@+id/fileItemNameTv"
        android:layout_width="0px"
        android:layout_height="0px"
        android:layout_marginHorizontal="30px"
        android:clickable="false"
        android:ellipsize="middle"
        android:focusable="false"
        android:gravity="center_vertical"
        android:includeFontPadding="false"
        android:paddingTop="1px"
        android:singleLine="true"
        android:textColor="@color/text_common"
        android:textSize="30px"
        app:float_tips="@string/float_tip_empty"
        app:layout_constraintBottom_toBottomOf="@id/fileItemSelectIv"
        app:layout_constraintLeft_toRightOf="@id/fileItemSelectIv"
        app:layout_constraintRight_toLeftOf="@id/fileItemListCheck"
        app:layout_constraintTop_toTopOf="@id/fileItemSelectIv"
        tools:text="规范.ppt" />

    <com.czur.uilib.choose.CZCheckBox
        android:id="@+id/fileItemListCheck"
        style="@style/style_select_mode_checkbox"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent" />
</merge>