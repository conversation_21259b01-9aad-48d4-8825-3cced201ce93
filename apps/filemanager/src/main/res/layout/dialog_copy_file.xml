<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <LinearLayout
        android:layout_width="1280px"
        android:layout_height="900px"
        android:layout_gravity="center"
        android:background="@drawable/bg_dialog_input"
        android:orientation="vertical">

        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="100px"
            android:background="@drawable/bg_move_dialog_title_bar"
            android:paddingLeft="30px"
            android:paddingRight="30px">

            <TextView
                android:id="@+id/copyDialogTitle"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_toLeftOf="@+id/copyDialogSortIv"
                android:layout_toRightOf="@+id/copyDialogBackIv"
                android:layout_centerVertical="true"
                android:gravity="center"
                android:textColor="@color/white"
                android:textSize="30px"
                tools:text="同步云文件"
                android:textStyle="bold"/>

            <ImageView
                android:id="@+id/copyDialogBackIv"
                style="@style/icon_copy_dialog"
                android:src="@drawable/baselib_icon_back"
                android:visibility="gone" />

            <ImageView
                android:id="@+id/copyDialogCloseIv"
                android:layout_alignParentRight="true"
                android:src="@drawable/file_icon_close"
                style="@style/icon_copy_dialog" />

            <ImageView
                android:id="@+id/copyDialogSortIv"
                style="@style/icon_copy_dialog"
                android:src="@drawable/file_icon_copy_sort"
                android:layout_toLeftOf="@id/copyDialogCloseIv"
                android:layout_marginRight="30px"
                android:visibility="gone"/>
        </RelativeLayout>

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/dialogCopyRv"
            android:layout_width="match_parent"
            android:layout_height="0px"
            android:layout_marginTop="18px"
            android:layout_weight="1" />

        <LinearLayout
            android:id="@+id/dialogCopyBtnBar"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_horizontal"
            android:layout_marginBottom="40px"
            android:orientation="horizontal"
            android:visibility="gone">

            <com.czur.starry.device.baselib.widget.CommonButton
                android:id="@+id/dialogCancelBtn"
                style="@style/style_dialog_file_trans_btn"
                android:layout_marginRight="30px"
                android:text="@string/dialog_cancel"
                app:baselib_theme="dark" />

            <com.czur.starry.device.baselib.widget.CommonButton
                android:id="@+id/dialogConfirmBtn"
                style="@style/style_dialog_file_trans_btn"
                android:text="@string/str_dialog_copy"
                app:baselib_theme="white2" />
        </LinearLayout>

    </LinearLayout>
</FrameLayout>