package com.czur.starry.device.file;

import android.content.Context;
import android.webkit.MimeTypeMap;

import androidx.test.platform.app.InstrumentationRegistry;

import org.junit.Before;
import org.junit.Test;

import java.io.File;

/**
 * Created by 陈丰尧 on 1/7/21
 */
public class MusicFileTypeTest {
    private File rootDir;
    @Before
    public void init(){
        // 根路径选择报名/file
        Context targetContext = InstrumentationRegistry.getInstrumentation().getTargetContext();
        File filesDir = targetContext.getFilesDir();
        rootDir = new File(filesDir,"LocalFileRoot");
    }

    @Test
    public void test(){
        File musicFile = new File(rootDir,"吴静-女儿情.mp3");
        MimeTypeMap mime = MimeTypeMap.getSingleton();
        String mp3 = mime.getMimeTypeFromExtension("mp3");
    }
}
