<resources xmlns:tools="http://schemas.android.com/tools">
    <string name="app_name">Configuración de inicio</string>
    <string name="welcome">Bienvenida</string>
    <string name="str_startup_finish_hint">Configuración del sistema en curso, no lo apague.</string>
    <string name="title_touch_pad_guide">Arrastre para completar la configuración</string>
    <string name="str_touch_pad_guide_hint">Arrastre el objetivo: pulse dos veces y deslice el dedo para arrastrar el objetivo (no suelte el dedo después de pulsar dos veces).</string>
    <string name="str_touch_pad_guide_hint_v2">Arrastre el objetivo: tabule el objetivo y deslice para arrastrarlo/moverlo (no lo suelte después de tabularlo).</string>
    <string name="str_touch_pad_guide_slide_hint">Arrastre hacia el \nlado derecho.</string>
    <string name="title_connect_touch_pad">Conecte el TouchBoard.</string>
    <string name="connect_touch_pad_hint">1. Pulse el botón de encendido para encenderlo. \n2. Mantenga pulsado el botón [TouchControl] en la esquina superior izquierda durante 3 segundos. \n     Después de que la luz azul parpadee, vuelva a colocar el TouchBoard en la base de carga. \n3. Espere hasta que la pantalla muestre «TouchBoard conectado».</string>
    <string name="startup_next_step">Siguiente</string>
    <string name="connect_touch_pad_hint_mainland">1. Gire el botón de encendido/apagado del TouchBoard (en la parte posterior) al estado ON. \n2. Mantenga pulsado el botón [TouchControl] en la parte superior izquierda durante cinco segundos; cuando la luz azul parpadee, vuelva a colocar el TouchBoard en posición de carga. \n3. Espere hasta que aparezca una ventana emergente en la pantalla de proyección del StarryHub con el mensaje \"Se ha emparejado.\".</string>
</resources>
