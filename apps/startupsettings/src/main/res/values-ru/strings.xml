<resources xmlns:tools="http://schemas.android.com/tools">
    <string name="app_name">Настройки запуска</string>
    <string name="welcome">Добро пожаловать</string>
    <string name="str_startup_finish_hint">Выполняется настройка системы. Не выключайте.</string>
    <string name="title_touch_pad_guide">Перетащите, чтобы завершить настройку</string>
    <string name="str_touch_pad_guide_hint">Перетащите цель: Дважды щелкните, затем проведите пальцем, чтобы перетащить цель. (Не отпускайте палец после двойного щелчка.)</string>
    <string name="str_touch_pad_guide_hint_v2">Перетащите цель: Нажмите на цель, затем сдвиньте ее, чтобы перетащить/переместить. (Не отпускайте после нажатия на цель.)</string>
    <string name="str_touch_pad_guide_slide_hint">Перетащите в \nправую сторону.</string>
    <string name="title_connect_touch_pad">Подключите TouchBoard.</string>
    <string name="connect_touch_pad_hint">1. Переведите кнопку питания в состояние ВКЛ. \n2. Нажмите и удерживайте кнопку [TouchControl] в левом верхнем углу в течение 3 секунд.\nпосле того, как мигнет синий индикатор, поместите TouchBoard обратно в док-станцию для зарядки. \n3. Подождите, пока на экране не появится надпись «TouchBoard подключена!».</string>
    <string name="startup_next_step">Далее</string>
    <string name="connect_touch_pad_hint_mainland">1. Переведите кнопку питания TouchBoard (на задней стороне) в положение ВКЛ. \n2. Нажмите и удерживайте кнопку [TouchControl] в верхнем левом углу в течение 5 секунд, когда синий индикатор начнет мигать, верните TouchBoard в положение зарядки. \n3. Подождите, пока на проекционном экране StarryHub не появится всплывающее окно с надписью \'Сопряжение успешно!\’.</string>
</resources>
