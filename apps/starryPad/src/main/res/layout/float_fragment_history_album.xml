<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="1408px"
    android:layout_height="1005px"
    android:elevation="10px"
    app:bl_corners_radius="10px"
    app:bl_solid_color="#F4F4F4"
    tools:background="#F4F4F4"
    tools:ignore="PxUsage,RtlHardcoded">

    <com.google.android.material.imageview.ShapeableImageView
        android:id="@+id/currentShowIv"
        android:layout_width="match_parent"
        android:layout_height="792px"
        app:layout_constraintTop_toTopOf="parent"
        app:strokeColor="@null"
        app:shapeAppearance="@style/HistoryCurrentImg" />

    <ImageView
        android:id="@+id/historyCloseIv"
        android:layout_width="50px"
        android:layout_height="50px"
        android:layout_marginTop="20px"
        android:layout_marginRight="28px"
        android:src="@drawable/ic_history_close"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <ImageView
        android:id="@+id/historyDelIv"
        android:layout_width="50px"
        android:layout_height="50px"
        android:layout_marginRight="30px"
        android:src="@drawable/ic_history_del"
        app:layout_constraintRight_toLeftOf="@id/historyCloseIv"
        app:layout_constraintTop_toTopOf="@id/historyCloseIv" />

    <ImageView
        android:id="@+id/historyShareIv"
        android:layout_width="50px"
        android:layout_height="50px"
        android:layout_marginRight="30px"
        android:src="@drawable/ic_history_share"
        app:layout_constraintRight_toLeftOf="@id/historyDelIv"
        app:layout_constraintTop_toTopOf="@id/historyDelIv" />

    <ImageView
        android:id="@+id/historySaveLocalIv"
        android:layout_width="50px"
        android:layout_height="50px"
        android:layout_marginRight="30px"
        android:src="@drawable/ic_history_save_local"
        app:layout_constraintRight_toLeftOf="@id/historyShareIv"
        app:layout_constraintTop_toTopOf="@id/historyShareIv" />

    <ImageView
        android:id="@+id/historyEditIv"
        android:layout_width="50px"
        android:layout_height="50px"
        android:src="@drawable/ic_history_edit"
        android:layout_marginRight="30px"
        app:layout_constraintBottom_toBottomOf="@id/historyCloseIv"
        app:layout_constraintRight_toLeftOf="@id/historySaveLocalIv"
        app:layout_constraintTop_toTopOf="@id/historyCloseIv"
        />

    <TextView
        android:id="@+id/albumPaintCountTv"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginLeft="25px"
        android:textColor="@color/black"
        android:textSize="28px"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toTopOf="@id/albumPaintListRv"
        tools:text="99+" />

    <TextView
        android:id="@+id/albumTitleTv"
        android:layout_width="67px"
        android:layout_height="wrap_content"
        android:layout_marginTop="2px"
        android:textColor="#ABABAB"
        android:textSize="12px"
        app:layout_constraintLeft_toLeftOf="@id/albumPaintCountTv"
        app:layout_constraintTop_toBottomOf="@id/albumPaintCountTv"
        tools:text="2023 04.25 10:25:22" />

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/albumPaintListRv"
        android:layout_width="match_parent"
        android:layout_height="144px"
        android:layout_marginLeft="168px"
        android:orientation="horizontal"
        app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintTop_toBottomOf="@id/currentShowIv" />


</androidx.constraintlayout.widget.ConstraintLayout>