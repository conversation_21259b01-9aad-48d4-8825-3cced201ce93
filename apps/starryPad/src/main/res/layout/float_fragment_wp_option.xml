<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="650px"
    android:layout_height="wrap_content"
    tools:ignore="PxUsage,RtlHardcoded">

    <com.noober.background.view.BLView
        android:id="@+id/topBarView"
        android:layout_width="match_parent"
        android:layout_height="100px"
        app:bl_corners_topLeftRadius="10px"
        app:bl_corners_topRightRadius="10px"
        app:bl_solid_color="#5879FC"
        app:layout_constraintTop_toTopOf="parent" />

    <com.noober.background.view.BLView
        android:layout_width="match_parent"
        android:layout_height="0px"
        app:bl_corners_bottomLeftRadius="10px"
        app:bl_corners_bottomRightRadius="10px"
        app:bl_solid_color="@color/white"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintTop_toBottomOf="@id/topBarView" />

    <ImageView
        android:id="@+id/closeIv"
        android:layout_width="60px"
        android:layout_height="60px"
        android:layout_marginRight="20px"
        android:src="@drawable/ic_wp_dev_detail_close"
        app:layout_constraintBottom_toBottomOf="@id/topBarView"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginLeft="60px"
        android:includeFontPadding="false"
        android:text="@string/str_opt_wp_dev"
        android:textColor="@color/white"
        android:textSize="48px"
        android:textStyle="bold"
        app:layout_constraintBottom_toBottomOf="@id/topBarView"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toTopOf="@id/topBarView" />


    <TextView
        android:id="@+id/shareWhenExitTitleTv"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginLeft="45px"
        android:paddingVertical="55px"
        android:text="@string/str_share_when_exit"
        android:textColor="#393939"
        android:textSize="30px"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toBottomOf="@id/topBarView" />

    <com.czur.uilib.CZSwitch
        android:id="@+id/shareWhenExitSwitch"
        android:layout_width="90px"
        android:layout_height="40px"
        android:layout_marginLeft="15px"
        app:layout_constraintBottom_toBottomOf="@id/shareWhenExitTitleTv"
        app:layout_constraintLeft_toRightOf="@id/shareWhenExitTitleTv"
        app:layout_constraintTop_toTopOf="@id/shareWhenExitTitleTv" />
</androidx.constraintlayout.widget.ConstraintLayout>