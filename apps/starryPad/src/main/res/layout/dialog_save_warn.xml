<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    tools:ignore="PxUsage"
    android:background="@drawable/bg_global_mark_dialog_corner"
    >

    <RelativeLayout
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_centerInParent="true"
        android:background="@drawable/bg_global_mark_dialog_corner"

        >
        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="500px"
            android:layout_height="360px"
            android:background="@drawable/bg_global_mark_dialog_corner"
            android:paddingVertical="30px"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            tools:ignore="PxUsage"
            android:layout_centerInParent="true">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/dialog_title_del"
                android:textColor="#3D3D3D"
                android:textSize="36px"
                android:textStyle="bold"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <TextView
                android:id="@+id/tv_save"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/dialog_save_palette_content"
                android:textColor="#3D3D3D"
                android:textSize="24px"
                android:textStyle="normal"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <com.czur.starry.device.baselib.widget.CommonButton
                android:id="@+id/noBtn"
                android:layout_width="180px"
                android:layout_height="60px"
                android:layout_marginLeft="55px"
                android:gravity="center"
                android:includeFontPadding="false"
                android:text="@string/dialog_save_btn_no"
                android:textSize="24px"
                android:textStyle="bold"
                app:baselib_theme="grey"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintLeft_toLeftOf="parent"
                />
              <com.czur.starry.device.baselib.widget.CommonButton
                android:id="@+id/yesBtn"
                android:layout_width="180px"
                android:layout_height="60px"
                android:layout_marginRight="55px"
                android:gravity="center"
                android:includeFontPadding="false"
                android:text="@string/dialog_save_btn_yes"
                android:textColor="@color/white"
                android:textSize="24px"
                android:textStyle="bold"
                app:baselib_theme="blue2"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintRight_toRightOf="parent" />

        </androidx.constraintlayout.widget.ConstraintLayout>
    </RelativeLayout>

</RelativeLayout>