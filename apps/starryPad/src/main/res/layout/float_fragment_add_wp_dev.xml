<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="650px"
    android:layout_height="800px"
    tools:ignore="PxUsage,RtlHardcoded">

    <com.noober.background.view.BLView
        android:id="@+id/topBarView"
        android:layout_width="match_parent"
        android:layout_height="100px"
        app:bl_corners_topLeftRadius="10px"
        app:bl_corners_topRightRadius="10px"
        app:bl_solid_color="#5879FC"
        app:layout_constraintTop_toTopOf="parent" />

    <com.noober.background.view.BLView
        android:layout_width="match_parent"
        android:layout_height="0px"
        app:bl_corners_bottomLeftRadius="10px"
        app:bl_corners_bottomRightRadius="10px"
        app:bl_solid_color="@color/white"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintTop_toBottomOf="@id/topBarView" />

    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginLeft="60px"
        android:includeFontPadding="false"
        android:text="@string/str_add_wp_dev"
        android:textColor="@color/white"
        android:textSize="48px"
        android:textStyle="bold"
        app:layout_constraintBottom_toBottomOf="@id/topBarView"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toTopOf="@id/topBarView" />

    <ImageView
        android:id="@+id/closeIv"
        android:layout_width="60px"
        android:layout_height="60px"
        android:layout_marginRight="20px"
        android:src="@drawable/ic_wp_dev_detail_close"
        app:layout_constraintBottom_toBottomOf="@id/topBarView"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/scanConstraintLayout"
        android:layout_width="match_parent"
        android:layout_height="0px"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintTop_toBottomOf="@id/topBarView">

        <ProgressBar
            android:id="@+id/scanningPb"
            android:layout_width="50px"
            android:layout_height="50px"
            android:indeterminateTint="@color/bg_main_blue"
            android:indeterminateTintMode="src_atop"
            app:layout_constraintBottom_toTopOf="@id/scanningTv"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintVertical_chainStyle="packed" />

        <TextView
            android:id="@+id/scanningTv"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="45px"
            android:text="@string/str_wp_dev_scanning"
            android:textColor="@color/bg_main_blue"
            android:textSize="24px"
            android:textStyle="bold"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toBottomOf="@id/scanningPb" />

        <androidx.constraintlayout.widget.Group
            android:id="@+id/scanningGroup"
            android:layout_width="0px"
            android:layout_height="0px"
            android:visibility="visible"
            app:constraint_referenced_ids="scanningPb,scanningTv" />

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/wpDevRv"
            android:layout_width="match_parent"
            android:layout_height="0px"
            android:clipToPadding="false"
            android:paddingTop="55px"
            app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
            app:layout_constraintBottom_toTopOf="@id/addDevConfirmBtn"
            app:layout_constraintTop_toTopOf="parent" />

        <com.czur.uilib.btn.CZButton
            android:id="@+id/addDevConfirmBtn"
            android:layout_width="450px"
            android:layout_height="80px"
            android:layout_marginBottom="80px"
            android:text="@string/btn_add_dev_confirm"
            android:textSize="30px"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent" />

        <androidx.constraintlayout.widget.Group
            android:id="@+id/devListGroup"
            android:layout_width="0px"
            android:layout_height="0px"
            android:visibility="gone"
            app:constraint_referenced_ids="addDevConfirmBtn,wpDevRv" />

        <!--  未发现手写板  -->
        <ImageView
            android:id="@+id/noWpDevIv"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:src="@drawable/ic_no_wp_dev"
            app:layout_constraintBottom_toTopOf="@id/noWpDevTv"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintVertical_chainStyle="packed" />


        <TextView
            android:id="@+id/noWpDevTv"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="55px"
            android:text="@string/str_no_wp_found"
            android:textColor="#F34949"
            android:textSize="24px"
            android:textStyle="bold"
            app:layout_constraintBottom_toTopOf="@id/noWpDevHintTv"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toBottomOf="@id/noWpDevIv" />

        <TextView
            android:id="@+id/noWpDevHintTv"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="20px"
            android:layout_marginBottom="60px"
            android:paddingHorizontal="30px"
            android:text="@string/str_found_wp_hint"
            android:textColor="@color/black"
            android:textSize="24px"
            android:textStyle="bold"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toBottomOf="@id/noWpDevTv" />

        <TextView
            android:id="@+id/noWpDevRePairTv"
            android:layout_width="400px"
            android:layout_height="70px"
            android:layout_marginTop="155px"
            android:layout_marginBottom="60px"
            android:background="@drawable/bg_blue_btn"
            android:gravity="center"
            android:text="@string/btn_write_pad_search_dev"
            android:textColor="@color/white"
            android:textSize="@dimen/no_wp_dev_rePair_tv_size"
            android:textStyle="bold"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toBottomOf="@id/noWpDevHintTv" />

        <androidx.constraintlayout.widget.Group
            android:id="@+id/noWpDevGroup"
            android:layout_width="0px"
            android:layout_height="0px"
            android:visibility="gone"
            app:constraint_referenced_ids="noWpDevIv,noWpDevTv,noWpDevHintTv,noWpDevRePairTv" />
    </androidx.constraintlayout.widget.ConstraintLayout>


    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/connectConstraintLayout"
        android:layout_width="match_parent"
        android:layout_height="0px"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintTop_toBottomOf="@id/topBarView">

        <ProgressBar
            android:id="@+id/connectingPb"
            android:layout_width="50px"
            android:layout_height="50px"
            android:indeterminateTint="@color/bg_main_blue"
            android:indeterminateTintMode="src_atop"
            app:layout_constraintBottom_toTopOf="@id/connectingTv"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintVertical_chainStyle="packed" />

        <TextView
            android:id="@+id/connectingTv"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="45px"
            android:text="@string/str_wp_dev_connecting"
            android:textColor="@color/bg_main_blue"
            android:textSize="24px"
            android:textStyle="bold"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toBottomOf="@id/connectingPb" />
    </androidx.constraintlayout.widget.ConstraintLayout>
</androidx.constraintlayout.widget.ConstraintLayout>