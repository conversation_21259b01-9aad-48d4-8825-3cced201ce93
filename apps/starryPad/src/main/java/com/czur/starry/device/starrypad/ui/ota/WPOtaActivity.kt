package com.czur.starry.device.starrypad.ui.ota

import android.content.Intent
import android.os.Bundle
import androidx.activity.viewModels
import androidx.fragment.app.commit
import androidx.lifecycle.LifecycleOwner
import com.czur.czurutils.log.logTagD
import com.czur.czurutils.log.logTagI
import com.czur.starry.device.baselib.base.v2.aty.CZViewBindingAty
import com.czur.starry.device.baselib.utils.launch
import com.czur.starry.device.baselib.utils.lifecycle.AutoRemoveLifecycleObserver
import com.czur.starry.device.baselib.utils.repeatCollectOnResume
import com.czur.starry.device.starrypad.databinding.ActivityWpOtaBinding

/**
 * Created by 陈丰尧 on 2023/12/19
 */
private const val TAG = "WPOtaActivity"

class WPOtaActivity : CZViewBindingAty<ActivityWpOtaBinding>() {
    private val otaViewModel: WPOtaViewModel by viewModels()

    companion object {
        const val KEY_MD5 = "md5"
        const val KEY_DOWN_URL = "downloadURL"
        const val KEY_DEV_ID = "devID"
        const val KEY_FILE_SIZE = "fileSize"
    }

    override fun handlePreIntent(preIntent: Intent) {
        super.handlePreIntent(preIntent)
        val md5 = preIntent.getStringExtra(KEY_MD5) ?: ""
        val downURL = preIntent.getStringExtra(KEY_DOWN_URL) ?: ""
        val devID = preIntent.getStringExtra(KEY_DEV_ID) ?: ""
        val fileSize = preIntent.getLongExtra(KEY_FILE_SIZE, 1L)
        otaViewModel.initParams(md5, downURL, devID, fileSize)

        logTagD(TAG, "md5:${md5}", "url:${downURL}", "devID:${devID}", "fileSize:${fileSize}")
    }

    override fun AtyParams.initAtyParams() {
        this.keepScreenOn = true
        lifecycleObserver = object : AutoRemoveLifecycleObserver {
            override fun onStop(owner: LifecycleOwner) {
                super.onStop(owner)
                logTagD(TAG, "onStop")
                otaViewModel.transToClientJob?.cancel()
                if (!isFinishing) {
                    finish()
                }
            }
        }
    }

    override fun initData(savedInstanceState: Bundle?) {
        super.initData(savedInstanceState)

        repeatCollectOnResume(otaViewModel.pageEventFlow) {
            logTagI(TAG, "pageEvent:${it}")
            val current = supportFragmentManager.findFragmentByTag(it.toString())
            if (current != null) {
                logTagD(TAG, "不重复替换Fragment")
                return@repeatCollectOnResume
            }
            when (it) {
                WPOtaViewModel.OTAPageEvent.START_DOWNLOAD -> {
                    supportFragmentManager.commit(true) {
                        replace(binding.otaFragContainer.id, DownloadOTAFragment(), it.toString())
                    }
                }

                WPOtaViewModel.OTAPageEvent.START_TRANS -> {
                    supportFragmentManager.commit(true) {
                        replace(binding.otaFragContainer.id, TransOTAFragment(), it.toString())
                    }
                }
            }
        }

        launch {
            val otaFile = otaViewModel.getAvailableOTAFile()
            logTagI(TAG, "localFileAvailable:${otaFile?.name}")
            if (otaFile != null) {
                otaViewModel.changeTransPage(otaFile)
            } else {
                otaViewModel.changeDownloadPage()
            }
        }
    }
}