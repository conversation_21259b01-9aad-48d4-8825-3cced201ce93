package com.czur.starry.device.starrypad.ui.fragment

import android.os.Bundle
import androidx.fragment.app.activityViewModels
import com.czur.czurutils.log.logTagV
import com.czur.starry.device.baselib.base.v2.fragment.CZViewBindingFragment
import com.czur.starry.device.baselib.base.v2.fragment.startActivity
import com.czur.starry.device.baselib.common.Constants
import com.czur.starry.device.baselib.utils.gone
import com.czur.starry.device.baselib.utils.invisible
import com.czur.starry.device.baselib.utils.repeatCollectOnResume
import com.czur.starry.device.baselib.utils.show
import com.czur.starry.device.starrypad.R
import com.czur.starry.device.starrypad.databinding.FragmentTopStatusBarBinding
import com.czur.starry.device.starrypad.devinfo.WPDeviceInfoManager
import com.czur.starry.device.starrypad.ui.settings.SettingsActivity
import com.czur.starry.device.starrypad.vm.TopStatusBarViewModel

/**
 * Created by 陈丰尧 on 2024/3/12
 * 顶部状态栏
 */

private const val TAG = "TopStatusBarFragment"

class TopStatusBarFragment : CZViewBindingFragment<FragmentTopStatusBarBinding>() {
    private val topStatusBarVM: TopStatusBarViewModel by activityViewModels()

    override fun FragmentTopStatusBarBinding.initBindingViews() {
        backIv.invisible(Constants.starryHWInfo.hasTouchScreen)

        backIv.setOnClickListener {
            requireActivity().finish()
        }
        statusAndSettingLayout.setOnClickListener {
            startActivity<SettingsActivity>()
        }
    }

    override fun initData(savedInstanceState: Bundle?) {
        super.initData(savedInstanceState)

        // 升级的小红点
        repeatCollectOnResume(WPDeviceInfoManager.hasNeedUpdateDevFlow) { hasUpdate ->
            binding.newVersionCircleView.gone(!hasUpdate)
        }

        repeatCollectOnResume(topStatusBarVM.hasConnectDevFlow) { hasConnectDev ->
            if (hasConnectDev) {
                logTagV(TAG, "有连接的设备")
                binding.apply {
                    topBarRootLayout.setBackgroundColor(
                        resources.getColor(
                            R.color.bg_color_top_status_bar_conn,
                            null
                        )
                    )

                    useHintTv.setTextColor(
                        resources.getColor(
                            R.color.color_top_status_bar_conn_center_info,
                            null
                        )
                    )
                    useHintIv.setColorFilter(
                        resources.getColor(
                            R.color.color_top_status_bar_conn_center_info,
                            null
                        )
                    )

                    backIv.setColorFilter(
                        resources.getColor(
                            R.color.color_top_status_bar_conn_action,
                            null
                        )
                    )
                    backIv.setBackgroundResource(R.drawable.sel_hover_top_sb_conn_action)
                    statusAndSettingLayout.setBackgroundResource(R.drawable.sel_hover_top_sb_conn_action)

                    connStatusIv.setImageResource(R.drawable.ic_status_conn_has)
                    connStatusTv.setTextColor(
                        resources.getColor(
                            R.color.color_top_status_bar_conn_action,
                            null
                        )
                    )
                    connStatusTv.setText(R.string.str_write_pad_connected)
                    goSettingTv.setTextColor(
                        resources.getColor(
                            R.color.color_top_status_bar_conn_action,
                            null
                        )
                    )
                    goSettingIv.setColorFilter(
                        resources.getColor(
                            R.color.color_top_status_bar_conn_action,
                            null
                        )
                    )
                    connCountTv.show()
                }
            } else {
                logTagV(TAG, "没有连接的设备")
                binding.apply {
                    topBarRootLayout.setBackgroundColor(resources.getColor(R.color.bg_color_top_status_bar_dis_conn))

                    useHintTv.setTextColor(
                        resources.getColor(
                            R.color.color_top_status_bar_dis_conn_center_info,
                            null
                        )
                    )
                    useHintIv.setColorFilter(
                        resources.getColor(
                            R.color.color_top_status_bar_dis_conn_center_info,
                            null
                        )
                    )

                    backIv.setColorFilter(
                        resources.getColor(
                            R.color.color_top_status_bar_dis_conn_action,
                            null
                        )
                    )
                    backIv.setBackgroundResource(R.drawable.sel_hover_top_sb_dis_conn_action)
                    statusAndSettingLayout.setBackgroundResource(R.drawable.sel_hover_top_sb_dis_conn_action)
                    connStatusIv.setImageResource(R.drawable.ic_status_conn_none)

                    connStatusTv.setTextColor(
                        resources.getColor(
                            R.color.color_top_status_bar_dis_conn_action,
                            null
                        )
                    )
                    connStatusTv.setText(R.string.str_write_pad_disconnected)
                    goSettingTv.setTextColor(
                        resources.getColor(
                            R.color.color_top_status_bar_dis_conn_action,
                            null
                        )
                    )
                    goSettingIv.setColorFilter(
                        resources.getColor(
                            R.color.color_top_status_bar_dis_conn_action,
                            null
                        )
                    )
                    connCountTv.invisible()
                }
            }
        }

        repeatCollectOnResume(topStatusBarVM.connectDevCountFlow) { count ->
            binding.connCountTv.text = getString(R.string.str_connect_count, count)
        }
    }
}