package com.czur.starry.device.starrypad.provider

import android.content.Context
import androidx.datastore.core.DataStore
import androidx.datastore.preferences.core.Preferences
import androidx.datastore.preferences.preferencesDataStore
import com.czur.starry.device.baselib.handler.SPContentHandler
import com.czur.starry.device.baselib.handler.SPContentProvider
import com.czur.starry.device.baselib.handler.createDefCorruptionHandler
import com.czur.starry.device.starrypadlib.StarryPadPaintHandler

/**
 * Created by 陈丰尧 on 2024/6/3
 */
class StarryPadPaintProvider : SPContentProvider() {
    override val Context.providerDataStore: DataStore<Preferences> by preferencesDataStore(
        name = "StarryPadPaintProvider",
        corruptionHandler = createDefCorruptionHandler("StarryPadPaintProvider")
    )
    override val targetHandler: SPContentHandler
        get() = StarryPadPaintHandler
}