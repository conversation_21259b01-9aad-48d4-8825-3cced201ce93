package com.czur.starry.device.starrypad.ui.adapter

import android.annotation.SuppressLint
import android.graphics.drawable.InsetDrawable
import android.view.MotionEvent
import android.view.ViewGroup
import androidx.core.view.updatePadding
import com.bumptech.glide.signature.ObjectKey
import com.czur.czurutils.log.logTagD
import com.czur.starry.device.baselib.base.BaseDifferAdapter
import com.czur.starry.device.baselib.base.BaseVH
import com.czur.starry.device.baselib.utils.scaleXY
import com.czur.starry.device.starrypad.R
import com.czur.starry.device.starrypad.db.entity.AlbumWithPaintList
import com.czur.starry.writepadlib.proto.WPTransferData

/**
 * Created by 陈丰尧 on 2022/4/27
 * 全部绘画数据的Adapter
 */
class AllAlbumAdapter : BaseDifferAdapter<AlbumDataVO>() {
    private val selPaints = mutableMapOf<Long, AlbumWithPaintList>()
    private var selMode = false

    // 选中数量改变, 不包括重复的
    var selCountChangeListener: ((selCount: Int) -> Unit)? = null

    fun getSelAlbum() = selPaints.values.map { it.album }
    fun getSelAlbumWithPaintLists() = selPaints.values.toTypedArray()

    @SuppressLint("NotifyDataSetChanged")
    fun updateSelMode(selMode: Boolean) {
        if (this.selMode == selMode) return
        selPaints.clear()
        this.selMode = selMode
        notifyDataSetChanged()
    }

    /**
     * 全选
     */
    @SuppressLint("NotifyDataSetChanged")
    fun selectAll() {
        getDataList().forEach { data ->
            data.albumWithPaintList?.album?.albumId?.let {
                selPaints[it] = data.albumWithPaintList
            }
        }
        selCountChangeListener?.invoke(selPaints.size)
        notifyDataSetChanged()
    }

    /**
     * 全不选
     */
    fun selectNone() {
        selPaints.clear()
        selCountChangeListener?.invoke(0)
        notifyDataSetChanged()
    }

    /**
     * 切换指定Item的选中状态
     */
    fun switchSelStatus(position: Int, replace: Boolean) {
        val entity = getData(position).albumWithPaintList ?: return
        val id = entity.album.albumId
        var beforeIds = emptyList<Long>()
        if (id in selPaints) {
            selPaints.remove(id)
        } else {
            if (replace) {
                beforeIds = selPaints.keys.toList()
                selPaints.clear()
            }
            selPaints[id] = entity
        }
        selCountChangeListener?.invoke(selPaints.size)

        // 刷新
        getDataList().forEachIndexed { index, paintDataVO ->
            val albumId = paintDataVO.albumWithPaintList?.album?.albumId
            if (albumId == id || beforeIds.contains(albumId)) {
                notifyItemChanged(index)
            }
        }
    }

    override fun areItemsTheSame(oldItem: AlbumDataVO, newItem: AlbumDataVO): Boolean {
        if (oldItem.type != newItem.type) {
            return false
        }
        return if (oldItem.type != ALBUM_VO_TYPE_DATA) {
            oldItem.createTime == newItem.createTime
        } else {
            oldItem.albumWithPaintList?.album?.albumId == newItem.albumWithPaintList?.album?.albumId
        }
    }

    override fun areContentsTheSame(oldItem: AlbumDataVO, newItem: AlbumDataVO): Boolean {
        if (oldItem.type != newItem.type) {
            return false
        }
        return if (oldItem.type != ALBUM_VO_TYPE_DATA) {
            oldItem == newItem
        } else {
            // 画作内容
            oldItem.albumWithPaintList!!.isContentSame(newItem.albumWithPaintList!!)
        }
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): BaseVH {
        return if (viewType == ALBUM_VO_TYPE_DATA) {
            val vh = BaseVH(R.layout.item_album, parent)

            vh.itemView.setOnHoverListener { v, event ->
                when (event.action) {
                    MotionEvent.ACTION_HOVER_ENTER -> {
                        v.animate()
                            .scaleXY(1.08F)
                        val count = (v as ViewGroup).childCount
                        for (i in 0 until count) {
                            val child = v.getChildAt(i)
                            child.animate().translationZ(10F)
                        }
                    }

                    MotionEvent.ACTION_HOVER_EXIT -> {
                        v.animate()
                            .scaleXY(1F)
                        val count = (v as ViewGroup).childCount
                        for (i in 0 until count) {
                            val child = v.getChildAt(i)
                            child.animate().translationZ(0F)
                        }
                    }
                }
                true
            }
            vh
        } else {
            BaseVH(R.layout.item_paint_title, parent)
        }
    }

    override fun bindViewHolder(holder: BaseVH, position: Int, itemData: AlbumDataVO) {
        val gDrawable = holder.itemView.background
        if (gDrawable != null) {
            val insetDrawable = InsetDrawable(gDrawable, 0, 0, 30, 30)
            holder.itemView.background = insetDrawable
        }
        if (itemData.type == ALBUM_VO_TYPE_DATA) {
            val albumWithPaintList = itemData.albumWithPaintList!!
            // 单张/多张
            holder.visible(albumWithPaintList.isMutablePaint, R.id.multipleMarginSize)
            if (albumWithPaintList.isMutablePaint) {
                holder.setImgResource(R.drawable.img_multiple_paint_mask, R.id.paintCountMask)
            } else {
                holder.setImgResource(R.drawable.img_single_paint_mask, R.id.paintCountMask)
            }

            val filePath = albumWithPaintList.paintList.first().contentImgPath
            // 画布
            holder.setImageFileCenterCrop(
                filePath,
                ObjectKey("${filePath}${albumWithPaintList.album.lastOpenTime}"),
                R.id.paintIv
            )
            holder.setText(itemData.createTime, R.id.createTimeStr)
            holder.visible(selMode, R.id.paintSelIv)
            if (selMode) {
                if (albumWithPaintList.album.albumId in selPaints) {
                    holder.setImgResource(R.drawable.ic_paint_sel, R.id.paintSelIv)
                } else {
                    holder.setImgResource(R.drawable.ic_paint_sel_none, R.id.paintSelIv)
                }
            }

            when (itemData.albumWithPaintList.albumMode) {
                WPTransferData.Mode.MODE_PALETTE -> holder.setCornersBg(
                    6F,
                    holder.context.getColor(R.color.album_type_palette),
                    R.id.albumModeTv
                ).setText(R.string.str_album_type_palette).visible(true)

                WPTransferData.Mode.MODE_MARK -> holder.setCornersBg(
                    6F,
                    holder.context.getColor(R.color.album_type_mark),
                    R.id.albumModeTv
                ).setText(R.string.str_album_type_mark).visible(true)

                else -> {
                    holder.visible(false, R.id.albumModeTv)
                }
            }
        } else {
            // 标题栏
            holder.setText(itemData.createTime, R.id.itemPaintDataTitleTv)
            if (position != 0) {
                holder.itemView.updatePadding(top = 10)
            } else {
                holder.itemView.updatePadding(top = 0)
            }
        }
    }

    override fun getItemViewType(position: Int): Int {
        return getData(position).type
    }


}