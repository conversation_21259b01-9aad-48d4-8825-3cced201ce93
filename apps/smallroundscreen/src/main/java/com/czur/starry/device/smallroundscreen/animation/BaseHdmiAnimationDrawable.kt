package com.czur.starry.device.smallroundscreen.animation

import android.content.Context
import android.graphics.Bitmap
import android.widget.ImageView
import com.bumptech.glide.Glide
import com.czur.czurutils.log.logTagD
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.MainScope
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext

open class BaseHdmiAnimationDrawable(
    private val context: Context,
    private val imageView: ImageView,
    private val assetPath: String, //动画文件路径
    private var looping: Boolean = false, //动画是否循环播放
    private val fadeEffect: Boolean = false, //设置单次播放动画是否淡入淡出 循环播放动画不生效
    private val onAnimationEnd: (() -> Unit)? = null //动画结束回调
) : CustomAnimation, CoroutineScope by MainScope() {

    companion object {
        private const val TAG = "BaseHdmiAnimationDrawable"
        private const val INITIAL_FRAME_COUNT = 20 //初始加载多少帧后开始播放
        private const val ANIMATION_PLAY_INTERVAL = 40L //动画播放间隔 毫秒
    }

    private var currentIndex = 0
    private val frames = mutableListOf<Bitmap>()
    private var allCount = 0
    private var isAnimationRunning = true

    override fun startAnimation() {
        launch {
            loadFramesAsync(assetPath)
        }
    }

    override fun stopAnimation() {
    }

    private suspend fun loadFramesAsync(pathInAssets: String) {
        val assetsManager = context.assets
        val files = assetsManager.list(pathInAssets)?.sorted()
        allCount = files?.size ?: 0

        files?.forEachIndexed { index, fileName ->
            if (!isAnimationRunning) return@forEachIndexed

            val filePath = "$pathInAssets/$fileName"

            while (frames.size >= INITIAL_FRAME_COUNT) {
                delay(ANIMATION_PLAY_INTERVAL)
            }

            val resource = withContext(Dispatchers.IO) {
                Glide.with(context)
                    .asBitmap()
                    .load("file:///android_asset/$filePath")
                    .submit().get()
            }

            frames.add(resource)
//            logTagD(TAG, "$index fadeEffect alpha $fileName")

            if (index == minOf(INITIAL_FRAME_COUNT - 1, files.size - 1)) {
                startPlayAnimation()
            }
        }
    }

    private fun startPlayAnimation() {
        logTagD(TAG, "startPlayAnimation $assetPath")
        if (frames.isNotEmpty()) {
            isAnimationRunning = true
            currentIndex = 0
            playNextFrame()
        } else {
            logTagD(TAG, "Frames not loaded yet")
        }
    }

    private fun playNextFrame() {
        if (!isAnimationRunning) return

        if (currentIndex < allCount) {
//            logTagD(TAG, "$currentIndex fadeEffect alpha $assetPath")

            if (fadeEffect) {
                if (currentIndex < 10) {
                    imageView.alpha = if (currentIndex == 9) 1.0f else (currentIndex + 1) * 0.1f
                }
                if (currentIndex >= allCount - 10) {
                    imageView.alpha = (allCount - currentIndex - 1) * 0.1f
                }
            } else {
                imageView.alpha = 1.0f
            }

            if (frames.isNotEmpty()) {
                imageView.setImageBitmap(frames[0])
                frames.removeAt(0)
                currentIndex++
            } else {
                logTagD(TAG, "Frames list is empty, cannot play next frame")
            }

            if (currentIndex == allCount - 1 && !looping) {
                onAnimationEnd?.invoke()
            } else {
                imageView.postDelayed({ playNextFrame() }, ANIMATION_PLAY_INTERVAL)
            }
        } else if (looping) {
            logTagD(TAG, "$assetPath 播放下一轮")
            launch {
                loadFramesAsync(assetPath)
            }
        }
    }

    override fun endAnimation() {
        logTagD(TAG, "$assetPath 结束")
        isAnimationRunning = false
        frames.clear()
    }
}