package com.czur.starry.device.noticecenter.notice.popup

import android.content.Intent
import android.graphics.drawable.Drawable
import android.os.Bundle
import androidx.core.view.isVisible
import com.bumptech.glide.Glide
import com.bumptech.glide.load.DataSource
import com.bumptech.glide.load.engine.GlideException
import com.bumptech.glide.request.RequestListener
import com.bumptech.glide.request.target.Target
import com.czur.czurutils.extension.platform.newTask
import com.czur.czurutils.global.globalAppCtx
import com.czur.czurutils.log.logTagE
import com.czur.czurutils.log.logTagV
import com.czur.czurutils.log.logTagW
import com.czur.starry.device.baselib.base.v2.aty.CZViewBindingAty
import com.czur.starry.device.baselib.utils.launch
import com.czur.starry.device.noticecenter.R
import com.czur.starry.device.noticecenter.databinding.ActivityPopupMsgBinding
import kotlinx.coroutines.delay

/**
 * Created by 陈丰尧 on 2024/8/12
 */
class PopupMsgActivity : CZViewBindingAty<ActivityPopupMsgBinding>() {
    companion object {
        private const val TAG = "PopupMsgActivity"
        const val EXTRA_KEY_TITLE = "title"
        const val EXTRA_KEY_CONTENT = "content"
        const val EXTRA_KEY_BTN_NEGATIVE_TEXT = "btn_negative_text"
        const val EXTRA_KEY_BTN_POSITIVE_TEXT = "btn_positive_text"
        const val EXTRA_KEY_BTN_POSITIVE_ACTION = "btn_positive_action"
        const val EXTRA_KEY_IMAGE = "image"
        const val EXTRA_KEY_NOTIFICATION_ID = "notification_id"
    }


    override fun ActivityPopupMsgBinding.initBindingViews() {
        val title = intent.getStringExtra(EXTRA_KEY_TITLE) ?: ""
        titleTv.text = title

        val content = intent.getStringExtra(EXTRA_KEY_CONTENT) ?: ""
        contentTv.text = content

        val btnNegativeText = intent.getStringExtra(EXTRA_KEY_BTN_NEGATIVE_TEXT) ?: ""
        negativeButton.text = btnNegativeText

        val btnPositiveText = intent.getStringExtra(EXTRA_KEY_BTN_POSITIVE_TEXT) ?: ""
        positiveButton.text = btnPositiveText

        val image = intent.getStringExtra(EXTRA_KEY_IMAGE) ?: ""
        if (image.isNotEmpty()) {
            Glide.with(globalAppCtx).load(image)
                .addListener(object : RequestListener<Drawable> {
                    override fun onLoadFailed(
                        e: GlideException?,
                        model: Any?,
                        target: Target<Drawable>,
                        isFirstResource: Boolean
                    ): Boolean {
                        logTagW(TAG, "load image failed", tr = e)
                        setViewIsVisible(true)
                        return false
                    }

                    override fun onResourceReady(
                        resource: Drawable,
                        model: Any,
                        target: Target<Drawable>?,
                        dataSource: DataSource,
                        isFirstResource: Boolean
                    ): Boolean {
                        logTagV(TAG, "load image success")
                        setViewIsVisible(true)
                        return false
                    }

                })
                .error(R.drawable.bg_drawable_popup_msg)
                .into(bgImgIv)
        } else {
            finish()
        }

        closeIv.setOnClickListener {
            finish()
        }
        negativeButton.setOnClickListener {
            finish()
        }
        positiveButton.setOnClickListener {
            val action = intent.getStringExtra(EXTRA_KEY_BTN_POSITIVE_ACTION) ?: ""
            if (action.isNotEmpty()) {
                try {

                    startActivity(Intent(action).newTask())
                } catch (tr: Throwable) {
                    logTagE(TAG, "startActivity failed", tr = tr)
                }
            }
            finish()
        }
    }

    override fun finish() {
        setViewIsVisible(false)
        super.finish()
    }

    private fun setViewIsVisible(visible: Boolean) {
        binding.titleTv.isVisible = visible
        binding.closeIv.isVisible = visible
        binding.contentTv.isVisible = visible
        binding.negativeButton.isVisible = visible
        binding.positiveButton.isVisible = visible
        binding.noMoreRemindersTv.isVisible = visible
        binding.noMoreRemindersCb.isVisible = visible
        binding.bgImgIv.isVisible = visible
    }

    override fun onDestroy() {
        if (binding.noMoreRemindersCb.isChecked()) {
            val notificationId = (intent.getStringExtra(EXTRA_KEY_NOTIFICATION_ID) ?: "-1").toLong()
            if (notificationId != -1L) {
                PopupMsgManager.disablePopupMsg(notificationId)
            }
        }
        super.onDestroy()
    }
}