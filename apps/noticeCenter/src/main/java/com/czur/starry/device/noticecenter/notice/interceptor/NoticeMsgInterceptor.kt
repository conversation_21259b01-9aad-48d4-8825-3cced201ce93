package com.czur.starry.device.noticecenter.notice.interceptor

import com.czur.czurutils.log.logTagD
import com.czur.czurutils.log.logTagW
import com.czur.starry.device.baselib.notice.MsgType
import com.czur.starry.device.baselib.notice.NoticeMsg
import com.czur.starry.device.noticecenter.app.NoticeApp
import com.czur.starry.device.noticecenter.provider.NotifyMsgService
import com.czur.starry.device.noticecenter.provider.ReadMsg
import com.czur.starry.device.noticecenter.provider.SyncNotifyMsg
import kotlinx.coroutines.CoroutineScope

/**
 * Created by 陈丰尧 on 2021/8/9
 * 有新的通知消息
 */
class NoticeMsgInterceptor : NoticeInterceptor {
    companion object {
        private const val TAG = "NoticeMsgInterceptor"

        private const val READ_ENTERPRISE_ID = "enterpriseId"
    }

    override suspend fun intercept(msgType: MsgType, msg: NoticeMsg, scope: CoroutineScope): Boolean {
        if (msgType.match(MsgType.SYNC, MsgType.SYNC_NOTICE)) {
            logTagD(TAG, "有新的消息")
            NotifyMsgService.addTask(NoticeApp.context, SyncNotifyMsg)
        }
        if (msgType.match(MsgType.NOTIFY_MSG, MsgType.NOTIFY_READ)) {
            val enterpriseId = msg.getStr(READ_ENTERPRISE_ID)

            if (enterpriseId.isNotEmpty()) {
                logTagD(TAG, "标记消息为已读:企业id:${enterpriseId}")
                NotifyMsgService.addTask(
                    NoticeApp.context,
                    ReadMsg(mapOf(READ_ENTERPRISE_ID to enterpriseId))
                )
            } else {
                logTagW(TAG, "企业ID为空, 忽略信息")
            }
            return true
        }

        return false // 不拦截, 继续转发
    }
}