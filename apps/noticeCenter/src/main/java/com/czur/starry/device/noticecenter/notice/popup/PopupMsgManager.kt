package com.czur.starry.device.noticecenter.notice.popup

import com.bumptech.glide.Glide
import com.czur.czurutils.global.globalAppCtx
import com.czur.czurutils.log.logTagD
import com.czur.czurutils.log.logTagE
import com.czur.czurutils.log.logTagV
import com.czur.czurutils.log.logTagW
import com.czur.starry.device.baselib.common.Constants
import com.czur.starry.device.baselib.cznotification.CZNotificationHandler
import com.czur.starry.device.baselib.cznotification.CZNotificationIntentWrapper
import com.czur.starry.device.baselib.network.HttpManager
import com.czur.starry.device.baselib.utils.ONE_DAY
import com.czur.starry.device.noticecenter.DS_KEY_NOTICE_LAST_SYNC_POPUP_TIME
import com.czur.starry.device.noticecenter.db.NoticeCenterDataBase
import com.czur.starry.device.noticecenter.db.entity.PopupMsgEntity
import com.czur.starry.device.noticecenter.entity.PopupMsg
import com.czur.starry.device.noticecenter.getDSValue
import com.czur.starry.device.noticecenter.net.IPopupNoticeServer
import com.czur.starry.device.noticecenter.setDSValue
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.MainScope
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext

/**
 * Created by 陈丰尧 on 2024/8/12
 */

object PopupMsgManager : CoroutineScope by MainScope() {
    private const val TAG = "PopupMsgManager"
    private const val CHECK_SERVER_INTERVAL = ONE_DAY

    private val popupNoticeServer: IPopupNoticeServer by lazy {
        HttpManager.getService(
            Constants.OTA_BASE_URL
        )
    }

    // 数据库
    private val popupMsgDao by lazy {
        NoticeCenterDataBase.instance.popupMsgDao()
    }

    private var showingMsgMap: MutableMap<Long, Int> =
        mutableMapOf()// key: msgID value NotificationID
    private val czNotificationHandler = CZNotificationHandler()

    suspend fun clearAllPkgNotification() {
        czNotificationHandler.clearAllPkgNotification(globalAppCtx.packageName)
        showingMsgMap.clear()
    }

    /**
     * 同步云端配置
     */
    suspend fun syncCloudPopupMsgList() {
        if (needSyncCloudConfig()) {
            val cloudEntity = withContext(Dispatchers.IO) {
                popupNoticeServer.checkPopupNotice()
            }
            if (!cloudEntity.isSuccess) {
                logTagW(TAG, "同步PopupMsgManager失败")
            }
            withContext(Dispatchers.IO) {
                // 触发Glide的下载图片缓存
                if (cloudEntity.bodyList.isNotEmpty()) {
                    val iconUrl = cloudEntity.bodyList[0].iconUrl
                    val imageUrl = cloudEntity.bodyList[0].imageUrl
                    // 触发Glide的下载图片缓存
                    Glide.with(globalAppCtx).asFile().load(iconUrl).submit().get()
                    Glide.with(globalAppCtx).asFile().load(imageUrl).submit()
                        .get()
                } else {
                    // bodyList 为空，进行相应处理
                    logTagE(TAG, "====bodyList 为空")
                }

            }
            // 记录同步时间
            saveToDatabase(cloudEntity.bodyList)
            setDSValue(DS_KEY_NOTICE_LAST_SYNC_POPUP_TIME, System.currentTimeMillis())
        } else {
            logTagV(TAG, "不需要同步PopupMsgManager")
        }
        checkAndShowPopupMsg()
    }

    fun resetShowingMsgMap() {
        showingMsgMap.clear()
    }

    fun disablePopupMsg(id: Long) {
        launch {
            logTagD(TAG, "disablePopupMsg: $id")
            popupMsgDao.disablePopupServerId(id)
            showingMsgMap.remove(id)?.let {
                logTagD(TAG, "showingMsgMap.remove(id): $it")
                czNotificationHandler.removeNotification(it)
            }
            checkAndShowPopupMsg()
        }
    }

    fun checkAndShowPopupMsg() {
        launch {
            logTagD(TAG, "检查通知")
            // 找到需要展示的弹窗
            val needShowMsg = popupMsgDao.getPopupMsgByTime(System.currentTimeMillis())
            logTagV(TAG, "需要展示的弹窗: $needShowMsg")
            if (needShowMsg == null) {
                logTagV(TAG, "没有需要展示的通知")
                clearAllPkgNotification()
                return@launch
            }

            // 1. 删除不需要展示的弹窗
            showingMsgMap.keys.forEach {
                if (it != needShowMsg.serverId) {
                    // 移除通知
                    czNotificationHandler.removeNotification(showingMsgMap[it]!!)
                }
                showingMsgMap.remove(it)
            }

            // 2. 展示新的弹窗
            // 已经显示的不再重复显示
            if (needShowMsg.serverId !in showingMsgMap) {
                val bitmap = withContext(Dispatchers.IO) {
                    Glide.with(globalAppCtx).asBitmap().load(needShowMsg.iconUrl).submit().get()
                }
                val notificationId = czNotificationHandler.showNotification(
                    bitmap,
                    needShowMsg.noticeTitle,
                    CZNotificationIntentWrapper().apply {
                        bootAction =
                            "com.czur.starry.device.noticecenter.popup.PopupMsgActivity"
                        bootPkg = "com.czur.starry.device.noticecenter"
                        extraMap[PopupMsgActivity.EXTRA_KEY_TITLE] = needShowMsg.title
                        extraMap[PopupMsgActivity.EXTRA_KEY_CONTENT] = needShowMsg.content
                        extraMap[PopupMsgActivity.EXTRA_KEY_BTN_NEGATIVE_TEXT] =
                            needShowMsg.cancelButtonText
                        extraMap[PopupMsgActivity.EXTRA_KEY_BTN_POSITIVE_TEXT] =
                            needShowMsg.primaryButtonText
                        extraMap[PopupMsgActivity.EXTRA_KEY_IMAGE] = needShowMsg.imageUrl
                        extraMap[PopupMsgActivity.EXTRA_KEY_BTN_POSITIVE_ACTION] =
                            needShowMsg.primaryButtonAction
                        extraMap[PopupMsgActivity.EXTRA_KEY_NOTIFICATION_ID] = needShowMsg.serverId.toString()
                    },
                    false
                )
                showingMsgMap[needShowMsg.serverId] = notificationId

            }
        }
    }

    private suspend fun saveToDatabase(msgList: List<PopupMsg>) {
        val serverIds = msgList.map { it.id }.toSet()
        // 1. 清理本地的过期数据
        val localMsgList = popupMsgDao.getAllPopupMsg()
        val currentTime = System.currentTimeMillis()
        val needDelList =
            localMsgList.filter { it.endTime < currentTime || it.serverId !in serverIds }
        if (needDelList.isNotEmpty()) {
            logTagV(TAG, "删除过期数据: ${needDelList.size}")
            popupMsgDao.delPopupMsgList(needDelList)
        }

        // 2. 插入新的数据
        val dbEntityList = msgList.map {
            PopupMsgEntity(
                serverId = it.id,
                it.cancelButtonAction,
                it.cancelButtonText,
                it.primaryButtonAction,
                it.primaryButtonText,
                it.title,
                it.content,
                it.imageUrl,
                it.iconUrl,
                it.noticeTitle,
                it.startTimeStamp,
                it.endTimeStamp,
            )
        }
        popupMsgDao.insertPopupMsg(dbEntityList)
    }


    /**
     * 是否需要同步云端配置
     */
    private suspend fun needSyncCloudConfig(): Boolean {
        val lastSyncTime = getDSValue(DS_KEY_NOTICE_LAST_SYNC_POPUP_TIME, 0L)
        return System.currentTimeMillis() - lastSyncTime > CHECK_SERVER_INTERVAL
    }

}