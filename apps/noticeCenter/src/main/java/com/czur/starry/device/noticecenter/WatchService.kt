package com.czur.starry.device.noticecenter

import android.annotation.SuppressLint
import android.app.AlertDialog
import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.content.IntentFilter
import android.os.RemoteException
import android.view.LayoutInflater
import android.view.View
import android.view.WindowManager
import android.widget.Button
import androidx.core.app.NotificationCompat
import androidx.core.view.setPadding
import androidx.lifecycle.LifecycleService
import androidx.lifecycle.lifecycleScope
import com.czer.starry.device.meetlib.MeetingHandler
import com.czer.starry.device.meetlib.MeetingHandler.localMeetingRecordCanEShare
import com.czer.starry.device.meetlib.MeetingHandler.localMeetingRecording
import com.czur.czurutils.log.logIntent
import com.czur.czurutils.log.logTagD
import com.czur.czurutils.log.logTagE
import com.czur.czurutils.log.logTagI
import com.czur.czurutils.log.logTagV
import com.czur.czurutils.log.logTagW
import com.czur.starry.device.baselib.common.KEY_BYOM_STATE
import com.czur.starry.device.baselib.common.KEY_HDMI_AIRPLAY_OPEN
import com.czur.starry.device.baselib.common.KEY_HDMI_AUTO_OPEN
import com.czur.starry.device.baselib.common.KEY_HDMI_STATE_IN
import com.czur.starry.device.baselib.common.KEY_KEYSTONE_CORRECTION_STATE
import com.czur.starry.device.baselib.common.KEY_KEYSTONE_CORRECTION_UPDATE
import com.czur.starry.device.baselib.common.KEY_LAUNCHER_BOOT_COMPLETE
import com.czur.starry.device.baselib.common.KEY_ONLY_USB_PERIPHERAL_USE_MIC
import com.czur.starry.device.baselib.common.KEY_STARTUP_COMPLETE
import com.czur.starry.device.baselib.notice.MsgType
import com.czur.starry.device.baselib.notice.NoticeHandler
import com.czur.starry.device.baselib.utils.CZPowerManager
import com.czur.starry.device.baselib.utils.InternetStatus
import com.czur.starry.device.baselib.utils.ONE_MIN
import com.czur.starry.device.baselib.utils.ONE_SECOND
import com.czur.starry.device.baselib.utils.basic.otherwise
import com.czur.starry.device.baselib.utils.basic.yes
import com.czur.starry.device.baselib.utils.createNotificationChannel
import com.czur.starry.device.baselib.utils.doWithoutCatch
import com.czur.starry.device.baselib.utils.fw.proxy.SystemManagerProxy
import com.czur.starry.device.baselib.utils.launch
import com.czur.starry.device.baselib.utils.ping
import com.czur.starry.device.baselib.utils.prop.getBooleanSystemProp
import com.czur.starry.device.baselib.utils.prop.getStringSystemProp
import com.czur.starry.device.baselib.utils.prop.setBooleanSystemProp
import com.czur.starry.device.baselib.utils.prop.setIntSystemProp
import com.czur.starry.device.baselib.utils.prop.setSystemProp
import com.czur.starry.device.baselib.utils.updateInternetStatus
import com.czur.starry.device.noticecenter.WatchService.Companion.ESHARE_BOOT_ACTION
import com.czur.starry.device.noticecenter.WatchService.Companion.HDMIIN_CLASS_NAME
import com.czur.starry.device.noticecenter.WatchService.Companion.HDMIIN_PACKAGE_NAME
import com.czur.starry.device.noticecenter.WatchService.Companion.KEY_MEETING_STAT_PROP
import com.czur.starry.device.noticecenter.dialog.PairFailedDialog
import com.czur.starry.device.noticecenter.hw.ConflictProcessor
import com.czur.starry.device.noticecenter.notice.UserAndNetWatcher
import com.czur.starry.device.noticecenter.notice.popup.PopupMsgManager
import com.czur.starry.device.noticecenter.notice.startUserAndNetWatcher
import com.czur.starry.device.noticecenter.util.CompatibleTool
import com.czur.starry.device.noticecenter.watcher.CameraWatcher
import com.czur.starry.device.noticelib.hwconn.HwConnHelper
import com.czur.starry.device.sharescreen.esharelib.EShareClientType
import com.czur.starry.device.sharescreen.esharelib.ESharePairState
import com.czur.starry.device.sharescreen.esharelib.E_SHARE_BYOM_AUDIO_RUNNING
import com.czur.starry.device.sharescreen.esharelib.E_SHARE_BYOM_CAMERA_RUNNING
import com.czur.starry.device.sharescreen.esharelib.SimpleEShareCallback
import com.czur.starry.device.sharescreen.esharelib.util.onShowRequestAlert
import com.eshare.serverlibrary.api.EShareServerSDK
import com.eshare.serverlibrary.api.EShareUtils
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.Job
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.buffer
import kotlinx.coroutines.flow.catch
import kotlinx.coroutines.flow.combine
import kotlinx.coroutines.flow.distinctUntilChanged
import kotlinx.coroutines.flow.onEach
import kotlinx.coroutines.flow.onErrorReturn
import kotlinx.coroutines.isActive
import kotlinx.coroutines.launch
import rockchip.hardware.hdmi.V1_0.HdmiAudioStatus
import rockchip.hardware.hdmi.V1_0.IHdmi
import rockchip.hardware.hdmi.V1_0.IHdmiCallback


/**
 * Created by 陈丰尧 on 2021/8/9
 * 用来监控3件事:
 * 1. 网络是否连接
 * 2. 是否有网络
 * 3. 是否登录
 * 4. hdmi 插拔
 * 5. 截图消息
 */
private const val TAG = "WatchService"

class WatchService : LifecycleService() {
    companion object {
        private const val WATCH_CHANNEL_NAME = "WatchChannel"
        const val ACTION_ESHARE_QUIT = "com.ecloud.eairplay.action.cz_req_quit"
        const val ACTION_ESHARE_EXITED = "com.ecloud.eairplay.action.disconnected"//已经退出投屏

        //信号源切换无线投屏广播
        const val ACTION_WIRELESS_SOURCE_CHANNEL = "com.android.action.WIRELESS_CHANNEL"

        //前五次截图广播
        const val ACTION_SCREEN_SHOT = "com.android.action.SCREEN_SHOT"

        // HDMI插入广播
        const val ACTION_HDMI_CHANGE = "com.android.action.CZUR_HDMIIN_CHANGED"
        const val ACTION_HDMI_STATE = "com.android.action.CZUR_HDMIIN_EAIRPLAY"
        const val ACTION_HDMI_STATE_KEY = "hdminIn"
        const val EAIRPLAY_PACKAGE_NAME = "com.ecloud.eairplay"
        const val LAUNCHER_PACKAGE_NAME = "com.czur.starry.device.launcher"

        const val HDMIIN_PACKAGE_NAME = "com.czur.starry.device.hdmiin"
        const val HDMIIN_CLASS_NAME = "com.czur.starry.device.hdmiin.MainActivity"
        const val KEY_MEETING_STAT_PROP = "sys.czur.voipproc"

        const val ESHARE_BOOT_ACTION = "com.czur.starry.device.sharescreen.BOOT_APP"
        private const val KEY_EXTRAS_HDMI_STATUS = "state"

        var quitEshareDialog: AlertDialog? = null

        /**
         * 启动监控服务
         */
        fun startWatchingService(context: Context) {
            logTagV(TAG, "startWatchingService")
            val intent = Intent(context, WatchService::class.java)
            context.startService(intent)
        }

        fun refreshWatch(context: Context) {
            val intent = Intent(context, WatchService::class.java).apply {
                putExtra("refresh", true)
            }
            context.startService(intent)
        }
    }


    private val job = Job()
    private val scope = CoroutineScope(job)

    private val pingFlow = MutableStateFlow(System.currentTimeMillis())


    private var eShareBackButtonReceiver: EShareBackButtonReceiver? = null
    private var eShareExitedReceiver: EShareExitedReceiver? = null

    private var wirelessReceiver: WirelessStatusReceiver? = null
    private var screenShotReceiver: ScreenShotReceiver? = null
    private var hdmiCallback: HDMICallback? = null
    private var hdmiService: IHdmi? = null

    // 资源冲突处理器
    private val conflictProcessor by lazy {
        ConflictProcessor(lifecycleScope)
    }

    // 网络连接状态
    private var netConnectStatus = false
        set(value) {
            if (field == value) return
            field = value
            launch {
                refreshPingState()
            }
        }
    private val systemManager: SystemManagerProxy by lazy { SystemManagerProxy(::onSystemChange) }
    private val byomScreenLock by lazy {
        CZPowerManager.createOneWakeLock("czByomScreen")
    }

    private val eShareCallback = object : SimpleEShareCallback() {
        override fun showRequestAlert(clientIp: String?, clientName: String?, clientType: Int) {
            logTagD(
                TAG,
                "showRequestAlert:${clientType} - clientIp:$clientIp - clientName:$clientName"
            )
            try {
                if (!MeetingHandler.localMeetingRecordCanEShare) {
                    logTagD(TAG, "当前处于本地会议录像中, 拒绝投屏")
                    eShareServerSDK.refuseScreenCast(clientIp, 7)
                    return
                }
                CZPowerManager.wakeUpScreen("ScreenShareStart")
                onShowRequestAlert(
                    this@WatchService,
                    eShareServerSDK,
                    EShareClientType.create(clientType),
                    clientIp,
                    clientName
                )

            } catch (exception: Exception) {
                logTagD(TAG, "showRequestAlert exception = ${exception}")
            }
        }

        override fun notifyPairState(pairState: Int) {
            logTagD(TAG, "notifyPairState:${pairState}")
            if (pairState == ESharePairState.SUCCESS.value) {
                HwConnHelper.showHwConnAlert(
                    this@WatchService,
                    HwConnHelper.HwConnType.CLICK_DROP_TYPE_C
                )
            } else if (pairState == ESharePairState.FAILURE.value) {
                val intent = Intent(this@WatchService, PairFailedDialog::class.java).apply {
                    addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
                }
                doWithoutCatch {
                    <EMAIL>(intent)
                }
            }
        }

        override fun showClientByomRequest(clientIp: String?, type: Int) {
            super.showClientByomRequest(clientIp, type)
            logTagV(TAG, "showClientByomRequest: clientIp = $clientIp, type = $type")
            conflictProcessor.onEShareByomRequest(clientIp, type) {
                logTagV(TAG, "允许byom投屏前的操作")
                enableHDMIAudio(false)
            }
        }

        override fun onSettingsChanged(key: String, newValue: Any?) {
            super.onSettingsChanged(key, newValue)
            when (key) {
                E_SHARE_BYOM_CAMERA_RUNNING, E_SHARE_BYOM_AUDIO_RUNNING -> {
                    conflictProcessor.refreshByomRunningFlow()
                    if (newValue == true) {
                        logTagV(TAG, "EShare onSettingsChanged: BYOM_CAMERA_RUNNING true")
                        conflictProcessor.onEShareByomRequest(null, null)
                    }
                }
            }
        }
    }

    private val eShareServerSDK by lazy {
        EShareServerSDK.getSingleton(this)
    }

    private var watcher: UserAndNetWatcher<WatchService>? = null

    private val compatibleTool = CompatibleTool()

    override fun onCreate() {
        super.onCreate()
        logTagI(TAG, "监控服务启动...")

        eShareServerSDK.registerCallback(eShareCallback)
        registerEShareBackButtonReceiver()
        registerEShareExited()

        registerWirelessReceiver()
        registerHDMICallback()
        registerScreenShotReceiver()

        // 添加Notification
        showNotification()

        setBiCubicState(1)
        watcher = startUserAndNetWatcher(this, scope, systemManager, ::onNetChange)
        CameraWatcher.startWatchCamera()    // 开始监控摄像头状态

        launch {
            compatibleTool.compatibleSystem()
        }

        launch {
            PopupMsgManager.clearAllPkgNotification()
        }

        scope.launch {
            while (isActive) {
                // 每3分钟 固定刷新一次各个数据的状态
                delay(ONE_MIN * 3)
                doWithoutCatch {
                    watcher?.refresh()
                }
            }
        }

        launch {
            pingFlow
                .buffer(8)// 最多缓存8个
                .collect {
                    val internetStatus = ping()
                        .yes {
                            InternetStatus.CONNECT
                        }.otherwise {
                            InternetStatus.DISCONNECT
                        }
                    updateInternetStatus(this@WatchService, internetStatus)
                }
        }

        launch {
            logTagV(TAG, "监控是否有网")
            while (isActive) {
                refreshPingState()
                delay(15 * ONE_SECOND)  // 每15s监控1次
            }
        }

        launch {
            conflictProcessor.refreshUsbGadgetModeFlow()
        }

        launch {
            conflictProcessor.byomRunningFlow.collect { byomRunning ->
                logTagD(TAG, "byomRunningFlow:$byomRunning")
                if (byomRunning) {
                    // 24.08.23 凯凯飞书提:进入BYOM模式时调用SystemManager的这个接口来更新音频策略updateAudioPolicyByByom
                    systemManager.updateAudioPolicyByByom(true)
                    enableHDMIAudio(false)
                } else {
                    conflictProcessor.resetEshareByom()
                    systemManager.updateAudioPolicyByByom(false)
                    enableHDMIAudio(true)
                }
            }
        }

        collectBYOMForSystem()

    }

    /**
     * 给系统设置BYOM状态
     * 值是int类型, 第一位表示eshare, 第二位表示usb,0表示关闭 1表示开启
     */
    @SuppressLint("WakelockTimeout", "Wakelock")
    private fun collectBYOMForSystem() {
        launch(Dispatchers.IO) {
            combine(
                conflictProcessor.byomRunningFlow,
                conflictProcessor.usbGadgetModeFlow
            ) { eshare, usb ->
                // 整合成数字
                val eshareValue = if (eshare) 1 else 0
                val usbValue = if (usb) 1 else 0
                // 按照位组合
                (eshareValue shl 1) or usbValue
            }
                .distinctUntilChanged()
                .onEach {
                    logTagV(TAG, "BYOM状态变化:es:${it shr 1} usb:${it and 1}")
                    if (it > 0) {
                        logTagV(TAG, "获取BYOM-CPU锁")
                        byomScreenLock.acquire()
                    } else {
                        logTagV(TAG, "释放CPU锁")
                        byomScreenLock.release()
                    }
                }.catch {
                    logTagE(TAG, "BYOM状态变化异常", tr = it)
                    byomScreenLock.release()
                }
                .collect {
                    logTagD(TAG, "写入BYOM(eshare&usb)状态变化(for system prop):$it")
                    setSystemProp(KEY_BYOM_STATE, it)
                }
        }
    }

    private fun onSystemChange(
        category: Int,
        eventID: Int,
        para1: Int,
        extend1: String,
        extend2: String
    ) {
        if (category == SystemManagerProxy.CALLBACK_CATEGORY_GADGET) {
            SystemManagerProxy.USBModeState.entries.find { it.state == eventID }?.let {
                conflictProcessor.onGadgetModeSwitch(it)
            }
        }
    }

    private fun enableHDMIAudio(isEnable: Boolean = true) {
        logTagV(TAG, "enableHDMI Audio:$isEnable")
        val audioStatus = HdmiAudioStatus()
        audioStatus.deviceId = hdmiService?.hdmiDeviceId
        //去除判断Mic占用；凯凯沟通确认底层可以处理是否播放声音。当status=1时，结束会议录制会自动切换声音。
        if (isEnable) {
            audioStatus.status = 1
        } else {
            audioStatus.status = 0
        }
        try {
            hdmiService?.onAudioChange(audioStatus)
        } catch (e: Exception) {
            logTagE(TAG, "enableHDMI Audio error", tr = e)
        }
    }

    /**
     * 网络连接状态发生改变
     */
    private fun onNetChange(connect: Boolean) {
        netConnectStatus = connect
    }

    /**
     * 刷新Ping的状态
     */
    private suspend fun refreshPingState() {
        pingFlow.emit(System.currentTimeMillis())
    }

    override fun onStartCommand(intent: Intent?, flags: Int, startId: Int): Int {
        if (intent != null) {
            val refresh = intent.getBooleanExtra("refresh", false)
            logTagD(TAG, "手动刷新Netty状态")
            if (refresh) {
                if (watcher == null) {
                    watcher = startUserAndNetWatcher(this, scope, systemManager, ::onNetChange)
                }
                watcher?.refresh()
            }
        }
        return super.onStartCommand(intent, flags, startId)
    }

    private fun showNotification() {
        createNotificationChannel(WATCH_CHANNEL_NAME, WATCH_CHANNEL_NAME)
        val notification = NotificationCompat.Builder(this, WATCH_CHANNEL_NAME)
            .setContentTitle(TAG)
            .setContentText(TAG)
            .setSmallIcon(R.mipmap.ic_launcher)
            .build()
        startForeground(3, notification)
    }

    private fun registerEShareExited() {
        eShareExitedReceiver = EShareExitedReceiver()
        val filter = IntentFilter().apply {
            addAction(ACTION_ESHARE_EXITED)
        }
        registerReceiver(eShareExitedReceiver, filter, RECEIVER_EXPORTED)

    }

    private fun registerEShareBackButtonReceiver() {
        eShareBackButtonReceiver = EShareBackButtonReceiver()
        val filter = IntentFilter().apply {
            addAction(ACTION_ESHARE_QUIT)
        }
        registerReceiver(eShareBackButtonReceiver, filter, RECEIVER_EXPORTED)
    }

    private class EShareExitedReceiver : BroadcastReceiver() {
        override fun onReceive(context: Context, intent: Intent?) {
            val action = intent?.action ?: return
            logTagD(TAG, "宜享通关闭投屏 action:${intent.action}")
            quitEshareDialog?.dismiss()
        }
    }

    private class EShareBackButtonReceiver : BroadcastReceiver() {
        override fun onReceive(context: Context, intent: Intent?) {
            val action = intent?.action ?: return
            logTagD(TAG, "宜享通知返回按键 action:${intent.action}")

            closeEshareDialog(context)
        }

        fun closeEshareDialog(context: Context) {
            val builder = AlertDialog.Builder(context, R.style.user_default_dialog)
            val view = LayoutInflater.from(context)
                .inflate(R.layout.activity_eshare_quit_dialog, null, false)
            builder.setView(view)
            (view.findViewById<Button>(R.id.quitDialogConfirmBtn)!!).setOnClickListener {
                quitCast(context)
                quitEshareDialog?.dismiss()
            }
            (view.findViewById<Button>(R.id.quitDialogCancelBtn)!!).setOnClickListener {
                cancelCast(context)
                quitEshareDialog?.dismiss()
            }

            quitEshareDialog = builder.create()
            quitEshareDialog?.window?.apply {
                setLayout(
                    WindowManager.LayoutParams.WRAP_CONTENT,
                    WindowManager.LayoutParams.WRAP_CONTENT
                )
                setType(WindowManager.LayoutParams.TYPE_APPLICATION_OVERLAY)
            }
            quitEshareDialog?.show()
            quitEshareDialog?.let {
                var parent = view.parent as? View
                while (parent != null) {
                    parent.elevation = 0F
                    parent.setPadding(0)
                    parent = parent.parent as? View
                }
            }
        }

        fun quitCast(context: Context) {
            logTagD(TAG, "通知宜享退出投屏")
            val intent = Intent("com.ecloud.eairplay.action.cz_req_quit_ok")
            EShareUtils.sendBroadcastAsUser(context, intent, "com.ecloud.eairplay")
        }

        fun cancelCast(context: Context) {
            logTagD(TAG, "通知宜享取消退出投屏")
            val intent = Intent("com.ecloud.eairplay.action.cz_req_quit_cancel")
            EShareUtils.sendBroadcastAsUser(context, intent, "com.ecloud.eairplay")
        }
    }


    /**
     * 注册截图广播来显示toast
     */
    private fun registerScreenShotReceiver() {
        screenShotReceiver = ScreenShotReceiver()
        val intentFilter = IntentFilter().apply {
            addAction(ACTION_SCREEN_SHOT)
        }
        registerReceiver(
            screenShotReceiver, intentFilter, Context.RECEIVER_EXPORTED
        )
    }

    /**
     * 注册Wireless的回调
     */
    private fun registerWirelessReceiver() {
        wirelessReceiver = WirelessStatusReceiver()
        val intentFilter = IntentFilter().apply {
            addAction(ACTION_WIRELESS_SOURCE_CHANNEL)
        }
        registerReceiver(wirelessReceiver, intentFilter, RECEIVER_EXPORTED)
    }

    /**
     * 注册HDMI的回调
     */
    private fun registerHDMICallback() {
        try {
            hdmiCallback = HDMICallback()
            hdmiService = IHdmi.getService(true)
            hdmiService?.registerListener(this.packageName, hdmiCallback as IHdmiCallback?)
        } catch (e: Throwable) {
            hdmiService = null
            hdmiCallback = null
            logTagW(TAG, "注册HDMI回调失败", tr = e)
        }
    }

    /**
     * 取消注册截图广播
     */
    private fun unRegisterScreenShotReceiver() {
        screenShotReceiver?.let {
            unregisterReceiver(it)
            screenShotReceiver = null
        }
    }

    /**
     * 取消注册Wireless的监听
     */
    private fun unRegisterWirelessReceiver() {
        wirelessReceiver?.let {
            unregisterReceiver(it)
            wirelessReceiver = null
        }
    }

    /**
     * 取消注册HDMI的监听
     */
    private fun unRegisterHDMICallback() {
        try {
            hdmiService?.unregisterListener(this.packageName, hdmiCallback as IHdmiCallback?)
        } catch (e: RemoteException) {
            logTagW(TAG, "unRegisterHDMICallback 失败", tr = e)
        }
    }


    private inner class HDMICallback : IHdmiCallback.Stub() {
        override fun onConnect(p0: String?) {
            setBooleanSystemProp(KEY_HDMI_STATE_IN, true)
            if (startUpComplete && launcherComplete) {
                logTagD(TAG, "=======HDMI连接成功====")
                sendBroadCastToLauncher(this@WatchService, true)
                switchHdmi(this@WatchService, true)
            }
        }

        override fun onFormatChange(p0: String?, p1: Int, p2: Int) {

        }

        override fun onDisconnect(p0: String?) {
            logTagD(TAG, "=======HDMI断开====")
            setBooleanSystemProp(KEY_HDMI_STATE_IN, false)
            sendBroadCastToLauncher(this@WatchService, false)
            switchHdmi(this@WatchService, false)
        }

    }

    val startUpComplete by lazy {
        getBooleanSystemProp(KEY_STARTUP_COMPLETE, false)
    }
    val launcherComplete by lazy {
        getBooleanSystemProp(KEY_LAUNCHER_BOOT_COMPLETE, false)
    }


    private inner class ScreenShotReceiver : BroadcastReceiver() {
        override fun onReceive(context: Context?, intent: Intent?) {
            logTagV(TAG, "收到了截图广播:${intent}")
            intent?.let {
                logIntent(intent)
            }
            if (intent?.action == ACTION_SCREEN_SHOT) {
                NoticeHandler.sendMessage(MsgType(MsgType.SCREEN_SHOT, MsgType.COMMON_TOAST)) {
                    put(getString(R.string.screen_short_toast))
                }
            }
        }
    }

    private inner class WirelessStatusReceiver : BroadcastReceiver() {

        override fun onReceive(context: Context?, intent: Intent?) {
            logTagV(TAG, "收到了source切换广播:${intent}")
            intent?.let {
                logIntent(intent)
            }
            when (intent?.action) {
                ACTION_WIRELESS_SOURCE_CHANNEL -> {
                    if (!launcherComplete) return
                    // 切换无线投屏信号
                    try {
                        val number = eShareServerSDK.numberOfScreen
                        logTagD(TAG, "======numberOfScreen==$number")
                        if (number == "0") {
                            bootCZURWireless(context)
                        }
                    } catch (e: Exception) {
                        e.printStackTrace()
                        bootCZURWireless(context)
                    }
                }

            }
        }


    }


    override fun onDestroy() {
        job.cancel()
        eShareServerSDK.unregisterCallback(eShareCallback)
        logTagI(TAG, "监控服务停止")
        watcher = null
        CameraWatcher.stopWatchCamera()
        unregisterReceiver(eShareExitedReceiver)
        unregisterReceiver(eShareBackButtonReceiver)
        unRegisterWirelessReceiver()
        unRegisterScreenShotReceiver()
        unRegisterHDMICallback()
        super.onDestroy()
    }

}

fun switchHdmi(context: Context?, hdmiStatus: Boolean) {

    val hdmiAirplay = getBooleanSystemProp(KEY_HDMI_AIRPLAY_OPEN, false)
    val hdmiAutoOpen = getBooleanSystemProp(KEY_HDMI_AUTO_OPEN, false)
    logTagV(TAG, "hdmi插入状态KEY_HDMI_AIRPLAY_OPEN:$hdmiAirplay")
    logTagV(TAG, "hdmi插入状态KEY_HDMI_AUTO_OPEN:$hdmiAutoOpen")
    if (!hdmiStatus) {
        doWithoutCatch {
            if (hdmiAirplay) {
                sendBroadCastToMirror(context, false)
            }
        }
    } else {
        /**
         * allMeetingStatus 包括会议录制
         * 会议录制中录音，拍照可以宜享投屏
         */
        if (!getMeetingProp() || (localMeetingRecording && localMeetingRecordCanEShare)) {
            if (hdmiAirplay) {
                doWithoutCatch {
                    sendBroadCastToMirror(context, true)
                }
            } else {
                if (hdmiAutoOpen) {
                    logTagV(TAG, "通过CZUR启动HDMI 3")
                    doWithoutCatch {
                        bootCZURHdmi(context)
                    }
                }
            }
        }
    }
}

/**
 * 发送广播通知投屏
 */
fun sendBroadCastToMirror(context: Context?, hdmiIn: Boolean) {
    logTagV(TAG, "给宜享发送hdmi广播 hdmiIn:${hdmiIn}")
    val intent = Intent(WatchService.ACTION_HDMI_STATE).apply {
        putExtra(WatchService.ACTION_HDMI_STATE_KEY, hdmiIn)
        setPackage(WatchService.EAIRPLAY_PACKAGE_NAME)
    }
    context?.sendBroadcast(intent)
}

fun sendBroadCastToLauncher(context: Context?, hdmiIn: Boolean) {
    val intent = Intent(WatchService.ACTION_HDMI_CHANGE).apply {
        putExtra(WatchService.ACTION_HDMI_STATE_KEY, hdmiIn)
        setPackage(WatchService.LAUNCHER_PACKAGE_NAME)
    }
    context?.sendBroadcast(intent)
}

/**
 * 启动本地hdmi
 */
fun bootCZURHdmi(context: Context?) {
    logTagV(TAG, "启动本地hdmi")
    val intent = Intent()
    intent.setClassName(HDMIIN_PACKAGE_NAME, HDMIIN_CLASS_NAME)
    intent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
    context?.startActivity(intent)
}

fun bootCZURWireless(context: Context?) {
    val intent = Intent(ESHARE_BOOT_ACTION)
    intent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
    context?.startActivity(intent)
}

fun getMeetingProp(): Boolean {
    if (getBooleanSystemProp(KEY_ONLY_USB_PERIPHERAL_USE_MIC, false)) {
        logTagD(TAG, "getMeetingProp:KEY_ONLY_USB_PERIPHERAL_USE_MIC=true, 返回false")
        return false
    }

    val value = getStringSystemProp(KEY_MEETING_STAT_PROP, "")
    return (!value.isBlank() && value != "0").also {
        logTagD(TAG, "getMeetingProp:$value, 返回:${it}")
    }
}

/**
 * 设置梯形校正状态1开启,0关闭
 */
fun setBiCubicState(state: Int) {
    setIntSystemProp(KEY_KEYSTONE_CORRECTION_STATE, state)
    setIntSystemProp(KEY_KEYSTONE_CORRECTION_UPDATE, 1)//1 表示需要更新
}


