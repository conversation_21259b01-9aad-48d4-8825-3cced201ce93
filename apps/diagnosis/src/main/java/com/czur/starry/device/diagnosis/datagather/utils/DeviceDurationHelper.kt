package com.czur.starry.device.diagnosis.datagather.utils

import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.content.IntentFilter
import android.os.Build
import androidx.lifecycle.DefaultLifecycleObserver
import androidx.lifecycle.LifecycleOwner
import com.czur.czurutils.log.logTagD
import com.czur.starry.device.baselib.common.Constants
import com.czur.starry.device.baselib.network.HttpManager
import com.czur.starry.device.baselib.utils.ONE_MIN
import com.czur.starry.device.baselib.utils.doWithoutCatch
import com.czur.starry.device.baselib.utils.launch
import com.czur.starry.device.diagnosis.datagather.net.IUploadService
import com.czur.starry.device.diagnosis.db.DiagnosisDatabase
import com.czur.starry.device.diagnosis.db.entity.DeviceDurationEntity
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.Job
import kotlinx.coroutines.delay
import kotlinx.coroutines.isActive
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext

/**
 *  author : WangHao
 *  time   :2024/09/19
 */

private const val TAG = "DeviceDurationHelper"

class DeviceDurationHelper(
    private val context: Context,
    private val lifecycleOwner: LifecycleOwner
) : DefaultLifecycleObserver {
    private var toDayTotalTime = 0L
    private var timeAddJob: Job? = null
    private var wakeupCount: Int = 1

    private val deviceDurationDao by lazy {
        DiagnosisDatabase.instance.DeviceDurationDao()
    }
    private val deviceActiveHelper by lazy {
        DeviceActiveHelper(context)
    }

    fun start() {
        lifecycleOwner.launch {
            //获取当天的总时长
            toDayTotalTime = totalTimeFromDb()
            logTagD(TAG, "=======db totalTime====$toDayTotalTime")
            //注册点亮屏幕监听广播
            registerScreenOnReceiver()
            startTiming()
        }

    }

     suspend fun upload() = withContext(Dispatchers.IO) {
         val list = deviceDurationDao.getAllDeviceDurationList(getCurrentDate())
         doWithoutCatch {
             if (list.isNullOrEmpty()){
                 return@withContext
             }
             val result = HttpManager.getService<IUploadService>(Constants.OTA_BASE_URL).uploadDeviceDuration(list)
             if (result.isSuccess) {
                 deviceDurationDao.clearAll(getCurrentDate())
             }
         }
    }

    private fun registerScreenOnReceiver() {
        val screenOnFilter = IntentFilter().apply {
            addAction(Intent.ACTION_SCREEN_ON)
            addAction(Intent.ACTION_SCREEN_OFF)
        }
        context.registerReceiver(screenOnReceiver, screenOnFilter)
    }

    override fun onDestroy(owner: LifecycleOwner) {
        super.onDestroy(owner)
        context.unregisterReceiver(screenOnReceiver)
    }

    private suspend fun saveDurationToDb(totalTime: Long) =
        withContext(Dispatchers.IO) {
            deviceDurationDao.insertOrUpdate(
                DeviceDurationEntity(
                    getCurrentDate(),
                    totalTime,
                    wakeupCount,
                    Constants.SERIAL,
                    Constants.FIRMWARE_NAME
                )
            )
        }


    private suspend fun totalTimeFromDb(): Long = withContext(Dispatchers.IO) {
        deviceDurationDao.getDeviceDuration(getCurrentDate())?.totalTime ?: 0L
    }



    private fun startTiming() {
        logTagD(TAG, "========开始计时========")
        timeAddJob = CoroutineScope(Dispatchers.IO).launch {
            if (null == deviceDurationDao.getDeviceDuration(getCurrentDate())) {
                //跨日期计算清除
                wakeupCount = 1
                toDayTotalTime = 0
                //跨日期唤醒同时需要发送活跃消息
                deviceActiveHelper.deviceDayActive()
            }
            while (isActive) {
                //每5分钟累计一下时长
                delay(5 * ONE_MIN)
                toDayTotalTime += 5 *ONE_MIN
                logTagD(TAG, "========统计时间======$toDayTotalTime==")
                saveDurationToDb(toDayTotalTime)
            }
        }
    }

    private fun stopTiming() {
        timeAddJob?.cancel()
        timeAddJob = null
    }

    private var screenOnReceiver: BroadcastReceiver = object : BroadcastReceiver() {
        override fun onReceive(context: Context, intent: Intent) {
            val action = intent.action ?: return
            when (action) {
                Intent.ACTION_SCREEN_ON -> {
                    wakeupCount++
                    //开始计时
                    startTiming()
                    logTagD(TAG, "========wakeupCount==${wakeupCount}======")
                }

                Intent.ACTION_SCREEN_OFF -> {
                    //停止计时
                    stopTiming()
                    logTagD(TAG, "========使用时长计时停止========")
                }
            }
        }
    }
}