package com.czur.starry.device.diagnosis.db.dao

import androidx.room.Dao
import androidx.room.Insert
import androidx.room.OnConflictStrategy
import androidx.room.Query
import com.czur.starry.device.diagnosis.db.entity.AppDurationEntity

/**
 *  author : <PERSON><PERSON><PERSON>
 *  time   :2024/09/18
 */


@Dao
abstract class AppDurationDao {
    // 查询所有信息(不包含当天的)
    @Query("select * from tab_app_duration where currentDate != :currentDate")
    abstract fun getAllAppDurationList(currentDate: String): List<AppDurationEntity>?

    // 插入或更新
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    abstract fun insertOrUpdate(entity: AppDurationEntity): Long


    //删除
    @Query("delete from tab_app_duration where currentDate != :currentDate")
    abstract fun clearAll(currentDate: String)
}