package com.czur.starry.device.diagnosis.db.entity

import androidx.room.Entity
import androidx.room.PrimaryKey

/**
 *  author : <PERSON><PERSON><PERSON>
 *  time   :2024/09/18
 */

@Entity(tableName = "tab_device_duration")
data class DeviceDurationEntity(
    @PrimaryKey
    val currentDate: String,
    val totalTime: Long,
    val wakeupCount: Int = 1,
    val sn: String,
    val version: String
)