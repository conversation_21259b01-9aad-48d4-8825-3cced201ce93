package com.czur.starry.device.diagnosis.algo

import com.alibaba.sdk.android.oss.ClientException
import com.alibaba.sdk.android.oss.OSS
import com.alibaba.sdk.android.oss.ServiceException
import com.alibaba.sdk.android.oss.callback.OSSCompletedCallback
import com.alibaba.sdk.android.oss.internal.OSSAsyncTask
import com.alibaba.sdk.android.oss.model.CompleteMultipartUploadResult
import com.alibaba.sdk.android.oss.model.MultipartUploadRequest
import com.czur.czurutils.log.logTagD
import com.czur.czurutils.log.logTagE
import com.czur.czurutils.log.logTagV
import com.czur.czurutils.log.logTagW
import com.czur.starry.device.baselib.utils.randomStr


/**
 * Created by 陈丰尧 on 2022/3/10
 * 用来管理OSS任务的
 * 任务包括包括 上传/下载
 * 操作包括: 增加 取消
 */

enum class OssTaskState {
    RUNNING,    // 任务正在运行中
    SUCCESS,    // 任务成功
    FAIL,       // 任务失败
}

class OSSRequestManager(
    private val fileOss: OSS
) {
    companion object {
        private const val TAG = "OSSRequestManager-Diagnosis"
    }

    private val taskMap = mutableMapOf<String, OSSAsyncTask<*>>()
    private val taskStateMap = mutableMapOf<String, OssTaskState>()
    private val errorMap = mutableMapOf<String, Exception>()

    fun getException(taskId: String): Exception? = errorMap[taskId]


    fun clearTask(taskId: String) {
        taskMap.remove(taskId)?.let {
            if (!it.isCanceled && !it.isCompleted) {
                // 如果任务没有完成, 就取消
                it.cancel()
            }
        }
        taskStateMap.remove(taskId)
        errorMap.remove(taskId)

    }

    /**
     * 获取当前Task的状态
     */
    fun getTaskStatus(taskId: String): OssTaskState? = taskStateMap[taskId]

    fun addUploadRequest(
        request: MultipartUploadRequest<*>
    ): String {
        // 生成ID
        val id = generateTaskID()

        // 进度回调
        request.setProgressCallback { _, currentSize, totalSize ->
            val percentage = currentSize.toFloat() / totalSize * 100
            logTagV(TAG, "上传进度: ${request.objectKey}: ${String.format("%.1f", percentage)} (${currentSize}/${totalSize})")
        }

        taskStateMap[id] = OssTaskState.RUNNING


        val task = fileOss.asyncMultipartUpload(
            request,
            object :
                OSSCompletedCallback<MultipartUploadRequest<*>, CompleteMultipartUploadResult> {
                override fun onSuccess(
                    request: MultipartUploadRequest<*>?,
                    result: CompleteMultipartUploadResult?
                ) {
                    taskStateMap[id] = OssTaskState.SUCCESS
                }

                override fun onFailure(
                    request: MultipartUploadRequest<*>?,
                    clientException: ClientException?,
                    serviceException: ServiceException?
                ) {
                    clientException?.let {
                        logTagE(TAG, "上传失败,客户端错误", tr = it)
                        errorMap[id] = it
                    }
                    serviceException?.let {
                        logTagE(TAG, "上传失败, 服务端错误", tr = it)
                        errorMap[id] = it
                    }
                    if (clientException == null && serviceException == null) {
                        logTagW(TAG, "上传失败, OSS没有抛出异常")
                    }
                    taskStateMap[id] = OssTaskState.FAIL
                }

            })

        taskMap[id] = task

        return id

    }

    /**
     * 生成任务ID
     */
    private fun generateTaskID(): String = randomStr()

}