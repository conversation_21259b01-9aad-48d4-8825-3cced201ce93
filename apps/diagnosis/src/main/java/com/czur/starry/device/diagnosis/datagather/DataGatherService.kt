package com.czur.starry.device.diagnosis.datagather

import android.content.pm.PackageManager
import androidx.lifecycle.LifecycleService
import com.czur.czurutils.log.logTagD
import com.czur.starry.device.baselib.utils.InternetStatus
import com.czur.starry.device.baselib.utils.NetStatusUtil
import com.czur.starry.device.baselib.utils.ONE_HOUR
import com.czur.starry.device.baselib.utils.ONE_SECOND
import com.czur.starry.device.baselib.utils.launch
import com.czur.starry.device.diagnosis.datagather.utils.AppDurationHelper
import com.czur.starry.device.diagnosis.datagather.utils.CrashInfoHelper
import com.czur.starry.device.diagnosis.datagather.utils.DeviceActiveHelper
import com.czur.starry.device.diagnosis.datagather.utils.DeviceDurationHelper
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.Job
import kotlinx.coroutines.delay
import kotlinx.coroutines.isActive
import kotlinx.coroutines.launch


/**
 *  author : <PERSON><PERSON><PERSON>
 *  time   :2024/09/09
 */

private const val TAG = "DataGatherService"

class DataGatherService : LifecycleService() {
    private lateinit var mPackageManager: PackageManager

    //网络监听
    private val netStatusUtil: NetStatusUtil by lazy {
        NetStatusUtil(this)
    }
    private var netState = true
    private var uploadJob: Job? = null

    private val deviceDurationHelper by lazy {
        DeviceDurationHelper(this, this)
    }
    private val appDurationHelper by lazy {
        AppDurationHelper(this, this, mPackageManager)
    }
    private val crashInfoHelper by lazy {
        CrashInfoHelper(this, this, mPackageManager)
    }

    private val deviceActiveHelper by lazy {
        DeviceActiveHelper(this)
    }

    override fun onCreate() {
        super.onCreate()
        mPackageManager = this.packageManager

        netStatusUtil.startWatching()
        //网络监听
        netStatusUtil.internetStatusLive.observe(this@DataGatherService) {
            netState = it == InternetStatus.CONNECT
            logTagD(TAG, "网络状态:$it")
            if (it == InternetStatus.CONNECT) {
                startUpload()
                deviceDayActive()
            } else {
                cancelUpload()
            }

        }

        //设备点亮时长
        deviceDurationHelper.start()
        //应用使用时长
        appDurationHelper.start()
        //报错搜集
        crashInfoHelper.start()

    }

    override fun onDestroy() {
        super.onDestroy()
        netStatusUtil.stopWatching()
        cancelUpload()
    }

    private fun startUpload() {
        logTagD(TAG, "=====间隔一小时=====上传数据=========")
        uploadJob = CoroutineScope(Dispatchers.IO).launch {
            delay(10 * ONE_SECOND)
            while (isActive) {
                deviceDurationHelper.upload()
                appDurationHelper.upload()
                crashInfoHelper.upload()
                delay(ONE_HOUR)
            }
        }
    }

    private fun deviceDayActive() {
        launch {
            deviceActiveHelper.deviceDayActive()
        }
    }

    private fun cancelUpload() {
        uploadJob?.cancel()
        uploadJob = null
    }


}