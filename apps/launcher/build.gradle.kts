
plugins {
    id(libs.plugins.application.get().pluginId)
    id(libs.plugins.kotlinAndroid.get().pluginId)
    id("kotlin-parcelize")
    alias(libs.plugins.ksp)
}

private val pkgName = "com.czur.starry.device.launcher"
private val apkName = "CZLauncher"
android.buildFeatures.buildConfig = true
android {
    namespace = pkgName
    compileSdk = libs.versions.compileSdkVersion.get().toInt()

    defaultConfig {
        applicationId = namespace
        minSdk = libs.versions.minSdkVersion.get().toInt()
        targetSdk = libs.versions.targetSdkVersion.get().toInt()

        versionCode = libs.versions.appVersionCode.get().toInt()
        versionName = libs.versions.appVersionName.get()

        testInstrumentationRunner = "androidx.test.runner.AndroidJUnitRunner"
        renderscriptSupportModeEnabled = true

        setFlavorDimensions(listOf("constantEnv"))

        vectorDrawables.useSupportLibrary = true
    }


    // 签名
    signingConfigs {
        create("keyStore") {
            storeFile = file("${rootDir}/signature/starry.jks")
            storePassword = rootProject.ext["storePassword"].toString()
            keyAlias = rootProject.ext["keyAlias"].toString()
            keyPassword = rootProject.ext["keyPassword"].toString()
        }
    }

    buildTypes {
        val signConfig = signingConfigs.getByName("keyStore")
        debug {
            signingConfig = signConfig
            isMinifyEnabled = false
            manifestPlaceholders["LAUNCHER_ENABLE"] = "true"
        }

        create("unsigned") {
            signingConfig = signConfig
            isDebuggable = false
            isMinifyEnabled = false
            manifestPlaceholders["LAUNCHER_ENABLE"] = "false"
        }

    }

    productFlavors {
        create("envProduct") {
            dimension = "constantEnv"
            buildConfigField("Integer", "CONSTANT_ENV", "0")
        }
        create("envTest") {
            dimension = "constantEnv"
            buildConfigField("Integer", "CONSTANT_ENV", "1")
            isDefault = true
        }
    }

    compileOptions {
        sourceCompatibility = JavaVersion.VERSION_11
        targetCompatibility = JavaVersion.VERSION_11
    }
    kotlinOptions {
        jvmTarget = "11"
    }

    buildFeatures {
        aidl = true
        viewBinding = true
    }

    android.applicationVariants.all {
        outputs.all {
            if (this is com.android.build.gradle
                .internal.api.ApkVariantOutputImpl
            ) {
                this.outputFileName = "${apkName}.apk"
            }
        }
    }

}


dependencies {
    implementation(fileTree(baseDir = "libs") {
        include("*.jar")
    })

    // 测试依赖
    testImplementation(libs.junit)
    androidTestImplementation(libs.bundles.androidTest)

    implementation(project(":baselib"))
    implementation(project(":base:hdmiLib"))
    implementation(project(":base:noticeLib"))
    implementation(project(":base:meetlib"))
    implementation(project(":base:otalib"))
    implementation(project(":base:filelib"))
    implementation(project(":base:SettingsLib"))
    implementation(project(":base:eShareLib"))
    implementation(project(":base:UILib"))
    implementation(project(":base:starryPadLib"))

    implementation (libs.viewpager2)
    implementation (libs.paging)
    implementation (libs.material)
    implementation (libs.palette)

    // 数据库相关
    implementation(libs.bundles.room)
    annotationProcessor(libs.room.compiler)
    ksp(libs.room.compiler)
    implementation (project(":aars:renderscriptToolkit"))
}