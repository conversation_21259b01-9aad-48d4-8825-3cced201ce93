package com.czur.starry.device.launcher.utils

import android.Manifest
import android.content.Intent
import android.content.pm.ApplicationInfo
import android.content.pm.PackageManager
import android.content.pm.PackageManager.MATCH_ALL
import com.czur.czurutils.log.logTagD
import com.czur.czurutils.log.logTagE
import com.czur.starry.device.baselib.utils.AppUtil
import com.czur.starry.device.baselib.utils.doWithoutCatch
import com.czur.starry.device.baselib.utils.prop.getBooleanSystemProp
import com.czur.starry.device.launcher.app.App
import com.czur.starry.device.launcher.data.bean.AppInfo
import com.czur.starry.device.settingslib.blackPkgList
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext

/**
 * Created by 陈丰尧 on 2/20/21
 */
private const val TAG = "AppInfoUtil"

val packageManager: PackageManager by lazy {
    appUtil.packageManager
}
private val appUtil: AppUtil by lazy {
    AppUtil()
}

private const val KEY_SHOW_ALL = "persist.launcher.showAll"
private val showAll by lazy {
    try {
        getBooleanSystemProp(KEY_SHOW_ALL, false)
    } catch (exp: Exception) {
        logTagE("AppInfoUtil", "获取showAll失败", tr = exp)
        false
    }
}


// 启动前需要检查麦克风的App
private val needCheckMicAppList = setOf(
    "com.czur.starry.device.localmeetingrecord"
)

// 与视频会议冲突的应用
private val conflictWithMeeting = setOf(
    "com.czur.starry.device.localmeetingrecord"
)

/**
 * 是否需要检查mic
 */
fun needCheckMic(pkgName: String) = pkgName in needCheckMicAppList

fun needCheckMeeting(pkgName: String) = pkgName in conflictWithMeeting

/**
 * 扫描系统中安装的应用
 */
suspend fun loadAppsInfo() = withContext(Dispatchers.IO) {
    val filterIntent = Intent(Intent.ACTION_MAIN, null)
    filterIntent.addCategory(Intent.CATEGORY_LAUNCHER)
    val apps = packageManager.queryIntentActivities(filterIntent, MATCH_ALL)
    val appsInfo = mutableListOf<AppInfo>()
    apps.filter {
        // 去除系统自带的应用
        showAll || it.activityInfo.packageName !in blackPkgList
    }.forEach { resolveInfo ->
        val iconDrawable =
            appUtil.getResIconFromActivityInfo(resolveInfo.activityInfo) ?: appUtil.getIconFromPkg(
                resolveInfo.activityInfo.packageName
            )
        val applicationInfo = resolveInfo.activityInfo.applicationInfo

        val canUnInstall = applicationInfo.flags and ApplicationInfo.FLAG_SYSTEM == 0
        iconDrawable?.let {
            doWithoutCatch {
                val packageName = resolveInfo.activityInfo.packageName
                val appName = resolveInfo.loadLabel(packageManager).toString()
                val pkgInfo =
                    packageManager.getPackageInfo(packageName, PackageManager.GET_PERMISSIONS)
                val installTime = pkgInfo.firstInstallTime
                var needCamera = false
                if (pkgInfo.requestedPermissions?.contains(Manifest.permission.CAMERA) == true) {
                    needCamera = true
                }
                val appInfo =
                    AppInfo(appName, packageName, it, installTime, canUnInstall, needCamera)
                appsInfo.add(appInfo)
            }
        }
    }
    // 用安装时间排序
    appsInfo.sortBy {
        it.installTime
    }
    appsInfo
}

fun makeIntentByPkgManager(pkgName: String) = packageManager.getLaunchIntentForPackage(pkgName)

/**
 * 判断是发已经安装了对应应用
 */
fun hasInstanceThisApp(pkgName: String): Boolean = appUtil.hasInstanceThisApp(pkgName)

/**
 * 卸载App
 */
suspend fun uninstallApp(pkgName: String) = appUtil.uninstallApp(pkgName)