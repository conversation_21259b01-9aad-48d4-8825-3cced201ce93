package com.czur.starry.device.launcher.pages.view.launcher

import android.animation.ValueAnimator
import android.os.Bundle
import android.view.animation.AccelerateDecelerateInterpolator
import android.view.animation.LinearInterpolator
import androidx.fragment.app.Fragment
import androidx.fragment.app.viewModels
import androidx.lifecycle.LifecycleOwner
import com.czur.czurutils.log.logTagV
import com.czur.starry.device.baselib.base.v2.fragment.CZViewBindingFragment
import com.czur.starry.device.baselib.common.Constants
import com.czur.starry.device.baselib.common.hw.StarryModel
import com.czur.starry.device.baselib.utils.ONE_SECOND
import com.czur.starry.device.baselib.utils.createFloatFrameLockAnim
import com.czur.starry.device.baselib.utils.gone
import com.czur.starry.device.baselib.utils.launch
import com.czur.starry.device.baselib.utils.lifecycle.AutoRemoveLifecycleObserver
import com.czur.starry.device.baselib.utils.repeatCollectOnResume
import com.czur.starry.device.baselib.utils.setOnDebounceClickListener
import com.czur.starry.device.baselib.utils.show
import com.czur.starry.device.launcher.R
import com.czur.starry.device.launcher.databinding.FragmentLauncherMainBinding
import com.czur.starry.device.launcher.pages.view.launcher.custom.LeftTimeFragment
import com.czur.starry.device.launcher.pages.view.launcher.quickboot.v2.ComplicatedQuickBootFragment
import com.czur.starry.device.sharescreen.esharelib.util.checkAndActiveEShare

/**
 * Created by 陈丰尧 on 2/19/21(改)
 */

class LauncherMainFragment : CZViewBindingFragment<FragmentLauncherMainBinding>() {
    companion object {
        private const val TAG = "LauncherMainFragment"
        private const val FRAGMENT_TAG_CONFIG = "config"
        private const val FRAGMENT_TAG_QUICK_BOOT = "quickBoot"
    }

    private val keyCodeVM: KeyCodeVM by viewModels({ requireActivity() })

    private val guideIvAnim: ValueAnimator by lazy {
        createFloatFrameLockAnim(15, 0F, 10F) {
            binding.slideUpIv.translationY = it.animatedValue as Float
        }.apply {
            duration = ONE_SECOND
            repeatCount = ValueAnimator.INFINITE
            repeatMode = ValueAnimator.REVERSE
            interpolator = AccelerateDecelerateInterpolator()
        }
    }
    private val guideTvAnim: ValueAnimator by lazy {
        createFloatFrameLockAnim(15, 0.2F, 1F) {
            binding.slideUpTv.alpha = it.animatedValue as Float
        }.apply {
            duration = ONE_SECOND + 500
            repeatCount = ValueAnimator.INFINITE
            repeatMode = ValueAnimator.REVERSE
            interpolator = LinearInterpolator()
        }
    }

    private val configFragment: Fragment?
        get() = childFragmentManager.findFragmentByTag(FRAGMENT_TAG_CONFIG)

    override fun FragmentLauncherMainBinding.initBindingViews() {
        // 左侧的Fragment
        val leftFragment = LeftTimeFragment()
        childFragmentManager.beginTransaction().replace(R.id.fragment_contact, leftFragment)
            .commit()

        showAppFragment()
        launch {
            checkAndActiveEShare(requireContext())
        }

        fun moveToAppPad() {
            (activity as? LauncherMainActivity)?.moveToAppPadPage()
        }

        slideUpIv.setOnDebounceClickListener {
            // 点击时上划
            moveToAppPad()
        }

        if (Constants.starryHWInfo.model == StarryModel.StudioModel.StudioSPlus) {
            slideUpTv.setText(R.string.str_slide_up_hint_simple)
        }
        slideUpTv.setOnDebounceClickListener {
            moveToAppPad()
        }

    }

    override fun initData(savedInstanceState: Bundle?) {
        super.initData(savedInstanceState)
        repeatCollectOnResume(keyCodeVM.showSlideUpGuideFlow) { showGuide ->
            if (showGuide) {
                logTagV(TAG, "显示引导UI")
                binding.slideUpGuideGroup.show()
                guideIvAnim.start() // 图片上下移动动画
                guideTvAnim.start() // 文字呼吸动画
            } else {
                // 隐藏引导UI
                logTagV(TAG, "隐藏引导UI")
                binding.slideUpGuideGroup.gone()
                guideIvAnim.pause()
                guideTvAnim.pause()
            }
        }

        viewLifecycleOwner.lifecycle.addObserver(object : AutoRemoveLifecycleObserver {
            override fun onStop(owner: LifecycleOwner) {
                super.onStop(owner)
                guideIvAnim.pause()
                guideTvAnim.pause()
            }
        })
    }


    private fun showAppFragment() {

        val quickBootFragment = ComplicatedQuickBootFragment()

        val trans = childFragmentManager.beginTransaction()
            .replace(R.id.container, quickBootFragment, FRAGMENT_TAG_QUICK_BOOT)
        configFragment?.let {
            trans.remove(it)
        }
        trans.commit()
    }


}