package com.czur.starry.device.launcher.pages.view.launcher.custom

import android.animation.AnimatorSet
import android.animation.ObjectAnimator
import android.graphics.Bitmap
import android.graphics.Outline
import android.os.Bundle
import android.view.View
import android.view.ViewOutlineProvider
import android.widget.ImageView
import android.widget.TextView
import androidx.core.graphics.drawable.toBitmap
import androidx.fragment.app.activityViewModels
import androidx.fragment.app.viewModels
import com.bumptech.glide.Glide
import com.bumptech.glide.load.engine.DiskCacheStrategy
import com.bumptech.glide.load.resource.drawable.DrawableTransitionOptions
import com.bumptech.glide.request.transition.DrawableCrossFadeFactory
import com.czur.czurutils.log.logTagD
import com.czur.czurutils.log.logTagI
import com.czur.starry.device.baselib.base.v2.fragment.CZViewBindingFragment
import com.czur.starry.device.baselib.common.BootParam
import com.czur.starry.device.baselib.utils.blur
import com.czur.starry.device.baselib.utils.clearContentText
import com.czur.starry.device.baselib.utils.invisible
import com.czur.starry.device.baselib.utils.launch
import com.czur.starry.device.baselib.utils.repeatCollectOnCreate
import com.czur.starry.device.baselib.utils.repeatCollectOnResume
import com.czur.starry.device.baselib.utils.repeatOnResume
import com.czur.starry.device.baselib.utils.setDebounceTouchClickListener
import com.czur.starry.device.baselib.utils.takeScreenShot
import com.czur.starry.device.baselib.utils.toast
import com.czur.starry.device.launcher.R
import com.czur.starry.device.launcher.backdrop.BackdropManager
import com.czur.starry.device.launcher.backdrop.BackdropManager.BackdropColor.Dark
import com.czur.starry.device.launcher.backdrop.BackdropManager.BackdropColor.Light
import com.czur.starry.device.launcher.backdrop.BackdropManager.BackdropEvent
import com.czur.starry.device.launcher.data.bean.AppInfo
import com.czur.starry.device.launcher.databinding.FragmentToolSheetBinding
import com.czur.starry.device.launcher.pages.view.dialog.BootGooglePlayHintFloating
import com.czur.starry.device.launcher.pages.view.dialog.showForceStopDialog
import com.czur.starry.device.launcher.pages.view.launcher.AppInfoViewModel
import com.czur.starry.device.launcher.pages.view.launcher.KeyCodeVM
import com.czur.starry.device.launcher.pages.view.launcher.LauncherMainViewModel
import com.czur.starry.device.launcher.utils.BootCheckResult
import com.czur.starry.device.launcher.utils.bootApp
import com.czur.starry.device.launcher.utils.bootAppByAction
import com.czur.starry.device.launcher.utils.checkBeforeBootApp
import com.czur.starry.device.launcher.utils.saveNeverRemindGooglePlayHint
import com.czur.starry.device.launcher.widget.BlurImageView
import kotlin.getValue

/**
 * Created by 陈丰尧 on 2025/6/3
 */
private const val TAG = "ToolSheetFragment"
private const val ANIM_DURATION = 100L
private const val ANIM_SCALE_RATIO = 1.07f
private const val ANIM_DURATION_CHANGE_ICON = 300L

class ToolSheetFragment : CZViewBindingFragment<FragmentToolSheetBinding>() {
    private val toolSheetList by lazy {
        listOf(
            SheetToolAppView(R.id.toolSheetAppIv1, R.id.toolSheetAppTv1),
            SheetToolAppView(R.id.toolSheetAppIv2, R.id.toolSheetAppTv2),
            SheetToolAppView(R.id.toolSheetAppIv3, R.id.toolSheetAppTv3),
            SheetToolAppView(R.id.toolSheetAppIv4, R.id.toolSheetAppTv4),
        )
    }

    private val keyCodeVM: KeyCodeVM by viewModels({ requireActivity() })
    private val mainViewModel: LauncherMainViewModel by activityViewModels()

    private val appsVM: AppInfoViewModel by viewModels({ requireActivity() })


    override fun FragmentToolSheetBinding.initBindingViews() {
        binding.root.outlineProvider = object : ViewOutlineProvider() {
            override fun getOutline(view: View?, outline: Outline?) {
                // 圆角矩形
                outline?.setRoundRect(
                    0, 0, view!!.width, view.height, 10F
                )
            }
        }
        binding.root.elevation = 20F

        initBlurBg(toolSheetBiv)

        toolSheetLayout.setOnHoverListener { v, event ->
            when (event.action) {
                android.view.MotionEvent.ACTION_HOVER_ENTER -> {
                    scaleUp(v.parent as View)
                }

                android.view.MotionEvent.ACTION_HOVER_EXIT -> {
                    scaleDown(v.parent as View)
                }
            }
            true
        }

        toolSheetLayout.setDebounceTouchClickListener {
            launch {
                val bgImg = takeScreenShot(Bitmap.Config.ARGB_8888)?.blur(samplingRate = 4)
                ToolSheetFloat(bgImg).show()
            }
        }
    }

    override fun initData(savedInstanceState: Bundle?) {
        super.initData(savedInstanceState)

        repeatCollectOnCreate(mainViewModel.backdropColorFlow) {
            when (it) {
                Dark -> binding.root.apply {
                    outlineSpotShadowColor = 0xFF6186FF.toInt()
                    outlineAmbientShadowColor = 0xFF6186FF.toInt()
                }

                Light -> binding.root.apply {
                    outlineSpotShadowColor = 0xFFA6C1FF.toInt()
                    outlineAmbientShadowColor = 0xFFA6C1FF.toInt()
                }
            }

        }

        keyCodeVM.scrollTime.observe(viewLifecycleOwner) {
            binding.toolSheetBiv.invalidate()
        }

        repeatOnResume {
            // 每次回到这个页面都重新加载一次
            appsVM.loadFavAppPkgList()
        }

        repeatCollectOnResume(appsVM.favAppInfoListFlow) {
            toolSheetList.forEachIndexed { index, sheetToolAppView ->
                sheetToolAppView.appInfo = it.getOrNull(index)
            }
        }

        repeatCollectOnCreate(BackdropManager.eventFlow) {
            if (it == BackdropEvent.CHANGE) {
                logTagD(TAG, "壁纸发生变化了,重新获取模糊图")
                // 重新设置模糊图片
                initBlurBg(binding.toolSheetBiv)
            }
        }
    }

    private fun initBlurBg(biv: BlurImageView) {
        biv.post {
            launch {
                BackdropManager.setBlurBitmapToBlurIv(biv)
            }
        }
    }

    private inner class SheetToolAppView(val icon: ImageView, val name: TextView) {
        var appInfo: AppInfo? = null
            set(value) {
                if (field != value) {
                    field = value
                    if (value != null) {
                        onAppInfoSet(value)
                    } else {
                        name.clearContentText()
                        icon.setImageBitmap(null)
                        icon.setImageDrawable(null)
                    }
                    icon.invisible(value == null)
                    name.invisible(value == null)
                }
            }

        private val crossFadeBuilder =
            DrawableCrossFadeFactory.Builder(ANIM_DURATION_CHANGE_ICON.toInt())
                .setCrossFadeEnabled(true)

        constructor(iconId: Int, nameId: Int) : this(
            requireView().findViewById<ImageView>(iconId),
            requireView().findViewById(nameId)
        )

        /**
         * 切换图标时的动画效果
         */
        private fun changeIconDrawable(appInfo: AppInfo) {
            // 如果不使用新的Bitmap, 那么动画效果就不会生效
            val bitmap =
                appInfo.appIcon.toBitmap(icon.width, icon.height)
            val builder = Glide.with(icon)
                .load(bitmap)
                .diskCacheStrategy(DiskCacheStrategy.NONE)
            if (icon.drawable == null) {
                builder.into(icon)  // 不加载动画,直接显示
            } else {
                builder
                    .placeholder(icon.drawable)
                    .transition(DrawableTransitionOptions.withCrossFade(crossFadeBuilder))
                    .into(icon)
            }
        }

        private fun onAppInfoSet(appInfo: AppInfo) {
            changeIconDrawable(appInfo)
            name.text = appInfo.appName

            icon.setDebounceTouchClickListener {
                bootFavApp(appInfo)
            }
            name.setDebounceTouchClickListener {
                bootFavApp(appInfo)
            }

            icon.setOnHoverListener { v, event ->
                when (event.action) {
                    android.view.MotionEvent.ACTION_HOVER_ENTER -> {
                        scaleUp(v)
                    }

                    android.view.MotionEvent.ACTION_HOVER_EXIT -> {
                        scaleDown(v)
                    }
                }
                false
            }
        }
    }

    /**
     * 放大
     */
    private fun scaleUp(view: View) {
        val animSet = AnimatorSet()
        val objectAnimatorX = ObjectAnimator.ofFloat(
            view, "scaleX",
            ANIM_SCALE_RATIO
        )
        val objectAnimatorY = ObjectAnimator.ofFloat(
            view, "scaleY",
            ANIM_SCALE_RATIO
        )
        objectAnimatorX.duration = ANIM_DURATION
        objectAnimatorY.duration = ANIM_DURATION
        animSet.play(objectAnimatorX).with(objectAnimatorY)
        animSet.start()
    }

    /**
     * 缩小
     */
    private fun scaleDown(view: View) {
        val animSet = AnimatorSet()
        val objectAnimatorX = ObjectAnimator.ofFloat(view, "scaleX", 1.0f)
        val objectAnimatorY = ObjectAnimator.ofFloat(view, "scaleY", 1.0f)
        objectAnimatorX.duration = ANIM_DURATION
        objectAnimatorY.duration = ANIM_DURATION
        animSet.play(objectAnimatorX).with(objectAnimatorY)
        animSet.start()
    }

    /**
     * 启动推荐App
     */
    private fun bootFavApp(appInfo: AppInfo) {
        logTagD(TAG, "启动app:${appInfo.appName}")

        if (appInfo.pkgName.isEmpty()) {
            logTagD(TAG, "点击加号, 启动Setting")
            bootAppByAction(BootParam.ACTION_BOOT_NOTICE_SETTING) {
                putExtra(BootParam.BOOT_KEY_SETTING_PAGE_MENU_KEY, "launcherFavApps")
            }
            return
        }

        when (val checkBeforeBootApp = checkBeforeBootApp(appInfo)) {
            BootCheckResult.ConflictWithMeeting -> {
                toast(R.string.toast_conflict_with_meeting, appInfo.appName)
            }

            is BootCheckResult.ConflictWithMic -> {
                logTagI(TAG, "启动app:${appInfo.appName} 与麦克风冲突")
                showForceStopDialog(
                    requireActivity(),
                    appInfo,
                    checkBeforeBootApp.useMicProcessList.toMutableList()
                ) {
                    bootFavApp(appInfo)
                }
            }

            BootCheckResult.HintGooglePlay -> {
                BootGooglePlayHintFloating { neverRemind, floating ->
                    floating.dismiss()
                    if (neverRemind) {
                        launch {
                            saveNeverRemindGooglePlayHint()
                        }
                    }
                    bootApp(pkgName = appInfo.pkgName, context = requireContext())
                }.show()
            }

            BootCheckResult.Success -> {
                bootApp(pkgName = appInfo.pkgName, context = requireContext())
            }

        }
    }
}