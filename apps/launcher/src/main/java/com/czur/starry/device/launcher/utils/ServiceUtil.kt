package com.czur.starry.device.launcher.utils

import android.app.ActivityManager
import android.content.Context

/**
 * 检查服务是否正在运行
 */
fun isServiceRunning(context: Context, serviceClassName: String): Boolean {
    val manager = context.getSystemService(Context.ACTIVITY_SERVICE) as ActivityManager
    for (service in manager.getRunningServices(Int.MAX_VALUE)) {
        if (serviceClassName == service.service.className) {
            return true
        }
    }
    return false
}
