package com.czur.starry.device.launcher.utils

import android.app.WallpaperManager
import android.content.Context
import android.graphics.Bitmap
import android.graphics.BitmapFactory
import android.graphics.Canvas
import android.graphics.Color
import android.graphics.Paint
import android.os.Environment
import android.provider.Settings
import androidx.annotation.VisibleForTesting
import com.czur.czurutils.log.logTagD
import com.czur.czurutils.log.logTagI
import com.czur.czurutils.log.logTagV
import com.czur.czurutils.log.logTagW
import com.czur.czurutils.log.logV
import com.czur.starry.device.baselib.common.Constants
import com.czur.starry.device.baselib.common.KEY_HDMI_AUTO_OPEN
import com.czur.starry.device.baselib.common.VersionIndustry
import com.czur.starry.device.baselib.common.StarryDevLocale
import com.czur.starry.device.baselib.utils.CZUR_LOG_DIR_NAME
import com.czur.starry.device.baselib.utils.ONE_SECOND
import com.czur.starry.device.baselib.utils.SettingUtil
import com.czur.starry.device.baselib.utils.fw.proxy.RkDisplayOutputManagerProxy
import com.czur.starry.device.baselib.utils.prop.setBooleanSystemProp
import com.czur.starry.device.launcher.R
import com.czur.starry.device.launcher.app.App
import com.czur.starry.device.sharescreen.esharelib.DEF_E_SHARE_NAME
import com.czur.starry.device.sharescreen.esharelib.PIN_CODE_MODE_DISABLE
import com.eshare.serverlibrary.api.EShareServerSDK
import com.eshare.serverlibrary.api.IEShareServerSDK
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.delay
import kotlinx.coroutines.withContext
import java.io.File
import java.nio.file.Files
import java.nio.file.StandardCopyOption

/**
 * Created by 陈丰尧 on 2023/2/10
 */
private const val TAG = "UpdateUtil"

private val appContext by lazy { App.context }
val updateTask = listOf(
    1 to ::updateTo1,   // 升级到版本1
    3 to ::updateTo3,   // 升级到版本3
    4 to ::updateTo4,   // 升级到版本4 DLNA功能
    3000 to ::updateTo3000,   // 升级到版本3.0 壁纸功能
    3001 to ::updateTo3001,   // 升级到版本3.0.1 壁纸功能
    3100 to ::updateTo3100,     // 升级到3.1
    3300 to ::updateTo3300,     // 升级到3.3
    4000 to ::updateTo4000,     // 升级到4.0
    5_00_00_00 to ::updateTo50000,   // 升级到5.0.0
)

/**
 * 从0版本升级到1版本
 */
private suspend fun updateTo1() {
    logTagV(TAG, "从0升级到1")

    logTagD(TAG, "设定HDMI自动打开")
    withContext(Dispatchers.IO) {
        setBooleanSystemProp(KEY_HDMI_AUTO_OPEN, true)
    }

    when (Constants.starryHWInfo.salesLocale) {
        StarryDevLocale.Mainland -> {
            logTagV(TAG, "国内版本")
            delay(ONE_SECOND)
            setSoHoInputMethod()
        }

        StarryDevLocale.Overseas -> logTagI(TAG, "海外版本, 不需要设置输入法")
    }
}

/**
 * 升级到版本3
 */
private suspend fun updateTo3() {
    logTagV(TAG, "升级到版本3")
    logTagV(TAG, "更换Log框架")
    withContext(Dispatchers.IO) {
        val dataDir = File(Environment.getExternalStorageDirectory(), "Android/data")
        (dataDir.list() ?: emptyArray<String>()).filter {
            it.contains("starry", true)
        }.map {
            File(dataDir, "${it}/files/$CZUR_LOG_DIR_NAME")
        }.filter {
            it.exists() && (it.list()?.size ?: 0) > 0
        }.forEach { logRootFile ->
            logRootFile.walkTopDown().forEach { logFile ->
                if (logFile.name.startsWith("log") || logFile.extension == "log") {
                    // 删除旧log
                    logFile.delete()
                }
            }
        }
    }
}

/**
 * 升级到版本4
 */
private suspend fun updateTo4() {
    logTagD(TAG, "升级到版本4")
    // 更新DLNA相关设定
    withContext(Dispatchers.IO) {
        val eShareSDK = EShareServerSDK.getSingleton(appContext)
        val historyDeviceName = eShareSDK.deviceName
        logTagD(TAG, "之前的EShareName:${historyDeviceName}")
        eShareSDK.setDeviceName(historyDeviceName ?: DEF_E_SHARE_NAME)
        eShareSDK.isDlnaEnable = true   // 默认开启DLNA
    }
}

/**
 * 升级到版本5 (设置壁纸)
 */
private suspend fun updateTo3000() {
    logTagD(TAG, "升级到版本5")
    // 设置壁纸
    setWallpaper()
    resetBrightness()
    val eShareSDK = EShareServerSDK.getSingleton(appContext)
    changeMiracast(eShareSDK, false)
}

private suspend fun updateTo3001() {
    logTagD(TAG, "升级到版本3.0.1")
    val eShareSDK = EShareServerSDK.getSingleton(appContext)
    // 自动打开Miracast
    logTagD(TAG, "自动打开Miracast")
    changeMiracast(eShareSDK, true)
    // 关闭投屏码
    logTagD(TAG, "关闭投屏码")
    eShareSDK.pinCodeMode = PIN_CODE_MODE_DISABLE
    // 打开投屏悬浮窗
    logTagD(TAG, "关闭宜享悬浮窗")
    eShareSDK.isShowPinWindow = false
    logTagD(
        TAG,
        "设置设备名悬浮窗到Default:${SettingUtil.ShareScreenSetting.DEF_VALUE_ENABLE_NAME_ALERT_WIN}"
    )
    SettingUtil.ShareScreenSetting.setEnableNameAlertWin()
}

private suspend fun updateTo3100() {
    logTagD(TAG, "升级到版本3.1")
    // 设置Launcher的一些设定
    logTagD(TAG, "设置Launcher的一些设定")
    SettingUtil.PersonalizationSetting.setLauncherFavApps() // 设置默认常用应用

    // 设置拾音器参数
    logTagD(TAG, "设置拾音器参数")
    SettingUtil.CameraAndMicSetting.setAudioAlgoNSLevel()
    SettingUtil.CameraAndMicSetting.setAudioAlgoAGCLevel()

    // 设置输入法
    logTagD(TAG, "设置输入法")
    setGBoardInputMethod()

    if (Constants.starryHWInfo.salesLocale == StarryDevLocale.Overseas) {
        // 海外版本
        logTagD(TAG, "海外版本, 打开ChromeCast")
        val eShareSDK = EShareServerSDK.getSingleton(appContext)
        eShareSDK.isChromecastEnable = true
    }
}

private suspend fun updateTo3300() = withContext(Dispatchers.IO) {
    logTagD(TAG, "升级到版本3.3")
    logTagV(TAG, "写入SN到EShareSdk")
    val eShareSDK = EShareServerSDK.getSingleton(appContext)
    eShareSDK.setExtraInfo(Constants.SERIAL)

    logTagI(TAG, "修改噪声抑制选项")
    val current = SettingUtil.CameraAndMicSetting.getAudioAlgoNSLevel().value
    logTagV(TAG, "OTA之前:噪声抑制选项:${current}")
    when (current) {
        2 -> {
            // 之前是高
            logTagV(TAG, "之前是高, 保持高")
            SettingUtil.CameraAndMicSetting.setAudioAlgoNSLevel(SettingUtil.CameraAndMicSetting.AudioAlgoNSLevel.HIGH)
        }

        1 -> {
            // 之前是低
            logTagV(TAG, "之前是低, 调整为中")
            SettingUtil.CameraAndMicSetting.setAudioAlgoNSLevel(SettingUtil.CameraAndMicSetting.AudioAlgoNSLevel.MID)
        }

        else -> {
            // 之前是关
            logTagV(TAG, "之前是关, 保持关")
        }
    }
}

/**
 * 升级到版本4.0
 */
private suspend fun updateTo4000() = withContext(Dispatchers.IO) {
    logTagD(TAG, "升级到版本4.0")
    SettingUtil.WritePadSetting.setEnable() // 4.0版本默认开启手写板
}

private suspend fun updateTo50000() = withContext(Dispatchers.IO) {
    movePhotoData()
}

@VisibleForTesting
suspend fun movePhotoData() {
    val srcDir = File(Environment.getExternalStorageDirectory(), "Screenshots")
    val destDir = File(Environment.getExternalStorageDirectory(), "Pictures/Screenshots")
    withContext(Dispatchers.IO) {
        if (!destDir.exists()) {
            destDir.mkdirs()
        }
        // 将srcDir下的所有文件移动到destDir下
        srcDir.listFiles()?.forEach { file ->
            val destFile = File(destDir, file.name)
            logTagV(TAG, "移动文件:${file.absolutePath} -> ${destFile.absolutePath}")
            Files.move(file.toPath(), destFile.toPath(), StandardCopyOption.REPLACE_EXISTING)
        }
    }
}


private val rkDisplayOutputManager by lazy {
    RkDisplayOutputManagerProxy()
}

/**
 * 关闭Miracast
 */
private fun changeMiracast(eShareSDK: IEShareServerSDK, enable: Boolean) {
    logTagD(TAG, "更改Miracast状态:${enable}")
    eShareSDK.miracastEnable = enable
}

/**
 * 重置亮度
 */
private suspend fun resetBrightness() = withContext(Dispatchers.IO) {
    val brightness = rkDisplayOutputManager.getBrightness()
    logTagD(TAG, "亮度:$brightness")
    if (brightness == 100) {
        logTagD(TAG, "之前是高亮模式, 恢复成标准模式")
        rkDisplayOutputManager.setBrightness(50)
    } else {
        logTagD(TAG, "之前是标准模式, 不需要恢复")
    }
}

private suspend fun setWallpaper() {
    logTagD(TAG, "设置壁纸")
    withContext(Dispatchers.IO) {
        val wpm =
            appContext.getSystemService(Context.WALLPAPER_SERVICE) as WallpaperManager

        // 这里设置壁纸会拉伸, 一点一点测试的, 这个宽高可以正好填充全屏
        val bitmap = Bitmap.createBitmap(2240, 2240, Bitmap.Config.RGB_565)
        val canvas = Canvas(bitmap)
        val paint = Paint()
        val wallpaperId = when (Constants.versionIndustry) {
            VersionIndustry.PARTY_BUILDING -> R.raw.app_background_party_building
            else -> R.raw.app_background
        }
        val srcBitmap =
            BitmapFactory.decodeResource(appContext.resources, wallpaperId)
        when (Constants.versionIndustry) {
            VersionIndustry.UNIVERSAL -> canvas.drawColor(0xFF5879FC.toInt())
            VersionIndustry.PARTY_BUILDING -> canvas.drawColor(Color.WHITE)
            VersionIndustry.DEVICE_INDUSTRY_ARMY_BUILD -> canvas.drawColor(0xFF5879FC.toInt())
        }
        canvas.drawBitmap(srcBitmap, 0F, (bitmap.height - srcBitmap.height) / 2F, paint)

        wpm.setBitmap(bitmap)
    }
}

/**
 * 设置输入法为搜狗输入法
 */
private fun setSoHoInputMethod() {
    logTagD(TAG, "尝试将输入法设置为搜狗输入法")
    if (!checkHasSoHoInput()) {
        logTagW(TAG, "没有搜狗输入法, 不需要设置")
        return
    }
    val contentResolver = appContext.contentResolver
    Settings.Secure.putString(
        contentResolver,
        Settings.Secure.DEFAULT_INPUT_METHOD,
        "com.sohu.inputmethod.sogou/.SogouIME"
    )
    Settings.Secure.putString(
        contentResolver,
        Settings.Secure.ENABLED_INPUT_METHODS,
        "com.sohu.inputmethod.sogou/.SogouIME"
    )
}

/**
 * 设置输入法为GBoard输入法
 */
private suspend fun setGBoardInputMethod() = withContext(Dispatchers.IO) {
    logTagD(TAG, "尝试将输入法设置为GBoard输入法")
    if (!checkHasGBoardInput()) {
        logTagW(TAG, "没有GBoard输入法, 不需要设置")
        return@withContext
    }
    val contentResolver = appContext.contentResolver

    val enabledInputMethods = Settings.Secure.getString(
        contentResolver,
        Settings.Secure.ENABLED_INPUT_METHODS
    )
    if ("com.google.android.inputmethod.latin" in enabledInputMethods) {
        logTagD(TAG, "GBoard 已经启用, 不需要修改设置(当前输入法enable:$enabledInputMethods)")
    } else {
        logV(TAG, "GBoard 没有启用, 设置启用")
        Settings.Secure.putString(
            contentResolver,
            Settings.Secure.ENABLED_INPUT_METHODS,
            "com.google.android.inputmethod.latin/com.android.inputmethod.latin.LatinIME"
        )
    }

    val currentInputMethod = Settings.Secure.getString(
        contentResolver,
        Settings.Secure.DEFAULT_INPUT_METHOD
    )
    if ("com.google.android.inputmethod.latin" in currentInputMethod) {
        logTagD(TAG, "当前输入法已经是GBoard, 不需要设置(当前输入法:$currentInputMethod)")
        return@withContext
    } else {
        logV(TAG, "当前输入法不是GBoard, 设置为GBoard")
        Settings.Secure.putString(
            contentResolver,
            Settings.Secure.DEFAULT_INPUT_METHOD,
            "com.google.android.inputmethod.latin/com.android.inputmethod.latin.LatinIME"
        )
    }
}

/**
 * 检查搜狗输入法是否存在
 */
private fun checkHasSoHoInput(): Boolean = hasInstanceThisApp("com.sohu.inputmethod.sogou")

/**
 * 检查GBoard输入法是否存在
 */
private fun checkHasGBoardInput(): Boolean =
    hasInstanceThisApp("com.google.android.inputmethod.latin")