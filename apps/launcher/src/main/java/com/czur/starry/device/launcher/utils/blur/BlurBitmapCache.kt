package com.czur.starry.device.launcher.utils.blur

import android.graphics.Bitmap
import android.util.LruCache

private val MAX_CACHE_SIZE = (Runtime.getRuntime().maxMemory() / 8).toInt()


class BlurBitmapCache : LruCache<String, Bitmap?>(MAX_CACHE_SIZE) {

    override fun sizeOf(key: String?, value: Bitmap?): Int {
        return value?.let {
            it.rowBytes * value.height
        } ?: 0
    }
}