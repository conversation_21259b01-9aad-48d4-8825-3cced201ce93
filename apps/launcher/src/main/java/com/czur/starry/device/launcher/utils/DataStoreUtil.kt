package com.czur.starry.device.launcher.utils

import android.content.Context
import androidx.datastore.core.DataStore
import androidx.datastore.core.handlers.ReplaceFileCorruptionHandler
import androidx.datastore.preferences.core.MutablePreferences
import androidx.datastore.preferences.core.Preferences
import androidx.datastore.preferences.core.booleanPreferencesKey
import androidx.datastore.preferences.core.doublePreferencesKey
import androidx.datastore.preferences.core.edit
import androidx.datastore.preferences.core.emptyPreferences
import androidx.datastore.preferences.core.floatPreferencesKey
import androidx.datastore.preferences.core.intPreferencesKey
import androidx.datastore.preferences.core.longPreferencesKey
import androidx.datastore.preferences.core.stringPreferencesKey
import androidx.datastore.preferences.preferencesDataStore
import com.czur.czurutils.global.globalAppCtx
import com.czur.czurutils.log.logTagE
import com.czur.czurutils.log.logTagW
import com.czur.starry.device.baselib.NotImpl
import com.czur.starry.device.baselib.handler.createDefCorruptionHandler
import com.czur.starry.device.launcher.app.App
import kotlinx.coroutines.flow.first
import kotlinx.coroutines.flow.map
import java.io.File
import kotlin.properties.ReadOnlyProperty

/**
 * Created by 陈丰尧 on 2022/6/24
 */

val Context.dataStore: DataStore<Preferences> by preferencesDataStore(
    "LauncherConfig",
    corruptionHandler = createDefCorruptionHandler("LauncherConfig")
)

val dataStore: DataStore<Preferences> by lazy {
    App.context.dataStore
}

// 用户设置的密码
const val DS_KEY_MEETING_PWD = "meetingPwd"

// 引导用户上划的次数
const val DS_KEY_GUIDE_SLIDE_UP_TIMES = "slideUpTimes"

/**
 * 设置值
 */
suspend inline fun <reified T> setDSValue(key: String, value: T) {
    val stringKey = getDSKey<T>(key)
    dataStore.edit {
        it[stringKey] = value
    }
}

/**
 * 获取值
 */
suspend inline fun <reified T> getDSValue(key: String, defValue: T = dsDefValue()): T {
    val dsKey = getDSKey<T>(key)
    return dataStore.data.map {
        it[dsKey] ?: defValue
    }.first()
}

inline fun <reified T> dsDefValue(): T {
    return when (T::class.java) {
        String::class.java -> ""
        Integer::class.java, java.lang.Integer::class.java -> 0
        Long::class.java, java.lang.Long::class.java -> 0L
        Float::class.java, java.lang.Float::class.java -> 0F
        Double::class.java, java.lang.Double::class.java -> .0
        Boolean::class.java, java.lang.Boolean::class.java -> false
        else -> NotImpl()
    } as T
}

inline fun <reified T> getDSKey(key: String): Preferences.Key<T> {
    return when (T::class.java) {
        String::class.java -> {
            stringPreferencesKey(key)
        }

        Integer::class.java, java.lang.Integer::class.java -> {
            intPreferencesKey(key)
        }

        Long::class.java, java.lang.Long::class.java -> {
            longPreferencesKey(key)
        }

        Float::class.java, java.lang.Float::class.java -> {
            floatPreferencesKey(key)
        }

        Double::class.java, java.lang.Double::class.java -> {
            doublePreferencesKey(key)
        }

        Boolean::class.java, java.lang.Boolean::class.java -> {
            booleanPreferencesKey(key)
        }

        else -> NotImpl()
    } as Preferences.Key<T>
}

