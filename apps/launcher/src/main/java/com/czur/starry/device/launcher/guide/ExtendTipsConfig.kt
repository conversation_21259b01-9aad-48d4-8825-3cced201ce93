package com.czur.starry.device.launcher.guide

import com.czur.starry.device.baselib.utils.SettingUtil
import com.czur.starry.device.launcher.R

/**
 * Created by 陈丰尧 on 2023/7/6
 */
private val COMMON_TIPS_STR_CONFIG = ExtendTipsStrConfig(needAppName = false)
data class ExtendTipsStrConfig(
    val needAppName: Boolean
)

data class ExtendTipsConfig(
    val pkgName: String,
    val showTipsCount: Int,
    val tipsStrRes: Int,
    val tipsStrConfig: ExtendTipsStrConfig = COMMON_TIPS_STR_CONFIG,
    val tipsKey: String = pkgName,  // 存在多个应用共享提示次数的情况, 用tipsKey区分
    val showTipCondition: (suspend () -> Boolean) = { true }, // 展示提示的条件
)

private val videoMeetingTipsCondition = suspend {
    SettingUtil.CameraAndMicSetting.getAudioAlgoNSLevel() != SettingUtil.CameraAndMicSetting.AudioAlgoNSLevel.NONE
}
private const val VIDEO_MEETING_TIPS_COUNT = 10

/**
 * 扩展提示配置
 * 不同的应用, 在启动时展示不同的Toast提示
 */
val extendTipsConfigMap = listOf(
    ExtendTipsConfig("com.cmcc.android.ysx", showTipsCount = 5, R.string.toast_boot_hint_ysx),
    ExtendTipsConfig(
        "com.tencent.wemeet.app",
        showTipsCount = VIDEO_MEETING_TIPS_COUNT,
        R.string.toast_boot_hint_wemeet,
        ExtendTipsStrConfig(true),
        showTipCondition = videoMeetingTipsCondition
    ),
    ExtendTipsConfig(
        "com.microsoft.teams",
        showTipsCount = VIDEO_MEETING_TIPS_COUNT,
        R.string.toast_boot_hint_teams,
        ExtendTipsStrConfig(true),
        showTipCondition = videoMeetingTipsCondition
    ),
    ExtendTipsConfig(
        "com.ss.android.lark",
        showTipsCount = VIDEO_MEETING_TIPS_COUNT,
        R.string.toast_boot_hint_lark,
        ExtendTipsStrConfig(true),
        showTipCondition = videoMeetingTipsCondition
    ),
    ExtendTipsConfig(
        "us.zoom.videomeetings",
        showTipsCount = VIDEO_MEETING_TIPS_COUNT,
        R.string.toast_boot_hint_zoom,
        ExtendTipsStrConfig(true),
        showTipCondition = videoMeetingTipsCondition
    ),
).associateBy { it.pkgName }