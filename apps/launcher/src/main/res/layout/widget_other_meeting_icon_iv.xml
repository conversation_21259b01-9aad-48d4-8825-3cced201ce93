<?xml version="1.0" encoding="utf-8"?>
<merge xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:ignore="PxUsage">

    <androidx.constraintlayout.utils.widget.ImageFilterView
        android:id="@+id/iconIv"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:scaleType="fitXY"
        app:round="10px" />


    <ImageView
        android:id="@+id/downloadFlagIv"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center"
        android:src="@drawable/ic_net_meeting_flag_download" />

    <com.czur.starry.device.launcher.widget.OtherMeetingProcess
        android:id="@+id/otherDownloadProgressView"
        android:layout_width="71px"
        android:layout_height="71px"
        android:layout_gravity="center"
        android:visibility="gone" />
</merge>