<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/statusBgLayout"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:ignore="RtlHardcoded,PxUsage"
    tools:layout_height="140px">

    <FrameLayout
        android:id="@+id/logoFl"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <ImageView
            android:id="@+id/starryMeetingLogoIv"
            android:layout_width="260px"
            android:layout_height="40px"
            android:layout_marginLeft="60px"
            android:layout_marginTop="28px"
            android:src="@drawable/icon_main_page_logo_starry_meeting" />

        <ImageView
            android:id="@+id/starryHubLogoIv"
            android:layout_width="197px"
            android:layout_height="39px"
            android:layout_marginLeft="26px"
            android:layout_marginTop="22px"
            android:src="@drawable/icon_main_page_logo_starry_hub"
            android:visibility="gone" />

        <TextView
            android:id="@+id/meetingRoomNameTV"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginLeft="26px"
            android:layout_marginTop="22px"
            android:textColor="@color/text_white"
            android:background="@android:color/transparent"
            android:textSize="32px"
            android:textStyle="bold"
            android:singleLine="true"
            android:includeFontPadding="false"
            android:padding="0px"
            android:shadowColor="#000000"
            android:shadowDx="0"
            android:shadowDy="0"
            android:shadowRadius="2"/>

    </FrameLayout>

    <!--  进行时候的动画  -->
    <ImageView
        android:id="@+id/aiCCAnimIv"
        android:layout_width="172px"
        android:layout_height="60px"
        android:layout_marginBottom="-9px"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="@id/logoFl"
        app:layout_constraintLeft_toRightOf="@id/logoFl"/>

    <ImageView
        android:id="@+id/aiCCIv"
        android:layout_width="80px"
        android:layout_height="30px"
        android:layout_marginLeft="24px"
        android:layout_marginBottom="7px"
        android:src="@drawable/ic_trans_stop"
        app:layout_constraintBottom_toBottomOf="@id/logoFl"
        app:layout_constraintLeft_toRightOf="@id/logoFl" />

    <ImageView
        android:id="@+id/aiCCIvBtn"
        android:layout_width="30px"
        android:layout_height="30px"
        android:layout_marginLeft="14px"
        app:layout_constraintBottom_toBottomOf="@id/aiCCIv"
        app:layout_constraintLeft_toRightOf="@id/aiCCIv"
        app:layout_constraintTop_toTopOf="@+id/aiCCIv"
        app:float_tips="@string/tips_aicc_click_stop" />
    />


    <LinearLayout
        android:id="@+id/czNotificationLayout"
        android:layout_width="wrap_content"
        android:layout_height="56px"
        android:layout_marginTop="10px"
        android:layout_marginLeft="20px"
        android:baselineAligned="false"
        android:gravity="center"
        android:orientation="horizontal"
        android:paddingHorizontal="24px"
        app:bl_corners_radius="10px"
        app:bl_solid_color="@color/white"
        app:layout_constraintBottom_toBottomOf="@id/logoFl"
        app:layout_constraintLeft_toRightOf="@id/aiCCIv"
        app:layout_constraintTop_toTopOf="@id/logoFl"
        tools:background="@color/white">

        <ImageView
            android:id="@+id/czNotificationIconIv"
            android:layout_width="40px"
            android:layout_height="40px" />

        <TextView
            android:id="@+id/czNotificationTitleTv"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginLeft="15px"
            android:textColor="@color/black"
            android:textSize="18px"
            android:textStyle="bold"
            tools:text="Starry OS 5.0新版本" />

    </LinearLayout>


    <TextView
        android:id="@+id/hdmiStatusTv"
        android:layout_width="wrap_content"
        android:layout_height="22px"
        android:layout_marginTop="19px"
        android:layout_marginRight="30px"
        android:background="@drawable/bg_hdmi_off"
        android:gravity="center"
        android:includeFontPadding="false"
        android:paddingHorizontal="10px"
        android:text="@string/str_quick_boot_hdmi"
        android:textColor="@color/white"
        android:textSize="16px"
        android:textStyle="bold"
        app:layout_constraintRight_toLeftOf="@id/notify_bar"
        app:layout_constraintTop_toTopOf="parent" />

    <LinearLayout
        android:id="@+id/byomInUseLayout"
        android:layout_width="wrap_content"
        android:layout_height="40px"
        android:layout_marginTop="20px"
        android:background="@drawable/bg_byom_on"
        android:baselineAligned="false"
        android:orientation="horizontal"
        android:paddingHorizontal="15px"
        android:visibility="gone"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <com.czur.starry.device.baselib.widget.CircleView
            android:layout_width="9px"
            android:layout_height="9px"
            android:layout_gravity="center_vertical"
            app:breathEffect="true" />

        <TextView
            android:id="@+id/peripheralModeNameTv"
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:layout_marginLeft="10px"
            android:gravity="center"
            android:includeFontPadding="false"
            android:text="@string/btn_status_bar_peripheral_byom_in_use"
            android:textColor="@color/white"
            android:textSize="18px"
            android:textStyle="bold" />

    </LinearLayout>

    <com.czur.starry.device.baselib.widget.NotifyBar
        android:id="@+id/notify_bar"
        android:layout_width="wrap_content"
        android:layout_height="60px"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

</androidx.constraintlayout.widget.ConstraintLayout>