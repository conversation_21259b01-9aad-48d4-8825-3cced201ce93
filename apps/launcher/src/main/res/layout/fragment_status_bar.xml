<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/statusBgLayout"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:ignore="RtlHardcoded,PxUsage"
    tools:layout_height="140px">

    <FrameLayout
        android:id="@+id/logoFl"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <ImageView
            android:id="@+id/starryHubLogoIv"
            android:layout_width="197px"
            android:layout_height="39px"
            android:layout_marginLeft="26px"
            android:layout_marginTop="22px"
            android:src="@drawable/icon_main_page_logo_starry_hub" />

        <TextView
            android:id="@+id/meetingRoomNameTV"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginLeft="26px"
            android:layout_marginTop="22px"
            android:background="@android:color/transparent"
            android:includeFontPadding="false"
            android:padding="0px"
            android:shadowColor="#000000"
            android:shadowDx="0"
            android:shadowDy="0"
            android:shadowRadius="2"
            android:singleLine="true"
            android:textColor="@color/text_white"
            android:textSize="32px"
            android:textStyle="bold" />

    </FrameLayout>


    <LinearLayout
        android:id="@+id/byomInUseLayout"
        android:layout_width="wrap_content"
        android:layout_height="40px"
        android:layout_marginTop="20px"
        android:background="@drawable/bg_byom_on"
        android:baselineAligned="false"
        android:orientation="horizontal"
        android:paddingHorizontal="15px"
        android:visibility="gone"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <com.czur.starry.device.baselib.widget.CircleView
            android:layout_width="9px"
            android:layout_height="9px"
            android:layout_gravity="center_vertical"
            app:breathEffect="true" />

        <TextView
            android:id="@+id/peripheralModeNameTv"
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:layout_marginLeft="10px"
            android:gravity="center"
            android:includeFontPadding="false"
            android:text="@string/btn_status_bar_peripheral_byom_in_use"
            android:textColor="@color/white"
            android:textSize="18px"
            android:textStyle="bold" />

    </LinearLayout>


    <LinearLayout
        android:id="@+id/czNotificationLayout"
        android:layout_width="wrap_content"
        android:layout_height="56px"
        android:layout_marginLeft="20px"
        android:layout_marginTop="10px"
        android:baselineAligned="false"
        android:gravity="center"
        android:orientation="horizontal"
        android:paddingHorizontal="24px"
        app:bl_corners_radius="10px"
        app:bl_solid_color="@color/white"
        tools:background="@color/white">

        <ImageView
            android:id="@+id/czNotificationIconIv"
            android:layout_width="40px"
            android:layout_height="40px" />

        <TextView
            android:id="@+id/czNotificationTitleTv"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginLeft="15px"
            android:textColor="@color/black"
            android:textSize="18px"
            android:textStyle="bold"
            tools:text="Starry OS 5.0新版本" />

    </LinearLayout>

    <ImageView
        android:id="@+id/voiceAssistantIv"
        android:layout_width="wrap_content"
        android:layout_height="60px"
        android:layout_marginRight="30px"
        android:src="@drawable/icon_voice_assistant" />


    <TextView
        android:id="@+id/hdmiStatusTv"
        android:layout_width="wrap_content"
        android:layout_height="22px"
        android:layout_marginTop="19px"
        android:layout_marginRight="30px"
        android:gravity="center"
        android:includeFontPadding="false"
        android:paddingHorizontal="10px"
        android:text="@string/str_quick_boot_hdmi"
        android:textColor="@color/white"
        android:textSize="18px"
        android:textStyle="bold" />

    <!--  进行时候的动画  -->


    <ImageView
        android:id="@+id/aiCCIv"
        android:layout_width="86px"
        android:layout_height="30px"
        android:layout_gravity="center"
        android:scaleType="fitXY"
        android:src="@drawable/ic_trans_stop" />


    <ImageView
        android:id="@+id/aiCCAnimIv"
        android:layout_width="172px"
        android:layout_height="60px"
        android:layout_gravity="right|center_vertical"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="@id/aiCCIv"
        app:layout_constraintLeft_toLeftOf="@id/aiCCIv"
        app:layout_constraintRight_toRightOf="@id/aiCCIv"
        app:layout_constraintTop_toTopOf="@id/aiCCIv" />

    <ImageView
        android:id="@+id/aiCCIvBtn"
        android:layout_width="35px"
        android:layout_height="60px"
        android:layout_gravity="right"
        android:layout_marginRight="23px"
        app:float_tips="@string/tips_aicc_click_stop"
        app:layout_constraintBottom_toBottomOf="@id/aiCCAnimIv"
        app:layout_constraintRight_toRightOf="@id/aiCCAnimIv"
        app:layout_constraintTop_toTopOf="@id/aiCCAnimIv" />


    <ImageView
        android:id="@+id/notifyBarMsgIv"
        android:layout_width="30px"
        android:layout_height="60px"
        android:paddingVertical="19px"
        android:src="@drawable/baselib_icon_msg"
        app:float_tips="@string/float_tip_notice_msg" />

    <com.czur.starry.device.baselib.widget.CircleView
        android:id="@+id/notifyBarUnReadPoint"
        android:layout_width="8px"
        android:layout_height="8px"
        android:layout_marginTop="15px"
        android:layout_marginRight="-2px"
        android:visibility="gone"
        app:circleColor="@color/notice_read"
        app:layout_constraintRight_toRightOf="@id/notifyBarMsgIv"
        app:layout_constraintTop_toTopOf="@id/notifyBarMsgIv" />

    <com.czur.starry.device.baselib.widget.NetStatusIcon
        android:id="@+id/notifyBarWifiIv"
        android:layout_width="wrap_content"
        android:layout_height="60px"
        android:src="@drawable/baselib_icon_wifi"
        app:float_tips="@string/float_tip_network_info" />

    <LinearLayout
        android:id="@+id/personalCenterGroup"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:gravity="center_vertical"
        android:orientation="horizontal"
        app:float_tips="@string/float_tip_personal_center">

        <ImageView
            android:id="@+id/notifyBarUserIv"
            android:layout_width="20px"
            android:layout_height="60px"
            android:paddingVertical="19px"
            android:src="@drawable/baselib_icon_user" />

        <TextView
            android:id="@+id/notifyBarUserTv"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginLeft="10px"
            android:includeFontPadding="false"
            android:paddingBottom="3px"
            android:textColor="@color/white"
            android:textSize="24px"
            tools:text="0000001" />
    </LinearLayout>


    <androidx.constraintlayout.helper.widget.Flow
        android:layout_width="wrap_content"
        android:layout_height="60px"
        android:layout_marginRight="60px"
        android:orientation="horizontal"
        app:constraint_referenced_ids="czNotificationLayout,voiceAssistantIv,aiCCIv,hdmiStatusTv,notifyBarMsgIv,notifyBarWifiIv,personalCenterGroup"
        app:flow_horizontalGap="32px"
        app:flow_horizontalStyle="packed"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

</androidx.constraintlayout.widget.ConstraintLayout>