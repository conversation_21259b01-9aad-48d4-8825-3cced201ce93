<?xml version="1.0" encoding="utf-8"?>
<merge xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/quickBootLT"
    tools:ignore="PxUsage,RtlHardcoded"
    tools:layout_height="218px"
    tools:layout_width="312px">

    <com.czur.starry.device.launcher.widget.BlurImageView
        android:id="@+id/quickBootBiv"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_margin="1px"/>

    <com.czur.starry.device.launcher.widget.LauncherMainMarkView
        android:id="@+id/quickBootMarkView"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        />

    <ImageView
        android:id="@+id/quickBootLogoIv"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        android:layout_marginTop="25px"
        android:layout_marginLeft="25px"
        tools:ignore="RtlHardcoded" />


    <TextView
        android:id="@+id/shareNameTv"
        android:layout_width="0px"
        android:layout_height="wrap_content"
        android:ellipsize="end"
        android:gravity="left"
        android:singleLine="true"
        android:textColor="@color/white"
        android:textSize="20px"
        android:textStyle="bold"
        android:visibility="gone"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        android:layout_marginLeft="32px"
        android:layout_marginTop="112px"
        tools:text="StarryHub-市场" />

    <LinearLayout
        android:id="@+id/wifiNameLl"
        android:layout_width="0px"
        android:layout_height="wrap_content"
        android:baselineAligned="false"
        android:gravity="center_vertical"
        android:orientation="horizontal"
        android:paddingVertical="5px"
        android:visibility="gone"
        app:layout_constraintLeft_toLeftOf="@id/shareNameTv"
        app:layout_constraintTop_toBottomOf="@id/shareNameTv">

        <ImageView
            android:id="@+id/wifiNameIv"
            android:layout_width="20px"
            android:layout_height="14px"
            android:src="@drawable/ic_quick_boot_share_wifi" />

        <TextView
            android:id="@+id/wifiNameTv"
            android:layout_width="0px"
            android:layout_height="wrap_content"
            android:layout_marginLeft="7px"
            android:layout_weight="1"
            android:ellipsize="end"
            android:singleLine="true"
            android:textColor="@color/white"
            android:textSize="20px"
            android:textStyle="bold"
            tools:text="CZUR-5G" />

        <TextView
            android:id="@+id/ethernetTv"
            android:layout_width="0px"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:ellipsize="end"
            android:text="@string/quick_boot_screen_share_ethernet"
            android:textColor="@color/white"
            android:textSize="20px"
            android:textStyle="bold"
            android:visibility="gone" />
    </LinearLayout>

    <View
        android:id="@+id/quickBootBadgePoint"
        android:layout_marginLeft="83px"
        android:layout_width="12px"
        android:layout_height="12px"
        android:visibility="invisible"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        android:elevation="30px"
        android:background="@drawable/bg_quick_red"
        android:layout_marginTop="30px"/>

    <TextView
        android:id="@+id/quickBootTitleTv"
        style="@style/short_cut_app_tv"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        tools:text="文件" />


    <TextView
        android:id="@+id/fileCodeTitleTv"
        style="@style/file_code_title_tv"
        android:text="@string/quick_boot_file_code"
        android:visibility="gone"
        android:gravity="right"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintBottom_toTopOf="@id/fileCodeContentTv" />

    <TextView
        android:id="@+id/fileCodeContentTv"
        style="@style/file_code_content_tv"
        android:visibility="gone"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintBottom_toBottomOf="@id/quickBootTitleTv"/>

</merge>