@Suppress("DSL_SCOPE_VIOLATION") // TODO: Remove once KTIJ-19369 is fixed
plugins {
    alias(libs.plugins.application)
    alias(libs.plugins.kotlinAndroid)
}

private val pkgName = "com.czur.starry.device.localmeetingrecord"
private val apkName = "CZLocalMeetingRecord"
android.buildFeatures.buildConfig = true
android {
    namespace = pkgName
    compileSdk = libs.versions.compileSdkVersion.get().toInt()

    defaultConfig {
        applicationId = namespace
        minSdk = libs.versions.minSdkVersion.get().toInt()
        targetSdk = libs.versions.targetSdkVersion.get().toInt()

        versionCode = libs.versions.appVersionCode.get().toInt()
        versionName = libs.versions.appVersionName.get()

        testInstrumentationRunner = "androidx.test.runner.AndroidJUnitRunner"
        renderscriptSupportModeEnabled = true

        setFlavorDimensions(listOf("constantEnv"))
        manifestPlaceholders["atyPlaceHolder"] = rootProject.ext["atyConfigChange"].toString()
    }

    // 签名
    signingConfigs {
        create("keyStore") {
            storeFile = file("${rootDir}/signature/starry.jks")
            storePassword = rootProject.ext["storePassword"].toString()
            keyAlias = rootProject.ext["keyAlias"].toString()
            keyPassword = rootProject.ext["keyPassword"].toString()
        }
    }

    buildTypes {
        val signConfig = signingConfigs.getByName("keyStore")
        debug {
            signingConfig = signConfig
            isMinifyEnabled = false
        }
        create("unsigned") {
            signingConfig = signConfig
            isDebuggable = false
            isMinifyEnabled = false
        }

    }

    productFlavors {
        create("envProduct") {
            dimension = "constantEnv"
            buildConfigField("Integer", "CONSTANT_ENV", "0")
        }
        create("envTest") {
            dimension = "constantEnv"
            buildConfigField("Integer", "CONSTANT_ENV", "1")
        }
    }

    externalNativeBuild {
        this.cmake {
            path = file("CMakeLists.txt")
        }
    }

    compileOptions {
        sourceCompatibility = JavaVersion.VERSION_11
        targetCompatibility = JavaVersion.VERSION_11
    }
    kotlinOptions {
        jvmTarget = "11"
    }

    buildFeatures {
        viewBinding = true
    }

    android.applicationVariants.all {
        outputs.all {
            if (this is com.android.build.gradle
                .internal.api.ApkVariantOutputImpl
            ) {
                this.outputFileName = "${apkName}.apk"
            }
        }
    }
}

dependencies {
    // 测试依赖
    testImplementation(libs.junit)
    androidTestImplementation(libs.bundles.androidTest)

    implementation(project(":baselib"))
    implementation(project(":base:noticeLib"))
    implementation(project(":base:meetlib"))
    implementation(project(":base:eShareLib"))
    implementation(project(":base:UILib")) // 依赖base库的UILib
    implementation(project(":aars:renderscriptToolkit"))
}