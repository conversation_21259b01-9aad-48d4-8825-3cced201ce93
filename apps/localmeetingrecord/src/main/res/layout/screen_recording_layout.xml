<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/black">

    <TextView
        android:id="@+id/textView3"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/str_is_recording_screen"
        android:textColor="@color/white"
        android:textSize="72px"
        app:layout_constraintBottom_toTopOf="@+id/screenRecordingDuringTv"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintHorizontal_bias="0.5"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintVertical_chainStyle="packed" />

    <TextView
        android:id="@+id/screenRecordingDuringTv"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="19px"
        android:text="00:00:00"
        android:textColor="@color/white"
        android:textSize="36px"
        app:layout_constraintBottom_toTopOf="@+id/controlRecordingIv"
        app:layout_constraintEnd_toEndOf="@+id/textView3"
        app:layout_constraintHorizontal_bias="0.5"
        app:layout_constraintStart_toStartOf="@+id/textView3"
        app:layout_constraintTop_toBottomOf="@+id/textView3" />

    <ImageView
        android:id="@+id/controlRecordingIv"
        android:layout_width="140px"
        android:layout_height="140px"
        android:layout_marginTop="80px"
        android:background="@drawable/pause_screen_recording"
        app:layout_constraintBottom_toTopOf="@+id/controlRecordingTv"
        app:layout_constraintEnd_toStartOf="@+id/stopScreenRecordingIv"
        app:layout_constraintHorizontal_bias="0.5"
        app:layout_constraintHorizontal_chainStyle="packed"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/screenRecordingDuringTv" />

    <ImageView
        android:id="@+id/stopScreenRecordingIv"
        android:layout_width="140px"
        android:layout_height="140px"
        android:layout_marginLeft="216px"
        android:background="@drawable/stop_screen_recording"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintHorizontal_bias="0.5"
        app:layout_constraintStart_toEndOf="@+id/controlRecordingIv"
        app:layout_constraintTop_toTopOf="@+id/controlRecordingIv" />

    <TextView
        android:id="@+id/controlRecordingTv"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="29px"
        android:text="@string/str_pause_record_screen"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="@+id/controlRecordingIv"
        app:layout_constraintHorizontal_bias="0.5"
        app:layout_constraintStart_toStartOf="@+id/controlRecordingIv"
        app:layout_constraintTop_toBottomOf="@+id/controlRecordingIv" />

    <TextView
        android:id="@+id/textView5"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="29px"
        android:text="@string/str_stop_record_screen"
        app:layout_constraintEnd_toEndOf="@+id/stopScreenRecordingIv"
        app:layout_constraintStart_toStartOf="@+id/stopScreenRecordingIv"
        app:layout_constraintTop_toBottomOf="@+id/stopScreenRecordingIv" />
</androidx.constraintlayout.widget.ConstraintLayout>