<resources xmlns:tools="http://schemas.android.com/tools">
    <string name="app_name">Grabar reunión local</string>
    <string name="str_time_watermark">Marca de agua de tiempo</string>
    <string name="toast_video_file_saved">Se ha guardado %s archivo de grabación.</string>
    <string name="toast_video_file_save_failed">Error al guardar el archivo de grabación.</string>
    <string name="toast_storage_not_enough_start_record">No se puede grabar. Quedan menos de 500 MB \nde espacio de almacenamiento.</string>
    <string name="toast_storage_not_enough_start_camera">No se puede hacer la foto. Quedan menos de 500 MB de espacio de almacenamiento.</string>
    <string name="toast_rec_storage_not_enough">La grabación finalizará en 5 minutos porque quedan menos de 500 MB de espacio de almacenamiento.</string>
    <string name="toast_rec_storage_stop_recording">Espacio de almacenamiento restante insuficiente.\nLa grabación ha terminado.</string>
    <string name="toast_rec_time_not_enough">Recordatorio: la grabación finalizará en %s minutos. Guarde el archivo de grabación lo antes posible.</string>
    <string name="tips_camera_mode">Modo de grabación de vídeo</string>
    <string name="tips_screen_mode">Modo de grabación de pantalla</string>
    <string name="tips_audio_mode">Modo de grabación de audio</string>
    <string name="toast_conflict_with_meeting">No se puede iniciar la grabación de la reunión local porque la videoconferencia está en curso.</string>
    <string name="dialog_mic_occupy_hint">%s está usando el micrófono. ¿Le gustaría silenciarlo y empezar a grabar?</string>
    <string name="dialog_mic_occupy_hint_init">%s está usando el micrófono. ¿Le gustaría silenciarlo e iniciar la reunión local?</string>
    <string name="dialog_stop_record_hint">¿Finalizar grabación?</string>
    <string name="dialog_refuse_eshare">No se puede presentar la pantalla porque la grabación de \nla videoconferencia está en curso.</string>
    <string name="str_is_recording_audio">Grabación de audio en curso.</string>
    <string name="str_is_recording_screen">Grabación de pantalla en curso.</string>
    <string name="str_resume_record_screen">Continuar grabación.</string>
    <string name="str_pause_record_screen">Pausar grabación.</string>
    <string name="str_stop_record_screen">Grabación completada.</string>
    <string name="str_can_record_background">Grabación de fondo</string>
    <string name="toast_recording_fail">Error al iniciar la grabación. Inténtelo de nuevo.</string>
    <string name="toast_init_camera_fail">Error al activar la cámara. Reinicie la aplicación.</string>
    <string name="str_function_simultaneousRecording_pictureInPicture">Grabar vídeo (PIP)</string>
    <string name="str_function_simultaneousRecording">Grabar audio</string>
    <string name="str_function_recorder">Grabar audio</string>
    <string name="str_function_screen_recorder">Grabar pantalla</string>
    <string name="str_function_video">Grabar vídeo</string>
    <string name="str_function_photo">Hacer una foto</string>
    <string name="str_rename">Cambiar nombre</string>
    <string name="str_rename_inputet">Introduzca el nombre del archivo</string>
    <string name="toast_init_camera_fail_100">No se ha podido iniciar la cámara. Reinicie la aplicación.</string>
    <string name="str_save_file">Cambiar nombre de archivo</string>
    <string name="str_alert_dialog_title">Notificación</string>
    <string name="str_dialog_sure">Aceptar</string>
    <string name="str_dialog_cancel">Cancelar</string>
    <string name="str_flip_lens">Duplicación de vídeo</string>
    <string name="toast_record_error">La grabación del video falló. Por favor, vuelva a intentarlo.</string>
</resources>
