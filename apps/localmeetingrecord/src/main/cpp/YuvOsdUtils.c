#include <stdlib.h>
#include <stdio.h>
#include <string.h>
#include <sys/time.h>
#include <jni.h>
#include <stdint.h>
#include <android/log.h>

#define TAG    "YuvOsd" // 这个是自定义的LOG的标识
#define LOGD(...)  __android_log_print(ANDROID_LOG_DEBUG,TAG,__VA_ARGS__) // 定义LOGD类型


#ifdef __cplusplus
extern "C" {
#endif

void NV21ToNV12(jbyte *nv21, int width, int height) {
    int framesize = width * height;
    int j = 0;
    int end = framesize + framesize / 2;
    jbyte temp = 0;
    for (j = framesize; j < end; j += 2)//u
    {
        temp = nv21[j];
        nv21[j] = nv21[j + 1];
        nv21[j + 1] = temp;
    }

}

int off_x, off_y;//x 偏移y 偏移
jint num_width, num_height;//数字宽高
int date_len, rotation;
int waterFrameWidth, waterFrameHeight, waterFrameSize;
int frame_width, frame_height;

char *mNumArrays;
size_t size;

JNIEXPORT void JNICALL
Java_com_czur_starry_device_localmeetingrecord_jni_YuvOsdUtils_initOsd(JNIEnv *env, jclass type,
                                                                       jint osdOffX,
                                                                       jint osdOffY,
                                                                       jint patternLen,
                                                                       jint frameWidth,
                                                                       jint frameHeight,
                                                                       jint rotation_angle) {
    off_x = osdOffX;
    off_y = osdOffY;

    num_width = 14;
    num_height = 16;
    date_len = patternLen;
    frame_width = frameWidth;
    frame_height = frameHeight;
    rotation = rotation_angle;

    waterFrameWidth = num_width * patternLen;
    waterFrameHeight = num_height;
    waterFrameSize = waterFrameWidth * waterFrameHeight;
    mNumArrays = calloc(date_len, num_width * num_height * sizeof(char));

    //这里数字水印数据是通过生成图片转换来的
    char NUM_0[] = {0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0,
                    0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0,
                    0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0,
                    0, 0, 1, 1, 1, 0, 0, 0, 0, 1, 1, 1, 0, 0,
                    0, 0, 1, 1, 1, 0, 0, 0, 0, 1, 1, 1, 0, 0,
                    0, 0, 1, 1, 1, 0, 0, 0, 0, 1, 1, 1, 0, 0,
                    0, 0, 1, 1, 1, 0, 0, 0, 0, 1, 1, 1, 0, 0,
                    0, 0, 1, 1, 1, 0, 0, 0, 0, 1, 1, 1, 0, 0,
                    0, 0, 1, 1, 1, 0, 0, 0, 0, 1, 1, 1, 0, 0,
                    0, 0, 1, 1, 1, 0, 0, 0, 0, 1, 1, 1, 0, 0,
                    0, 0, 1, 1, 1, 0, 0, 0, 0, 1, 1, 1, 0, 0,
                    0, 0, 1, 1, 1, 0, 0, 0, 0, 1, 1, 1, 0, 0,
                    0, 0, 1, 1, 1, 0, 0, 0, 0, 1, 1, 1, 0, 0,
                    0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0,
                    0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0,
                    0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0};
    char NUM_1[] = {0, 0, 0, 0, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0,
                    0, 0, 0, 0, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0,
                    0, 0, 0, 0, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0,
                    0, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0, 0, 0, 0,
                    0, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0, 0, 0, 0,
                    0, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0, 0, 0, 0,
                    0, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0, 0, 0, 0,
                    0, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0, 0, 0, 0,
                    0, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0, 0, 0, 0,
                    0, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0, 0, 0, 0,
                    0, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0, 0, 0, 0,
                    0, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0, 0, 0, 0,
                    0, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0, 0, 0, 0,
                    0, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0, 0, 0, 0,
                    0, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0, 0, 0, 0,
                    0, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0, 0, 0, 0};
    char NUM_2[] = {0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0,
                    0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0,
                    0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0,
                    0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0,
                    0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0,
                    0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0,
                    0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0,
                    0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0,
                    0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0,
                    0, 0, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0,
                    0, 0, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0,
                    0, 0, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0,
                    0, 0, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0,
                    0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0,
                    0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0,
                    0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0};
    char NUM_3[] = {0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0,
                    0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0,
                    0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0,
                    0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0,
                    0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0,
                    0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0,
                    0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0,
                    0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0,
                    0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0,
                    0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0,
                    0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0,
                    0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0,
                    0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0,
                    0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0,
                    0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0,
                    0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0};
    char NUM_4[] = {0, 0, 1, 1, 1, 0, 0, 0, 0, 1, 1, 1, 0, 0,
                    0, 0, 1, 1, 1, 0, 0, 0, 0, 1, 1, 1, 0, 0,
                    0, 0, 1, 1, 1, 0, 0, 0, 0, 1, 1, 1, 0, 0,
                    0, 0, 1, 1, 1, 0, 0, 0, 0, 1, 1, 1, 0, 0,
                    0, 0, 1, 1, 1, 0, 0, 0, 0, 1, 1, 1, 0, 0,
                    0, 0, 1, 1, 1, 0, 0, 0, 0, 1, 1, 1, 0, 0,
                    0, 0, 1, 1, 1, 0, 0, 0, 0, 1, 1, 1, 0, 0,
                    0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0,
                    0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0,
                    0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0,
                    0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0,
                    0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0,
                    0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0,
                    0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0,
                    0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0,
                    0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0};
    char NUM_5[] = {0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0,
                    0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0,
                    0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0,
                    0, 0, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0,
                    0, 0, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0,
                    0, 0, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0,
                    0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0,
                    0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0,
                    0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0,
                    0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0,
                    0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0,
                    0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0,
                    0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0,
                    0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0,
                    0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0,
                    0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0};
    char NUM_6[] = {0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0,
                    0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0,
                    0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0,
                    0, 0, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0,
                    0, 0, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0,
                    0, 0, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0,
                    0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0,
                    0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0,
                    0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0,
                    0, 0, 1, 1, 1, 0, 0, 0, 0, 1, 1, 1, 0, 0,
                    0, 0, 1, 1, 1, 0, 0, 0, 0, 1, 1, 1, 0, 0,
                    0, 0, 1, 1, 1, 0, 0, 0, 0, 1, 1, 1, 0, 0,
                    0, 0, 1, 1, 1, 0, 0, 0, 0, 1, 1, 1, 0, 0,
                    0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0,
                    0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0,
                    0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0};
    char NUM_7[] = {0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0,
                    0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0,
                    0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0,
                    0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0,
                    0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0,
                    0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0,
                    0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0,
                    0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0,
                    0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0,
                    0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0,
                    0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0,
                    0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0,
                    0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0,
                    0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0,
                    0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0,
                    0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0};
    char NUM_8[] = {0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0,
                    0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0,
                    0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0,
                    0, 0, 1, 1, 1, 0, 0, 0, 0, 1, 1, 1, 0, 0,
                    0, 0, 1, 1, 1, 0, 0, 0, 0, 1, 1, 1, 0, 0,
                    0, 0, 1, 1, 1, 0, 0, 0, 0, 1, 1, 1, 0, 0,
                    0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0,
                    0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0,
                    0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0,
                    0, 0, 1, 1, 1, 0, 0, 0, 0, 1, 1, 1, 0, 0,
                    0, 0, 1, 1, 1, 0, 0, 0, 0, 1, 1, 1, 0, 0,
                    0, 0, 1, 1, 1, 0, 0, 0, 0, 1, 1, 1, 0, 0,
                    0, 0, 1, 1, 1, 0, 0, 0, 0, 1, 1, 1, 0, 0,
                    0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0,
                    0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0,
                    0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0};
    char NUM_9[] = {0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0,
                    0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0,
                    0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0,
                    0, 0, 1, 1, 1, 0, 0, 0, 0, 1, 1, 1, 0, 0,
                    0, 0, 1, 1, 1, 0, 0, 0, 0, 1, 1, 1, 0, 0,
                    0, 0, 1, 1, 1, 0, 0, 0, 0, 1, 1, 1, 0, 0,
                    0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0,
                    0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0,
                    0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0,
                    0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0,
                    0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0,
                    0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0,
                    0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0,
                    0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0,
                    0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0,
                    0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0};
    char NUM_COLON[] = {0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
                        0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
                        0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
                        0, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0, 0, 0, 0,
                        0, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0, 0, 0, 0,
                        0, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0, 0, 0, 0,
                        0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
                        0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
                        0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
                        0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
                        0, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0, 0, 0, 0,
                        0, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0, 0, 0, 0,
                        0, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0, 0, 0, 0,
                        0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
                        0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
                        0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0};
    char NUM_LINE[] = {0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
                       0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
                       0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
                       0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
                       0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
                       0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
                       0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0,
                       0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0,
                       0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0,
                       0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
                       0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
                       0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
                       0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
                       0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
                       0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
                       0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0};
    char NUM_SPACE[] = {0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
                        0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
                        0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
                        0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
                        0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
                        0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
                        0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
                        0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
                        0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
                        0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
                        0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
                        0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
                        0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
                        0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
                        0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
                        0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0};

    char NUM_SLASH[] = {0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
                        0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0,
                        0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0, 0,
                        0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0, 0,
                        0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0, 0, 0,
                        0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0, 0, 0,
                        0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0,
                        0, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0, 0, 0, 0,
                        0, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0, 0, 0, 0,
                        0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0,
                        0, 0, 0, 0, 0, 1, 1, 1, 0, 0, 0, 0, 0, 0,
                        0, 0, 0, 0, 0, 1, 1, 1, 0, 0, 0, 0, 0, 0,
                        0, 0, 0, 0, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0,
                        0, 0, 0, 0, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0,
                        0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0,
                        0, 0, 0, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0};


    size = (size_t) num_width * num_height;
    memcpy(mNumArrays, NUM_0, size);
    memcpy(mNumArrays + size * 1, NUM_1, size);
    memcpy(mNumArrays + size * 2, NUM_2, size);
    memcpy(mNumArrays + size * 3, NUM_3, size);
    memcpy(mNumArrays + size * 4, NUM_4, size);
    memcpy(mNumArrays + size * 5, NUM_5, size);
    memcpy(mNumArrays + size * 6, NUM_6, size);
    memcpy(mNumArrays + size * 7, NUM_7, size);
    memcpy(mNumArrays + size * 8, NUM_8, size);
    memcpy(mNumArrays + size * 9, NUM_9, size);
    memcpy(mNumArrays + size * 10, NUM_LINE, size);
    memcpy(mNumArrays + size * 11, NUM_SPACE, size);
    memcpy(mNumArrays + size * 12, NUM_COLON, size);
    memcpy(mNumArrays + size * 13, NUM_SLASH, size);

    LOGD("offX = %d offY = %d num_width=%d num_height=%d", off_x, off_y, num_width,
         num_height);

};

int getIndex(jchar c) {
    if (c >= '0' && c <= '9')
        return c - '0';
    else if (c == '-')
        return 10;
    else if (c == ' ')
        return 11;
    else if (c == ':')
        return 12;
    else if (c == '/')
        return 13;
    return 11;
}

void printfArr(char *arrs, int width, int height) {
    for (int i = 0; i < height; i++) {
        char line[width + 1];
        for (int j = 0; j < width; j++) {
            line[j] = '0' + *(arrs + i * width + j);
        }
        line[width] = '\0';
        LOGD("%s", line);
    }
}

JNIEXPORT void JNICALL
Java_com_czur_starry_device_localmeetingrecord_jni_YuvOsdUtils_addOsd(
        JNIEnv *env, jclass type, jbyteArray yuv_in_data, jbyteArray yvu_out_data,
        jstring date_) {

    if (yuv_in_data == NULL || yvu_out_data == NULL) {
        // throw IllegalArgumentException to inform Java layer about the issue.
        jclass exClass;
        char *className = "java/lang/IllegalArgumentException";
        exClass = (*env)->FindClass(env, className);
        (*env)->ThrowNew(env, exClass, "yuv_in_data, yvu_out_data, date_ can't be NULL");
        return;
    }

    /* Add checks for length of byte arrays */
    int length_in = (*env)->GetArrayLength(env, yuv_in_data);
    int length_out = (*env)->GetArrayLength(env, yvu_out_data);

    if (length_in < length_out) {
        /* If length of input array is less than output array throw Exception */
        jclass exClass;
        char *className = "java/lang/IllegalArgumentException";
        exClass = (*env)->FindClass(env, className);
        (*env)->ThrowNew(env, exClass, "yuv_in_data length can't be less than yvu_out_data length");
        return;
    }

    jbyte *nv21Src = (*env)->GetByteArrayElements(env, yuv_in_data, NULL);
    if (nv21Src == NULL) {
        return;
    }

    jbyte *destData = (*env)->GetByteArrayElements(env, yvu_out_data, NULL);
    if (destData == NULL) {
        (*env)->ReleaseByteArrayElements(env, yuv_in_data, nv21Src, 0);
        return;
    }

    const jchar *date = (*env)->GetStringChars(env, date_, NULL);
    jsize date_len = (*env)->GetStringLength(env, date_);
//    if (date == NULL) {
//        (*env)->ReleaseByteArrayElements(env, yuv_in_data, nv21Src, 0);
//        (*env)->ReleaseByteArrayElements(env, yvu_out_data, destData, 0);
//        return;
//    }
//    LOGD("date_size=%d date=%d %s",date_size,date_len,date);
    int framesize = frame_width * frame_height;

    int newFrameW;
    if (rotation == 0) {//不旋转
        memcpy(destData, nv21Src, framesize);//copy y数据

        int end = framesize + framesize / 2;
        for (int j = framesize; j < end; j += 2) {//copy uv
            destData[j] = nv21Src[j + 1];
            destData[j + 1] = nv21Src[j];
        }

        newFrameW = frame_width;
    } else if (rotation == 90) {//顺时针旋转90
        int k = 0;
        for (int i = 0; i < frame_width; i++) {//旋转Y
            for (int j = frame_height - 1; j >= 0; j--) {
                destData[k++] = nv21Src[j * frame_width + i];
            }
        }
        //旋转UV
        int uvHeight = frame_height >> 1;

        for (int i = 0; i < frame_width; i += 2) {
            for (int j = uvHeight - 1; j >= 0; j--) {
                destData[k] = nv21Src[framesize + frame_width * j + i + 1];
                destData[k + 1] = nv21Src[framesize + frame_width * j + i];
                k += 2;
            }
        }
        newFrameW = frame_height;
    } else if (rotation == 270) {//顺时针旋转270,=逆方向90
        int k = 0;
        for (int i = frame_width - 1; i >= 0; i--) {//旋转Y
            for (int j = 0; j < frame_height; j++) {
                destData[k++] = nv21Src[j * frame_width + i];
            }
        }
        //旋转UV
        int uvHeight = frame_height >> 1;

        for (int i = frame_width - 1; i >= 0; i -= 2) {
            for (int j = 0; j < uvHeight; j++) {
                destData[k] = nv21Src[framesize + frame_width * j + i];
                destData[k + 1] = nv21Src[framesize + frame_width * j + i - 1];
                k += 2;
            }
        }
        newFrameW = frame_height;
    }


    if (date_len > 0){
        //添加时间水印
        jbyte *dest = destData;
        jbyte *start = dest + off_y * newFrameW;
        for (int i = 0; i < date_len; i++) {
            int index = getIndex(*(date + i));
            char *num = mNumArrays + size * index;
            jbyte *column = start + i * num_width;
            if (!num)
                continue;

            // 计算范围内的Y
            int sum = 0;
            for (int j = 0; j < num_height; j++) {
                jbyte *destIndex = column + j * newFrameW + off_x;
                for (int k = 0; k < num_width; k++) {
                    // 这里的Y是 不能带着符号转成int
                    int brightness = *(destIndex + k) & 0x00FF;
                    sum += brightness;
                }
            }
            int avg = sum / (num_height * num_width);

            for (int j = 0; j < num_height; j++) {
                jbyte *destIndex = column + j * newFrameW + off_x;
                char *src = num + j * num_width;
                for (int k = 0; k < num_width; k++) {
                    if (*(src + k) != 0) {//黑色背景色
                        if (avg < 128) {
                            *(destIndex + k) = -21;//水印文字颜色，-21为白色，0为黑色
                        } else {
                            *(destIndex + k) = 0;//水印文字颜色，-21为白色，0为黑色
                        }

                    }
                }
            }
        }


    }

    (*env)->ReleaseByteArrayElements(env, yuv_in_data, nv21Src, 0);
    (*env)->ReleaseByteArrayElements(env, yvu_out_data, destData, 0);
    (*env)->ReleaseStringChars(env, date_, date);
}


JNIEXPORT jbyteArray JNICALL
Java_com_czur_starry_device_localmeetingrecord_jni_YuvOsdUtils_argbIntToNV21Byte(JNIEnv *env,
                                                                                 jclass jclazz,
                                                                                 jintArray ints,
                                                                                 jint width,
                                                                                 jint height) {
    int frameSize = width * height;
    jint *argb = (*env)->GetIntArrayElements(env, ints, NULL);
    int resLength = frameSize * 3 / 2;
    jbyte *yuv420sp = (jbyte *) malloc(resLength + 1 * sizeof(jbyte));
    int yIndex = 0;
    int uvIndex = frameSize;
    int a, R, G, B, Y, U, V;
    int index = 0;
    for (int j = 0; j < height; j++) {
        for (int i = 0; i < width; i++) {
            a = (argb[index] & 0xff000000) >> 24; // a is not used obviously
            R = (argb[index] & 0xff0000) >> 16;
            G = (argb[index] & 0xff00) >> 8;
            B = argb[index] & 0xff;

            Y = ((66 * R + 129 * G + 25 * B + 128) >> 8) + 16;
            U = ((-38 * R - 74 * G + 112 * B + 128) >> 8) + 128;
            V = ((112 * R - 94 * G - 18 * B + 128) >> 8) + 128;

            yuv420sp[yIndex++] = (jbyte) ((Y < 0) ? 0 : ((Y > 255) ? 255 : Y));
            if (j % 2 == 0 && index % 2 == 0) {
                yuv420sp[uvIndex++] = (jbyte) ((V < 0) ? 0 : ((V > 255) ? 255 : V));
                yuv420sp[uvIndex++] = (jbyte) ((U < 0) ? 0 : ((U > 255) ? 255 : U));
            }
            index++;
        }
    }
    (*env)->ReleaseIntArrayElements(env, ints, argb, JNI_ABORT);
    jbyteArray res = (*env)->NewByteArray(env, resLength);
    (*env)->SetByteArrayRegion(env, res, 0, resLength, yuv420sp);

    free(yuv420sp);
    return res;
}

JNIEXPORT jbyteArray JNICALL
Java_com_czur_starry_device_localmeetingrecord_jni_YuvOsdUtils_argbIntToNV12Byte(JNIEnv *env,
                                                                                 jclass jclazz,
                                                                                 jintArray ints,
                                                                                 jint width,
                                                                                 jint height) {
    int frameSize = width * height;
    jint *argb = (*env)->GetIntArrayElements(env, ints, NULL);
    int resLength = frameSize * 3 / 2;
    jbyte *yuv420sp = (jbyte *) malloc(resLength + 1 * sizeof(jbyte));
    int yIndex = 0;
    int uvIndex = frameSize;
    int a, R, G, B, Y, U, V;
    int index = 0;
    for (int j = 0; j < height; j++) {
        for (int i = 0; i < width; i++) {
            a = (argb[index] & 0xff000000) >> 24; // a is not used obviously
            R = (argb[index] & 0xff0000) >> 16;
            G = (argb[index] & 0xff00) >> 8;
            B = argb[index] & 0xff;

            Y = ((66 * R + 129 * G + 25 * B + 128) >> 8) + 16;
            U = ((-38 * R - 74 * G + 112 * B + 128) >> 8) + 128;
            V = ((112 * R - 94 * G - 18 * B + 128) >> 8) + 128;

            yuv420sp[yIndex++] = (jbyte) ((Y < 0) ? 0 : ((Y > 255) ? 255 : Y));
            if (j % 2 == 0 && index % 2 == 0) {
                yuv420sp[uvIndex++] = (jbyte) ((U < 0) ? 0 : ((U > 255) ? 255 : U));
                yuv420sp[uvIndex++] = (jbyte) ((V < 0) ? 0 : ((V > 255) ? 255 : V));
            }
            index++;
        }
    }
    (*env)->ReleaseIntArrayElements(env, ints, argb, JNI_ABORT);
    jbyteArray res = (*env)->NewByteArray(env, resLength);
    (*env)->SetByteArrayRegion(env, res, 0, resLength, yuv420sp);

    free(yuv420sp);
    return res;
}

JNIEXPORT jbyteArray JNICALL
Java_com_czur_starry_device_localmeetingrecord_jni_YuvOsdUtils_argbIntToGrayNVByte(JNIEnv *env,
                                                                                   jclass jclazz,
                                                                                   jintArray ints,
                                                                                   jint width,
                                                                                   jint height) {
    int frameSize = width * height;
    jint *argb = (*env)->GetIntArrayElements(env, ints, NULL);
    int resLength = frameSize;
    jbyte *yuv420sp = (jbyte *) malloc(resLength + 1 * sizeof(jbyte));
    int yIndex = 0;
    int R, G, B, Y;
    int index = 0;
    for (int j = 0; j < height; j++) {
        for (int i = 0; i < width; i++) {
            R = (argb[index] & 0xff0000) >> 16;
            G = (argb[index] & 0xff00) >> 8;
            B = argb[index] & 0xff;

            Y = ((66 * R + 129 * G + 25 * B + 128) >> 8) + 16;

            yuv420sp[yIndex++] = (jbyte) ((Y < 0) ? 0 : ((Y > 255) ? 255 : Y));

            index++;
        }
    }
    (*env)->ReleaseIntArrayElements(env, ints, argb, JNI_ABORT);
    jbyteArray res = (*env)->NewByteArray(env, resLength);
    (*env)->SetByteArrayRegion(env, res, 0, resLength, yuv420sp);

    free(yuv420sp);
    return res;
}

JNIEXPORT void JNICALL
Java_com_czur_starry_device_localmeetingrecord_jni_YuvOsdUtils_nv21ToNv12(JNIEnv *env,
                                                                          jclass type,
                                                                          jbyteArray nv21Src_,
                                                                          jbyteArray nv12Dest_,
                                                                          jint width,
                                                                          jint height) {
    jbyte *nv21Src = (*env)->GetByteArrayElements(env, nv21Src_, NULL);
    jbyte *nv12Dest = (*env)->GetByteArrayElements(env, nv12Dest_, NULL);

    int framesize = width * height;
    int end = framesize + framesize / 2;
    memcpy(nv21Src, nv12Dest, framesize);

    for (int j = framesize; j < end; j += 2)//u
    {
        nv12Dest[j] = nv21Src[j + 1];
        nv12Dest[j + 1] = nv21Src[j];
    }
    (*env)->ReleaseByteArrayElements(env, nv21Src_, nv21Src, 0);
    (*env)->ReleaseByteArrayElements(env, nv12Dest_, nv12Dest, 0);
}

JNIEXPORT void JNICALL
Java_com_czur_starry_device_localmeetingrecord_jni_YuvOsdUtils_releaseOsd(JNIEnv *env,
                                                                          jclass type) {
    free(mNumArrays);
    mNumArrays = NULL; // 置为 NULL 防止野指针
}