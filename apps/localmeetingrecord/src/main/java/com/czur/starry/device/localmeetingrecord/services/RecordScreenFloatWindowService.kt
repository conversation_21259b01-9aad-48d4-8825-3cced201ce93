package com.czur.starry.device.localmeetingrecord.services

import android.app.ActivityManager
import android.content.Intent
import android.os.Binder
import android.os.IBinder
import android.view.View
import android.widget.FrameLayout
import android.widget.ImageView
import android.widget.TextView
import androidx.constraintlayout.widget.Group
import androidx.core.content.res.ResourcesCompat
import com.czur.czurutils.log.logTagD
import com.czur.czurutils.log.logTagE
import com.czur.czurutils.log.logTagI
import com.czur.czurutils.log.logTagV
import com.czur.starry.device.baselib.common.Constants
import com.czur.starry.device.baselib.notice.MsgType
import com.czur.starry.device.baselib.notice.NoticeHandler
import com.czur.starry.device.baselib.utils.getScreenHeight
import com.czur.starry.device.baselib.utils.gone
import com.czur.starry.device.baselib.utils.invisible
import com.czur.starry.device.baselib.utils.launch
import com.czur.starry.device.baselib.utils.setDebounceTouchClickListener
import com.czur.starry.device.baselib.utils.show
import com.czur.starry.device.baselib.utils.toast
import com.czur.starry.device.baselib.widget.CircleView
import com.czur.starry.device.baselib.widget.UserInterruptConstraintLayout
import com.czur.starry.device.localmeetingrecord.Config
import com.czur.starry.device.localmeetingrecord.Config.AUDIO_FLOAT_HEIGHT
import com.czur.starry.device.localmeetingrecord.Config.AUDIO_FLOAT_WIDTH
import com.czur.starry.device.localmeetingrecord.Config.AUDIO_FLOAT_X
import com.czur.starry.device.localmeetingrecord.Config.AUDIO_FLOAT_Y
import com.czur.starry.device.localmeetingrecord.Config.SCREEN_FLOAT_HEIGHT_CAMERA
import com.czur.starry.device.localmeetingrecord.Config.SCREEN_FLOAT_HEIGHT_WITH_CONTROL
import com.czur.starry.device.localmeetingrecord.Config.SCREEN_FLOAT_WIDTH
import com.czur.starry.device.localmeetingrecord.Config.SCREEN_FLOAT_X
import com.czur.starry.device.localmeetingrecord.Config.SCREEN_FLOAT_Y
import com.czur.starry.device.localmeetingrecord.Config.SRC_VIDEO_HEIGHT_360
import com.czur.starry.device.localmeetingrecord.Config.SRC_VIDEO_WIDTH_360
import com.czur.starry.device.localmeetingrecord.R
import com.czur.starry.device.localmeetingrecord.RecordState
import com.czur.starry.device.localmeetingrecord.monitor.CameraWrapper
import com.czur.starry.device.localmeetingrecord.widget.CameraView
import com.czur.starry.device.localmeetingrecord.widget.RecordView
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.Job
import kotlinx.coroutines.delay
import kotlinx.coroutines.withContext

private const val TAG = "RecordScreenFloatWindowService"

class RecordScreenFloatWindowService() : MeetingRecordAlertWindowService() {
    override var layoutId: Int = R.layout.float_record_video_layout
    override var windowWidth: Int = SCREEN_FLOAT_WIDTH

    override var windowHeight: Int = SCREEN_FLOAT_HEIGHT_WITH_CONTROL

    private var saveOffsetX = SCREEN_FLOAT_X
    private var saveOffsetY = SCREEN_FLOAT_Y

    override var xOffSet = saveOffsetX

    override var yOffset = saveOffsetY


    override val draggable: Boolean
        get() = true

    private lateinit var bgcl: UserInterruptConstraintLayout
    private lateinit var bgVideoFl: FrameLayout
    private lateinit var videoLayout: View
    private lateinit var withoutVideoLayout: View
    private lateinit var surfaceView: CameraView
    private lateinit var btnsGroup: Group
    private lateinit var stopIv: RecordView
    private lateinit var stopIv2: RecordView
    private lateinit var controlIv: ImageView
    private lateinit var controlIv2: ImageView
    private lateinit var fullScreenIv: ImageView
    private lateinit var recordTimeTv: TextView
    private lateinit var recordTimeTv2: TextView
    private lateinit var redPoint: CircleView
    private lateinit var redPointSimple: CircleView

    var floatWindowScreenServiceListener: FloatWindowScreenServiceListener? = null
    private val mBinder: IBinder = RecordBinder()

    override var channel_id = TAG
    override var channel_id_num = 111

    private var activityManager: ActivityManager? = null

    var recordState: String = RecordState.REC.name
    private var mClientCount = 0


    var recordingStatus = true
    var enableVideo = false
    var flipTheLensCbStatus = false
    private var previewViewFinish = false

    var initCameraLaunch: Job? = null
    var reTryCameraTime = 0

    var currentRecordDuringTime = ""

    override fun onCreate() {
        super.onCreate()
        logTagV(TAG, "RecordScreenFloatWindowService-onCreate")

        NoticeHandler.register(MsgType(MsgType.COMMON, MsgType.COMMON_BYOM_REQUEST), this) {
            if (byomIsPairing == true) {
                logTagI(TAG, "收到宜享BYOM申请(Activity),但是正在配对中,不予相应")
                return@register
            }
            logTagI(TAG, "收到宜享BYOM申请(Service)")
            floatWindowScreenServiceListener?.floatStopRecord()
        }
    }

    override fun View.initViews() {
        initView()
    }

    fun initView() {

        bgcl = rootView?.findViewById<UserInterruptConstraintLayout>(R.id.video_bgcl)!!
        bgVideoFl = rootView?.findViewById<FrameLayout>(R.id.bg_video_fl)!!
        videoLayout = rootView?.findViewById<View>(R.id.video_layout)!!
        withoutVideoLayout = rootView?.findViewById<View>(R.id.without_video_layout)!!
        surfaceView = rootView?.findViewById<CameraView>(R.id.surfaceViewadc)!!
        btnsGroup = rootView?.findViewById<Group>(R.id.record_btns_group)!!
        stopIv = rootView?.findViewById<RecordView>(R.id.stop_iv)!!
        stopIv2 = rootView?.findViewById<RecordView>(R.id.stop_iv2)!!
        controlIv = rootView?.findViewById<ImageView>(R.id.pause_iv)!!
        controlIv2 = rootView?.findViewById<ImageView>(R.id.control_iv2)!!
        fullScreenIv = rootView?.findViewById<ImageView>(R.id.full_screen_iv)!!
        recordTimeTv = rootView?.findViewById<TextView>(R.id.record_time_tv)!!
        recordTimeTv2 = rootView?.findViewById<TextView>(R.id.record_time_tv2)!!
        redPoint = rootView?.findViewById<CircleView>(R.id.redpoint)!!
        redPointSimple = rootView?.findViewById<CircleView>(R.id.redPointView)!!


        changeStopIvClickable(false)

        activityManager = application.getSystemService(ACTIVITY_SERVICE) as ActivityManager


        controlIv.setDebounceTouchClickListener {
            floatWindowScreenServiceListener?.floatControlBtnClick()
        }

        controlIv2.setDebounceTouchClickListener {
            floatWindowScreenServiceListener?.floatControlBtnClick()
        }

        fullScreenIv.setDebounceTouchClickListener {
            moveFront()
        }

        stopIv.setDebounceTouchClickListener {
            narrowTimeDownTime = System.currentTimeMillis() + 2000L
            floatWindowScreenServiceListener?.floatStopRecord()
        }
        stopIv2.setDebounceTouchClickListener {
            narrowTimeDownTime = System.currentTimeMillis() + 2000L
            floatWindowScreenServiceListener?.floatStopRecord()
        }
    }

    fun setRecordDuringTime(time: String) {

        if (time != "3" && time != "2" && time != "1") {
            currentRecordDuringTime = time
            recordTimeTv.text = time
            recordTimeTv2.text = time
        }

        if (time == "3" || time == "2" || getRecordTime(time) > 1) {
            changeStopIvClickable(true)
        }
    }

    // 把00:00:00格式的时间转换成秒
    private fun getRecordTime(time: String): Int {
        val timeArray = time.split(":")
        if (timeArray.size <= 1) {
            return 0
        }
        val hour = timeArray[0].toInt()
        val minute = timeArray[1].toInt()
        val second = timeArray[2].toInt()
        return hour * 3600 + minute * 60 + second
    }

    fun changeRecordState(state: String) {
        recordState = state
        var stopIvRes = -1

        val imgRes = when (recordState) {
            RecordState.STOP.name -> {
                recordingStatus = false
                stopIvRes = R.drawable.ic_recording
                R.drawable.ic_play_white
            }

            RecordState.PREPARE.name -> {
                recordingStatus = false
                stopIvRes = R.drawable.ic_recording
                R.drawable.ic_pause_white
            }

            RecordState.REC.name -> {
                recordingStatus = true
                stopIvRes = R.drawable.ic_recording
                isPause = false
                R.drawable.ic_pause_white
            }

            RecordState.PAUSE.name -> {
                recordingStatus = false
                stopIvRes = R.drawable.ic_recording
                changeStopIvClickable(true)
                isPause = true
                R.drawable.ic_play_white
            }

            else -> {
                recordingStatus = false
                stopIvRes = R.drawable.ic_recording
                R.drawable.ic_play_white
            }
        }
        updateRedPointStatus()
        stopIv.setImageResource(stopIvRes)
        if (stopIv.isShown) {
            stopIv.recording = recordingStatus
        }
        controlIv.setImageResource(imgRes)
        controlIv2.setImageResource(imgRes)

    }

    private fun moveFront(): Boolean {
        rootView?.visibility = View.GONE
        //获得当前运行的task(任务)
        val taskInfoList = activityManager!!.appTasks
        for (taskInfo in taskInfoList) {
            //找到本应用的 task，并将它切换到前台
            if (taskInfo.taskInfo.topActivity?.packageName?.contains(packageName) == true) {
                activityManager!!.moveTaskToFront(taskInfo.taskInfo.id, 0)
                break
            }
        }
        return true
    }

    fun getFloatX(): Int {
        return wmParams!!.x
    }

    /**
     * 用于记忆悬浮窗Y轴位置, 每次生成的时候,会显示底部操作条,所以需要增加操作条的偏移量
     */
    fun getFloatY(): Int {
        return wmParams!!.y.coerceAtMost(getScreenHeight() - SCREEN_FLOAT_HEIGHT_WITH_CONTROL - 10)
    }

    override fun initData() {

    }

    override fun onBind(intent: Intent): IBinder? {
        super.onBind(intent)
        mClientCount++
        val duringTimeStr = intent.getStringExtra("recordTime")
        setRecordDuringTime(duringTimeStr!!)

        val state = intent.getStringExtra("recordState")
        enableVideo = intent.getBooleanExtra("enableVideo", false)
        flipTheLensCbStatus = intent.getBooleanExtra("flipTheLensCbStatus", false)


        val defaultX = if (enableVideo) SCREEN_FLOAT_X else AUDIO_FLOAT_X
        val defaultY = if (enableVideo) SCREEN_FLOAT_Y else AUDIO_FLOAT_Y
        saveOffsetX = intent.getIntExtra("offsetX", defaultX)
        saveOffsetY = intent.getIntExtra("offsetY", defaultY)

        logTagD(TAG, "saveOffsetX:${saveOffsetX} + saveOffsetY:${saveOffsetY}")

        if (saveOffsetX == -1 || saveOffsetY == -1) {
            saveOffsetX = defaultX
            saveOffsetY = defaultY
        }

        if (enableVideo) {
            enableVideoView()
        } else {
            closeVideoView()
            needNarrowToCircle = !enableVideo
        }


        if (state != null) {
            changeRecordState(state)
        }
        return mBinder
    }

    private fun closeVideoView(needRefreshParams: Boolean = true) {
        withoutVideoLayout.show()
        videoLayout.gone()
        if (needRefreshParams) {
            refreshParams(
                AUDIO_FLOAT_WIDTH,
                AUDIO_FLOAT_HEIGHT,
                saveOffsetX,
                saveOffsetY
            )
        }
        previewViewFinish = true
        updateRedPointStatus()
    }

    private fun enableVideoView() {
        videoLayout.show()
        withoutVideoLayout.gone()
        refreshParams(
            SCREEN_FLOAT_WIDTH,
            SCREEN_FLOAT_HEIGHT_WITH_CONTROL,
            saveOffsetX,
            saveOffsetY
        )
        logTagV(TAG, "enableVideoView")
        surfaceView.onReadyCallback(
            readyCallback = {
                logTagV(TAG, "readyCallback")
                launch {
                    startPreview()
                }
            },
            displayFrameCallback = {
                logTagV(TAG, "displayFrameCallback")
                previewViewFinish = true
                ResourcesCompat.getDrawable(resources, R.drawable.blue_50_bg, null)?.let {
                    bgVideoFl.background = it
                }
                updateRedPointStatus()  // 防止画面没出来, 小红点在那孤零零的闪烁
                floatWindowScreenServiceListener?.displayFrameCallback()
            })

        val rotation = if (flipTheLensCbStatus) {
            0f
        } else {
            180f
        }
        surfaceView.rotationY = rotation;
    }

    /**
     * 开始预览
     * 该方法耗时, 大概耗时1.3s左右
     */
    private fun startPreview() {
        initCameraLaunch = launch(Dispatchers.IO) {

            try {
                CameraWrapper.rePreviewCamera(
                    SRC_VIDEO_WIDTH_360,
                    SRC_VIDEO_HEIGHT_360,
                    surfaceView
                )
                CameraWrapper.justPausePreview()
                CameraWrapper.startPreview()
                reTryCameraTime = 0
                delay(500)
            } catch (e: Exception) {

                logTagE(TAG, "float相机开启失败,重试中...次数${reTryCameraTime}}", tr = e)
                if (reTryCameraTime > Config.OPEN_CAMERA_RETRY_TIMES) {
                    withContext(Dispatchers.Main) {
                        toast(R.string.toast_init_camera_fail)
                    }
                } else {
                    reTryCameraTime++
                    delay(500)
                    startPreview()
                }
            }


        }
    }

    private fun updateRedPointStatus() {
        redPoint.gone(!previewViewFinish || !recordingStatus)
        redPointSimple.invisible(!previewViewFinish || !recordingStatus)
    }

    override fun onUnbind(intent: Intent?): Boolean {
        mClientCount--
        return super.onUnbind(intent)
    }

    fun isServiceAlive(): Boolean {
        return mClientCount > 0
    }

    inner class RecordBinder : Binder() {
        val recordService: RecordScreenFloatWindowService
            get() = this@RecordScreenFloatWindowService

    }

    override fun onDestroy() {
        super.onDestroy()
    }

    override fun reCreateView() {
        initView()
        setRecordDuringTime(currentRecordDuringTime)

        closeVideoView(false)

        changeRecordState(recordState)
    }


    private fun changeStopIvClickable(clickable: Boolean) {
        stopIv.apply {
            isClickable = clickable
            isEnabled = clickable
            isFocusable = clickable
        }

        stopIv2.apply {
            isClickable = clickable
            isEnabled = clickable
            isFocusable = clickable
        }
    }

    interface FloatWindowScreenServiceListener {
        fun floatControlBtnClick()//暂停或继续
        fun floatStopRecord() //停止录制
        fun floatFullScreen()//最大化
        fun displayFrameCallback()//view有视频数据了
    }
}