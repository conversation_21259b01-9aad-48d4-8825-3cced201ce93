package com.czur.starry.device.wallpaperdisplay.util

import android.graphics.Rect
import android.view.View
import androidx.recyclerview.widget.RecyclerView

/**
 * created by wangh 22.0930
 */

private const val SPAN_COUNT = 4
private const val HEAD_ITEM_COUNT = 0
private const val TOP_SPACE = 20
class SpacesItemDecoration(private val space: Int = 50) : RecyclerView.ItemDecoration() {

    override fun getItemOffsets(
        outRect: Rect,
        view: View,
        parent: RecyclerView,
        state: RecyclerView.State
    ) {

        setNGridLayoutSpaceItemDecoration(outRect, view, parent, state)

    }


    /**
     * GridLayoutManager or StaggeredGridLayoutManager spacing
     * @param outRect
     * @param view
     * @param parent
     * @param state
     */
    private fun setNGridLayoutSpaceItemDecoration(
        outRect: Rect,
        view: View,
        parent: RecyclerView,
        state: RecyclerView.State
    ) {
        val position: Int = parent.getChildAdapterPosition(view) - HEAD_ITEM_COUNT
        if ((HEAD_ITEM_COUNT != 0) && (position == -HEAD_ITEM_COUNT)) {
            return
        }
        val column: Int = position % SPAN_COUNT
        if (false) {//左右两边界处理
            outRect.left = space - column * space / SPAN_COUNT
            outRect.right = (column + 1) * space / SPAN_COUNT
            if (position < SPAN_COUNT) {
                outRect.top = space
            }
            outRect.bottom = this.space
        } else {
            outRect.left = column * space / SPAN_COUNT
            outRect.right = space - (column + 1) * space / SPAN_COUNT
            if (position >= SPAN_COUNT) {
                outRect.top = TOP_SPACE
            }
        }
    }

}