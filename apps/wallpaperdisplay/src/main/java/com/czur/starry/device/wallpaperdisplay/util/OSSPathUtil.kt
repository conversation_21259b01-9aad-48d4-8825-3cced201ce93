package com.czur.starry.device.wallpaperdisplay.util

import com.czur.czurutils.log.logTagW
import com.czur.czurutils.log.logTagD
import com.alibaba.sdk.android.oss.OSS
import com.alibaba.sdk.android.oss.model.ListObjectsRequest

import com.czur.starry.device.wallpaperdisplay.bean.FileEntity
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.isActive
import kotlinx.coroutines.withContext
import java.io.File

/**
 * Created by 陈丰尧 on 2022/3/9
 */
class OSSPathUtil(
    private val fileOss: OSS,
    private val bucketName: String,
) {
    companion object {
        private const val TAG = "OSSPathUtil"
    }

    /**
     * 递归方式获取指定路径下的全部文件/文件夹 path
     */
    suspend fun getAllCloudFilePath(fileEntities: List<FileEntity>): List<String> {
        val resultSet = mutableSetOf<String>()

        return withContext(Dispatchers.IO) {
            fileEntities.forEach { entity ->
                if (!isActive) {
                    logTagW(TAG, "协程取消(getAllFilePath)")
                    return@forEach
                }
                if (entity.isDir()) {
                    val prefix = entity.absPathWithSuffix
                    getObjectList(prefix, resultSet)
                } else {
                    resultSet.add(entity.absPathWithSuffix)
                }
            }

            resultSet.toList()
        }
    }




    private fun getObjectList(
        prefix: String,
        pathSet: MutableSet<String>
    ) {
        var nextMarker: String? = null
        do {
            val listObjectsRequest = ListObjectsRequest(
                bucketName, prefix, nextMarker, null, null
            )
            val objectListing = fileOss.listObjects(listObjectsRequest)
            if (objectListing.objectSummaries.size > 0) {
                for (s in objectListing.objectSummaries) {
                    logTagD(TAG, "getObjectList:" + s.key)
                    pathSet.add(s.key)
                }
            }
            nextMarker = objectListing.nextMarker
        } while (objectListing.isTruncated)
    }

    private fun getLocalFilePath(file: File, paths: MutableSet<String>) {
        if (!file.isDirectory) {
            paths.add(file.absolutePath)
        } else {
            paths.add(file.absolutePath + "/")
            file.walkTopDown().forEach {
                if (!it.isDirectory) {
                    paths.add(it.absolutePath)
                } else {
                    paths.add(it.absolutePath + "/")
                }
            }
        }
    }
}