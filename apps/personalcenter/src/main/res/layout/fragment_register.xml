<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/base_bg_color"
    tools:ignore="PxUsage,RtlHardcoded">


    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="98px"
        android:gravity="center"
        android:text="@string/register_account_title"
        android:textColor="@color/white"
        android:textSize="48px"
        android:textStyle="bold"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <View
        android:id="@+id/registerTopLine"
        style="@style/register_line"
        android:layout_marginTop="214px"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <View
        android:id="@+id/registerBottomLine"
        style="@style/register_line"
        android:layout_marginTop="242px"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@id/registerTopLine" />

    <TextView
        android:id="@+id/registerAccountIDTv"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:textColor="@color/white"
        android:textSize="48px"
        android:textStyle="bold"
        app:layout_constraintBottom_toTopOf="@id/registerAccountIDsView"
        app:layout_constraintLeft_toLeftOf="@id/registerTopLine"
        app:layout_constraintRight_toRightOf="@id/registerTopLine"
        app:layout_constraintTop_toBottomOf="@id/registerTopLine"
        tools:text="239830" />

    <ProgressBar
        android:id="@+id/registerAccountLoadingPb"
        android:layout_width="40px"
        android:layout_height="40px"
        android:indeterminateTint="@color/white"
        android:indeterminateTintMode="src_atop"
        app:layout_constraintBottom_toBottomOf="@id/registerAccountIDTv"
        app:layout_constraintLeft_toLeftOf="@id/registerAccountIDTv"
        app:layout_constraintRight_toRightOf="@id/registerAccountIDTv"
        app:layout_constraintTop_toTopOf="@+id/registerAccountIDTv"

        />

    <TextView
        android:id="@+id/registerAccountTitleTv"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/account_label"
        android:textColor="@color/white"
        android:textSize="30px"
        android:textStyle="bold"
        app:layout_constraintBottom_toBottomOf="@id/registerAccountIDTv"
        app:layout_constraintLeft_toLeftOf="@id/registerTopLine"
        app:layout_constraintTop_toTopOf="@id/registerAccountIDTv" />

    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/account_hint"
        android:textColor="@color/white"
        android:textSize="20px"
        app:layout_constraintLeft_toLeftOf="@id/registerAccountTitleTv"
        app:layout_constraintRight_toRightOf="@id/registerAccountTitleTv"
        app:layout_constraintTop_toBottomOf="@id/registerAccountTitleTv" />

    <LinearLayout
        android:id="@+id/registerChangeAccountLl"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        app:layout_constraintBottom_toBottomOf="@id/registerAccountIDTv"
        app:layout_constraintRight_toRightOf="@id/registerTopLine"
        app:layout_constraintTop_toTopOf="@id/registerAccountIDTv">

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/account_change"
            android:textColor="@color/white"
            android:textSize="30px"
            android:textStyle="bold" />

        <ImageView
            android:layout_width="11px"
            android:layout_height="20px"
            android:layout_gravity="center_vertical"
            android:layout_marginLeft="12px"
            android:src="@drawable/ic_change_arrow_right" />
    </LinearLayout>

    <com.czur.starry.device.personalcenter.widget.AccountNoSelView
        android:id="@+id/registerAccountIDsView"
        android:layout_width="match_parent"
        android:layout_height="50px"
        android:visibility="invisible"
        app:layout_constraintBottom_toTopOf="@id/registerBottomLine"
        app:layout_constraintTop_toBottomOf="@id/registerAccountIDTv" />

    <EditText
        android:id="@+id/registerPwdEt"
        style="@style/register_input_et"
        android:layout_marginTop="70px"
        android:hint="@string/pwd_hint"
        android:inputType="textPassword"
        android:maxLength="20"
        android:nextFocusUp="@id/registerNicknameEt"
        android:nextFocusDown="@id/registerNicknameEt"
        android:textColorHint="#80FFFFFF"
        app:bl_corners_radius="10px"
        app:bl_solid_color="#********"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@id/registerBottomLine" />

    <com.czur.starry.device.baselib.widget.EyeView
        android:id="@+id/pwdEyeView"
        android:layout_width="40px"
        android:layout_height="24px"
        app:layout_constraintBottom_toBottomOf="@id/registerPwdEt"
        app:layout_constraintLeft_toLeftOf="@id/nickNameTipTv"
        app:layout_constraintRight_toRightOf="@id/nickNameTipTv"
        app:layout_constraintTop_toTopOf="@id/registerPwdEt" />

    <TextView
        android:id="@+id/pwdErrorInfoTv"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginLeft="30px"
        android:textColor="@color/white"
        android:textSize="25px"
        android:textStyle="bold"
        app:layout_constraintBottom_toBottomOf="@id/registerPwdEt"
        app:layout_constraintLeft_toRightOf="@id/pwdEyeView"
        app:layout_constraintTop_toTopOf="@id/registerPwdEt" />

    <TextView
        style="@style/register_input_title_tv"
        android:text="@string/password"
        app:layout_constraintBottom_toBottomOf="@id/registerPwdEt"
        app:layout_constraintRight_toLeftOf="@id/registerPwdEt"
        app:layout_constraintTop_toTopOf="@id/registerPwdEt" />

    <EditText
        android:id="@+id/registerNicknameEt"
        style="@style/register_input_et"
        android:layout_marginTop="50px"
        android:hint="@string/nickname_hint"
        android:imeOptions="actionDone"
        android:nextFocusUp="@id/registerPwdEt"
        android:nextFocusDown="@id/registerPwdEt"
        android:textColorHint="#80FFFFFF"
        app:bl_corners_radius="10px"
        app:bl_solid_color="#********"
        app:layout_constraintLeft_toLeftOf="@id/registerPwdEt"
        app:layout_constraintTop_toBottomOf="@id/registerPwdEt" />

    <TextView
        android:id="@+id/registerNicknameTitleTv"
        style="@style/register_input_title_tv"
        android:text="@string/nickname"
        app:layout_constraintBottom_toBottomOf="@id/registerNicknameEt"
        app:layout_constraintRight_toLeftOf="@id/registerNicknameEt"
        app:layout_constraintTop_toTopOf="@id/registerNicknameEt" />

    <TextView
        android:id="@+id/nickNameTipTv"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginLeft="14px"
        android:gravity="center"
        android:minWidth="96px"
        android:text="@string/nickname_extend"
        android:textColor="@color/white"
        android:textSize="24px"
        app:layout_constraintBottom_toBottomOf="@id/registerNicknameEt"
        app:layout_constraintLeft_toRightOf="@id/registerNicknameEt"
        app:layout_constraintTop_toTopOf="@id/registerNicknameEt" />

    <com.czur.uilib.btn.CZButton
        android:id="@+id/registerPreBtn"
        android:layout_width="300px"
        android:layout_height="80px"
        android:layout_marginBottom="60px"
        android:text="@string/step_pre"
        android:textSize="30px"
        app:colorStyle="NegativeInBlue"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintHorizontal_chainStyle="packed"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toLeftOf="@id/registerNextBtn" />

    <com.czur.uilib.btn.CZButton
        android:id="@+id/registerNextBtn"
        android:layout_width="300px"
        android:layout_height="80px"
        android:layout_marginLeft="30px"
        android:enabled="false"
        android:text="@string/step_next"
        android:textSize="30px"
        app:colorStyle="PositiveInBlue"
        app:layout_constraintLeft_toRightOf="@id/registerPreBtn"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="@id/registerPreBtn" />

</androidx.constraintlayout.widget.ConstraintLayout>