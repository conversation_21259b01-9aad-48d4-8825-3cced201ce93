<?xml version="1.0" encoding="utf-8"?>
<navigation xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/nav_user_begin"
    app:startDestination="@id/remindFragment">


    <fragment
        android:id="@+id/remindFragment"
        android:name="com.czur.starry.device.personalcenter.account.deactivate.DeactivateRemindFragment"
        android:label="RemindFragment"
        tools:layout="@layout/layout_deactivate_remind" >
        <action
            android:id="@+id/action_remindFragment_to_deactivateFragment"
            app:destination="@id/checkPhoneFragment"
            app:enterAnim="@anim/nav_default_enter_anim"
            app:exitAnim="@anim/nav_default_exit_anim" />
    </fragment>

    <fragment
        android:id="@+id/checkPhoneFragment"
        android:name="com.czur.starry.device.personalcenter.account.deactivate.CheckPhoneFragment"
        android:label="CheckPhoneFragment"
        tools:layout="@layout/layout_change_phone">

    </fragment>



</navigation>