package com.czur.starry.device.personalcenter.account.deactivate

import android.content.Intent
import androidx.navigation.fragment.findNavController
import com.czur.czurutils.log.logTagI
import com.czur.czurutils.log.logTagV
import com.czur.czurutils.log.logTagW
import com.czur.starry.device.baselib.data.provider.UserHandler
import com.czur.starry.device.baselib.network.core.exception.MiaoNetException
import com.czur.starry.device.baselib.network.core.exception.MiaoVerificationCodeException
import com.czur.starry.device.baselib.utils.basic.alsoFalse
import com.czur.starry.device.baselib.utils.basic.alsoTrue
import com.czur.starry.device.baselib.utils.clearContentText
import com.czur.starry.device.baselib.utils.launch
import com.czur.starry.device.baselib.utils.toast
import com.czur.starry.device.baselib.utils.toastFail
import com.czur.starry.device.personalcenter.R
import com.czur.starry.device.personalcenter.account.phone.AbsChangePhoneFragment
import com.czur.starry.device.personalcenter.account.phone.ChangePhoneViewModel
import com.czur.starry.device.personalcenter.begin.UserBeginNavActivity
import com.czur.starry.device.personalcenter.databinding.LayoutChangePhoneBinding

private const val TAG = "CheckPhoneFragment"

class CheckPhoneFragment : AbsChangePhoneFragment<LayoutChangePhoneBinding>() {
    override val viewType: ChangePhoneViewModel.ViewType
        get() = ChangePhoneViewModel.ViewType.OLD

    override fun LayoutChangePhoneBinding.initSelfViews() {
        captchaTitleTv.setText(R.string.title_change_bind_phone_old)
        captchaDescTv.clearContentText()
        captchaNextBtn.setText(R.string.str_deactivate)

        val userPhone = UserHandler.mobile
        val displayPhone = userPhone.replaceRange(3..6, "****")
        captchaPhoneEt.setText(displayPhone)
        captchaPhoneEt.isEnabled = false
        viewModel.updateUserInputPhoneNum(userPhone)
    }

    override fun doOnNextStep() {
        verifyVerificationCode()
    }

    /**
     * 验证验证码
     */
    private fun verifyVerificationCode() {
        val verificationCode = viewModel.userInputCaptcha
        logTagI(
            TAG,
            "验证验证码: $verificationCode"
        )
        launch {
            val deactivateAccountRes = withLoading {
                viewModel.deactivateAccount(verificationCode)
            }
            if (deactivateAccountRes.isSuccess) {
                logTagV(TAG, "注销成功")
                UserHandler.logout(true)
                startActivity(Intent(requireActivity(), UserBeginNavActivity::class.java).apply {
                    addFlags(Intent.FLAG_ACTIVITY_CLEAR_TASK)
                })
                requireActivity().finish()
            } else {
                val tr = deactivateAccountRes.exceptionOrNull()
                logTagW(TAG, "注销失败", tr = tr)
                when (tr) {
                    is MiaoVerificationCodeException -> {
                        toast(R.string.toast_error_verification_code)
                    }

                    is MiaoNetException -> {
                        toast(R.string.toast_network_error_retry)
                    }

                    else -> {
                        toastFail()
                    }
                }
            }
        }
    }
}