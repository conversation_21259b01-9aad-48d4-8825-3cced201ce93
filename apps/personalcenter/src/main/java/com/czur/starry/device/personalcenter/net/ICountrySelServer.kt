package com.czur.starry.device.personalcenter.net

import com.czur.starry.device.baselib.network.core.MiaoHttpEntity
import com.czur.starry.device.baselib.network.core.MiaoHttpGet
import com.czur.starry.device.baselib.network.core.MiaoHttpParam
import com.czur.starry.device.personalcenter.bean.CountryData

/**
 * Created by 陈丰尧 on 2/26/21
 */
interface ICountrySelServer {
    /**
     * 获取国家列表, HOST 和其他的接口不同, 是单独的
     * @param type: 语言, 涉及到排序
     *          zh-CN: 中文
     *          en-US: 英文
     */
    @MiaoHttpGet("/api/v1/public/country/code")
    fun countryCodeList(
        @MiaoHttpParam("type")
        type: String,
        clazz: Class<CountryData> = CountryData::class.java
    ): MiaoHttpEntity<CountryData>
}