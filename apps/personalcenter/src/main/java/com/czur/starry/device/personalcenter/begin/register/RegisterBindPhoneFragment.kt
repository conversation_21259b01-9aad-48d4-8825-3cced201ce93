package com.czur.starry.device.personalcenter.begin.register

import android.os.Bundle
import android.text.InputFilter
import android.view.KeyEvent
import android.view.View
import androidx.core.widget.doAfterTextChanged
import androidx.fragment.app.viewModels
import androidx.navigation.fragment.findNavController
import com.czur.czurutils.log.logTagV
import com.czur.starry.device.baselib.base.listener.KeyDownListener
import com.czur.starry.device.baselib.utils.keyboard.keyboardHide
import com.czur.starry.device.baselib.utils.keyboard.keyboardShow
import com.czur.starry.device.baselib.utils.launch
import com.czur.starry.device.baselib.utils.setOnActionDoneListener
import com.czur.starry.device.baselib.utils.setOnDebounceClickListener
import com.czur.starry.device.baselib.view.dialog.LoadingDialog
import com.czur.starry.device.personalcenter.begin.AbsBeginFragment
import com.czur.starry.device.personalcenter.begin.viewmodel.RegisterBindPhoneViewModel
import com.czur.starry.device.personalcenter.begin.viewmodel.RegisterViewModel
import com.czur.starry.device.personalcenter.begin.viewmodel.RegisterViewModel.Companion.CAPTCHA_ERROR
import com.czur.starry.device.personalcenter.begin.viewmodel.RegisterViewModel.Companion.NETWORK_REGISTER
import com.czur.starry.device.personalcenter.begin.viewmodel.RegisterViewModel.Companion.NETWORK_SEND_CODE
import com.czur.starry.device.personalcenter.begin.viewmodel.RegisterViewModel.Companion.SUCCESS
import com.czur.starry.device.personalcenter.databinding.LayoutPhoneCaptchaBinding
import com.czur.starry.device.personalcenter.net.AccountManager
import com.czur.starry.device.personalcenter.net.AccountManager.captchaTime
import com.czur.starry.device.personalcenter.startup.getNetworkErrorDialog

/**
 * Created by 陈丰尧 on 2/26/21
 */
private const val TAG = "RegisterBindPhoneFragment"
class RegisterBindPhoneFragment : AbsBeginFragment<LayoutPhoneCaptchaBinding>(), KeyDownListener {
    private val model: RegisterViewModel by viewModels({ requireActivity() })
    private val bindPhoneViewModel: RegisterBindPhoneViewModel by viewModels()
    private var chinesePhoneFilter = InputFilter.LengthFilter(11)
    private val loadingDialog by lazy {
        LoadingDialog()
    }

    override fun LayoutPhoneCaptchaBinding.initBindingViews() {
        updateEditTextLengthFilter()

        captchaPreBtn.setOnDebounceClickListener {
            logTagV(TAG, "上一步")
            findNavController().popBackStack()
            // 返回上一步时,清空记录的手机号
            bindPhoneViewModel.clearInfo()
        }

        getCaptchaTv.setOnDebounceClickListener {
            sendVerifyCode()
        }

        captchaPhoneEt.doAfterTextChanged {
            it?.let { editable ->
                val text = editable.toString()
                bindPhoneViewModel.sendTarget = text
            }
        }
        captchaPhoneEt.requestFocus()
        captchaPhoneEt.keyboardShow(300L)

        captchaEt.doAfterTextChanged {
            it?.let {
                val verificationCode = it.toString()
                bindPhoneViewModel.verificationCode = verificationCode
            }
        }
        captchaEt.setOnActionDoneListener {
            if (captchaNextBtn.isEnabled) {
                logTagV(TAG, "captchaEt - 注册")
                captchaNextBtn.performClick()
            } else {
                captchaEt.keyboardHide()
            }
            true
        }

        captchaNextBtn.setOnDebounceClickListener(500) {
            register()
        }
    }

    override fun initData(savedInstanceState: Bundle?) {
        initObserves()
    }

    private fun initObserves() {
        bindPhoneViewModel.isTargetLegalLive.observe(viewLifecycleOwner) {
            // 手机号是否正确
            binding.getCaptchaTv.isEnabled = it
        }
        captchaTime.observe(viewLifecycleOwner) {
            if (it < 0) {
                // 显示获取
                binding.countDownTv.visibility = View.GONE
                binding.getCaptchaTv.visibility = View.VISIBLE
            } else {
                binding.countDownTv.visibility = View.VISIBLE
                binding.getCaptchaTv.visibility = View.GONE
                binding.countDownTv.text = "${it}s"
            }
        }
        bindPhoneViewModel.registerEnableLive.observe(viewLifecycleOwner) {
            binding.captchaNextBtn.isEnabled = it
        }
        model.networkError.observe(viewLifecycleOwner) {
            when (it) {
                NETWORK_SEND_CODE -> getNetworkErrorDialog(requireActivity()) {
                    sendVerifyCode()
                }.show()
                NETWORK_REGISTER -> getNetworkErrorDialog(requireActivity()) {
                    register()
                }.show()
            }
        }

        model.loading.observe(viewLifecycleOwner) {
            if (it) {
                loadingDialog.show()
            } else {
                loadingDialog.dismiss()
            }
        }

    }


    private fun register() {
        launch {
            logTagV(TAG, "注册")
            when (model.register(bindPhoneViewModel.getBindInfo())) {
                SUCCESS -> {
                    // 注册成功
                    RegisterBindPhoneFragmentDirections
                        .actionRegisterBindPhoneFragmentToRegisterSuccessFragment()
                        .nav()
                }
                CAPTCHA_ERROR -> {
                    // 验证码错误时, 情况验证码输入框
                    binding.captchaEt.setText("")
                }
                RegisterViewModel.TIMEOUT_ERROR -> {
                    // 在页面中停留时间太长了, 直接回到登录选择页面
                    RegisterBindPhoneFragmentDirections
                        .actionRegisterBindPhoneFragmentToUserBeginFragment()
                        .nav()
                }
            }
        }
    }

    /**
     * 请求验证码
     */
    private fun sendVerifyCode() {
        launch {
            logTagV(TAG, "请求验证码")
            val phone = bindPhoneViewModel.sendTarget
            model.sendVerificationCode(phone, AccountManager.VerificationCodeSendWay.PHONE)
        }
    }

    private fun updateEditTextLengthFilter() {
        val rawFilters = binding.captchaPhoneEt.filters?.toMutableList() ?: mutableListOf()
        if (binding.captchaAreaCodeTv.text.toString() == "+86") {
            rawFilters.add(chinesePhoneFilter)
        } else {
            rawFilters.remove(chinesePhoneFilter)
        }
        binding.captchaPhoneEt.filters = rawFilters.toTypedArray()

    }

    override fun onKeyDown(keyCode: Int, event: KeyEvent?): Boolean {
        return if (keyCode == KeyEvent.KEYCODE_BACK) {
            binding.captchaPreBtn.performClick()
            true
        } else {
            false
        }
    }


}