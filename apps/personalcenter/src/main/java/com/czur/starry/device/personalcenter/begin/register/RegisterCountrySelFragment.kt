package com.czur.starry.device.personalcenter.begin.register

import android.os.Bundle
import androidx.fragment.app.viewModels
import androidx.navigation.fragment.findNavController
import com.czur.starry.device.baselib.utils.SettingHandler
import com.czur.starry.device.baselib.utils.doOnItemClick
import com.czur.starry.device.baselib.utils.launch
import com.czur.starry.device.personalcenter.begin.AbsBeginFragment
import com.czur.starry.device.personalcenter.begin.viewmodel.RegisterCountrySelViewModel
import com.czur.starry.device.personalcenter.begin.viewmodel.RegisterViewModel
import com.czur.starry.device.personalcenter.databinding.FragmentCountrySelectBinding
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext

/**
 * Created by 陈丰尧 on 2022/7/18
 * 国家选择页面
 */
class RegisterCountrySelFragment : AbsBeginFragment<FragmentCountrySelectBinding>() {
    private val countrySelVM: RegisterCountrySelViewModel by viewModels()
    private val registerViewModel: RegisterViewModel by viewModels({ requireActivity() })

    private lateinit var adapter: RegisterCountrySelAdapter

    override fun FragmentCountrySelectBinding.initBindingViews() {
        selCountryRv.doOnItemClick { vh, view ->
            val pos = vh.bindingAdapterPosition
            adapter.selectPos = pos // 切换选中项
            true
        }

        // 上一步
        countrySelPreBtn.setOnClickListener {
            findNavController().navigateUp()
        }
        // 下一步
        countrySelNextBtn.setOnClickListener {
            registerViewModel.selCountryCode = adapter.getSelCountryCode
            RegisterCountrySelFragmentDirections
                .actionRegisterCountrySelFragmentToRegisterAccountFragment().nav()
        }
    }

    override fun initData(savedInstanceState: Bundle?) {
        countrySelVM.countryListLive.observe(viewLifecycleOwner) {
            if (this@RegisterCountrySelFragment::adapter.isInitialized) {
                adapter.setData(it)
                if (adapter.selectPos < 0 && it.isNotEmpty()) {
                    // 初始化选中信息
                    val selIndex = it.indexOfFirst { info ->
                        info.sel
                    }
                    adapter.selectPos = selIndex
                    binding.selCountryRv.scrollToPosition(selIndex)
                }
            }
        }

        launch {
            // 初始化Adapter
            val languageCode = withContext(Dispatchers.IO) {
                SettingHandler.czurLang
            }
            adapter = RegisterCountrySelAdapter(languageCode)
            binding.selCountryRv.adapter = adapter

            countrySelVM.loadCountryList()
        }
    }

}