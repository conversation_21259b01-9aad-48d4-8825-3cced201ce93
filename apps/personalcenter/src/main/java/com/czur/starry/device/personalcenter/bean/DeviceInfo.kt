package com.czur.starry.device.personalcenter.bean

import android.os.Parcelable
import kotlinx.android.parcel.Parcelize

/**
 * 设备信息
 */
@Parcelize
data class DeviceInfo(
    val meetingAccount: MutableList<Device>,   // 手机号绑定的设备集合
    val token: String
) : Parcelable

/**
 * 单条设备信息
 */
@Parcelize
data class Device(
    val accountNo: String,
    val name: String,
    val kind: Int,
    val type: Int
) : Parcelable

