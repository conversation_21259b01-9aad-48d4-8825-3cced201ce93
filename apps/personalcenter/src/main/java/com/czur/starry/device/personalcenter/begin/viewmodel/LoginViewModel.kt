package com.czur.starry.device.personalcenter.begin.viewmodel

import androidx.lifecycle.Observer
import androidx.lifecycle.ViewModel
import com.czur.czurutils.log.logTagD
import com.czur.starry.device.baselib.network.core.exception.MiaoHttpException
import com.czur.starry.device.baselib.utils.DifferentLiveData
import com.czur.starry.device.baselib.utils.isDeviceAccountLength
import com.czur.starry.device.baselib.utils.isPwdLength
import com.czur.starry.device.personalcenter.net.AccountManager

private const val TAG = "LoginViewModel"
class LoginViewModel : ViewModel() {
    val account = DifferentLiveData("")
    val pwd = DifferentLiveData("")

    // 登录按钮是否可用
    val loginBtnEnable = DifferentLiveData(false)

    val errorCode = DifferentLiveData<Int>()

    init {
        val loginEnableObservable = Observer<String> {
            checkLoginBtnEnable()
        }
        account.observeForever(loginEnableObservable)
        pwd.observeForever(loginEnableObservable)
    }

    private fun checkLoginBtnEnable() {
        val account = this.account.value!!
        val pwd = this.pwd.value!!
        loginBtnEnable.value = account.isDeviceAccountLength() && pwd.isPwdLength()
    }

    /**
     * 登录
     */
    suspend fun login(): Boolean {
        return try {
            val userInfo = AccountManager.login(accountNo = account.value!!, pwd = pwd.value!!)
            logTagD(TAG, "登录成功:$userInfo")
            true
        } catch (e: MiaoHttpException) {
            // 21.3.3 于洋: 不需要先判断账号是否存在, 就一个账号/密码错误
            logTagD(TAG, "登录失败:${e.message}")
            errorCode.value = e.code
            false
        }
    }

    /**
     * 重置ErrorCode
     */
    fun errorCodeReset(){
        errorCode.value = 0
    }

}