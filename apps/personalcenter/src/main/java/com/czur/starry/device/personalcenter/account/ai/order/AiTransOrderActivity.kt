package com.czur.starry.device.personalcenter.account.ai.order

import androidx.navigation.fragment.NavHostFragment
import com.czur.starry.device.baselib.base.v2.aty.CZViewBindingAty
import com.czur.starry.device.personalcenter.R
import com.czur.starry.device.personalcenter.databinding.ActivityAiTransCommonBinding

/**
 * Created by 陈丰尧 on 2025/4/23
 */
class AiTransOrderActivity: CZViewBindingAty<ActivityAiTransCommonBinding>() {
    override fun ActivityAiTransCommonBinding.initBindingViews() {
        val fragment = navHost.getFragment<NavHostFragment>()
        val navId = R.navigation.nav_ai_trans_order
        fragment.navController.setGraph(navId)
    }
}