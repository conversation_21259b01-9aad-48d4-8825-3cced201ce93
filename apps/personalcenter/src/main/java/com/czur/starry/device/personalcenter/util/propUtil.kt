package com.czur.starry.device.personalcenter.util

import java.io.BufferedReader
import java.io.InputStreamReader

/**
 * Created by 陈丰尧 on 2021/7/20
 */
fun getProp(key: String): String {
    val process = Runtime.getRuntime().exec("getprop $key")
    val stream = process.inputStream
    val reader = BufferedReader(InputStreamReader(stream))
    return reader.useLines {
        buildString {
            it.forEach { line ->
                append(line)
            }
        }
    }
}

fun setProp(key: String, value: String) {
    Runtime.getRuntime().exec("setprop $key $value")
}