package com.czur.starry.device.personalcenter.begin.viewmodel

import com.czur.czurutils.log.logTagE
import android.app.Application
import androidx.lifecycle.AndroidViewModel
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.Observer
import com.czur.starry.device.baselib.network.core.exception.*
import com.czur.starry.device.baselib.utils.*
import com.czur.starry.device.personalcenter.R
import com.czur.starry.device.personalcenter.bean.DeviceInfo
import com.czur.starry.device.personalcenter.net.AccountManager

/**
 * Created by 陈丰尧 on 3/3/21
 */
class LoginForgetCaptchaEmailViewModel(application: Application) : AndroidViewModel(application) {
    companion object {
        private const val TAG = "LoginForgetCaptchaViewModel"
    }

    val email = DifferentLiveData("")
    val captcha = DifferentLiveData("")

    val nextEnable = DifferentLiveData(false)
    val captchaEnable = DifferentLiveData(false)
    val deviceInfo = DifferentLiveData<DeviceInfo>()

    val errorCode = MutableLiveData<Int>()

    var lastNetwork: (suspend () -> Unit)? = null

    init {
        val nextEnableObservable = Observer<String> {
            checkNextEnable()
        }
        email.observeForever(nextEnableObservable)
        captcha.observeForever(nextEnableObservable)

        email.observeForever {
            captchaEnable.value = it.isEmail()
        }
    }

    private fun checkNextEnable() {
        val email = this.email.value!!
        val captcha = this.captcha.value!!
        nextEnable.value = captcha.isVerificationCode() && email.isEmail()
    }

    suspend fun getCaptcha() {
        lastNetwork = this::getCaptcha
        try {
            val email = this.email.value!!
            AccountManager.obtainCaptchaCode(email, AccountManager.VerificationCodeSendWay.EMAIL)
        } catch (e: MiaoHttpException) {
            logTagE(TAG, "获取验证码异常")
            processNetworkError(e)
        }
    }

    /**
     * 处理网络请求的错误
     */
    private fun processNetworkError(e: MiaoHttpException) {
        when (e) {
            // 网络错误
            is MiaoNetException -> toast(R.string.dialog_network_error_info)
            // 验证码错误
            is MiaoVerificationCodeException ->
                toast(R.string.toast_error_verification_code)
            is MiaoSendSMSExp ->
                toast(R.string.toast_error_send_sms_error)
            is MiaoSendSMSRepeat ->
                toast(R.string.toast_error_verification_phone_repeat)
            is MiaoSMSRequestToMuch ->
                toast(R.string.toast_error_sms_request_to_much)
            // 其他错误
            else -> {
                toastFail()
                logTagE(TAG, "其他错误!!", tr = e)
            }

        }
    }

    suspend fun getDevice() {
        lastNetwork = this::getDevice
        try {
            val info = AccountManager.getAccountList(email.value!!, captcha.value!!)
            deviceInfo.postValue(info)
        } catch (e: MiaoHttpException) {
            errorCode.postValue(e.code)
        }
    }

    suspend fun retry() = lastNetwork?.invoke()
}