package com.czur.starry.device.personalcenter.net

import com.czur.czurutils.encryption.md5
import com.czur.czurutils.log.logTagD
import com.czur.czurutils.log.logTagV
import com.czur.czurutils.log.logTagW
import com.czur.starry.device.baselib.common.Constants
import com.czur.starry.device.baselib.data.entity.AreaCode
import com.czur.starry.device.baselib.data.provider.UserHandler
import com.czur.starry.device.baselib.network.HttpManager
import com.czur.starry.device.baselib.network.core.common.ResCode
import com.czur.starry.device.baselib.network.core.exception.MiaoHttpException
import com.czur.starry.device.baselib.network.core.util.checkAndThrowException
import com.czur.starry.device.baselib.network.core.util.createMiaoHttpException
import com.czur.starry.device.baselib.utils.*
import com.czur.starry.device.personalcenter.bean.*
import kotlinx.coroutines.*

/**
 * Created by 陈丰尧 on 2/26/21
 */

object AccountManager {
    private const val TAG = "AccountManager"

    /**
     * 验证码发送方式
     */
    enum class VerificationCodeSendWay {
        PHONE,      // 通过电话号
        EMAIL,      // 通过电子邮件
    }

    val captchaTime = DifferentLiveData(-1L)
    var captchaDownJob: Job? = null
    private var deviceInfo: DeviceInfo? = null

    // 注册相关接口
    private val registerService: IRegisterService by lazy {
        HttpManager.getService()
    }

    // 登录相关接口
    private val loginService: ILoginService by lazy {
        HttpManager.getService()
    }

    // 信息相关接口
    private val infoService: IInfoService by lazy {
        HttpManager.getService()
    }

    // 账号相关接口
    private val accountService: IAccountServer by lazy {
        HttpManager.getService()
    }

    /**
     * 获取随机会议号
     */
    @Throws(MiaoHttpException::class)
    suspend fun obtainAccountId(): List<AccountNo> = withContext(Dispatchers.IO) {
        val entity = registerService.obtainDeviceId()
        checkAndThrowException(entity)
        entity.bodyList
    }

    /**
     * 获取验证码, 有两种方式: 手机号/电子邮件
     * @param sendTarget: 要把验证码发给谁, 传入手机号/电子邮件
     * @param sendWay:    确定验证码的发送方式
     */
    @Throws(MiaoHttpException::class)
    suspend fun obtainCaptchaCode(sendTarget: String, sendWay: VerificationCodeSendWay) =
        withContext(Dispatchers.IO) {
            logTagD(TAG, "获取验证码:$sendTarget")

            if (captchaTime.value ?: -1 > 0) {
                return@withContext
            }
            captchaTime.postValue(-1)
            startCaptchaCountDown()
            val result = when (sendWay) {
                VerificationCodeSendWay.PHONE -> registerService.obtainPhoneCode(sendTarget)
                VerificationCodeSendWay.EMAIL -> registerService.obtainEMailCode(sendTarget)
            }
            if (!result.isSuccess && result.code != ResCode.RESULT_CODE_VERIFICATION_CODE_HAS_SEND) {
                // 请求不成功并且不是发送短信失败, 取消倒计时
                stopCaptchaCountDown()
            }
            checkAndThrowException(result)
        }


    /**
     * 停止发送验证码的倒计时
     */
    private fun stopCaptchaCountDown() {
        logTagD(TAG, "清空之前倒计时")
        captchaDownJob?.cancel()
        captchaTime.postValue(-1)
    }

    /**
     * 开始发送验证码的倒计时
     */
    private fun startCaptchaCountDown() {
        stopCaptchaCountDown()
        captchaDownJob = CoroutineScope(Job()).launch {
            logTagD(TAG, "开始倒计时:${Thread.currentThread().name}")
            try {
                var countDown = 59L
                while (isActive && countDown >= 0) {
                    delay(ONE_SECOND)
                    captchaTime.postValue(countDown)
                    countDown--
                }
            } finally {
                captchaTime.postValue(-1)
            }
        }
    }

// TODO 注册/登录完成后, 还需要上传时区和语言
    /**
     * 注册
     */
    suspend fun register(
        accountNo: String,
        password: String,
        name: String,
        countryCode: String,
        bindInfo: BindInfo
    ): UserInfo = withContext(Dispatchers.IO) {
        val result = registerService.register(
            accountNo = accountNo,
            mobile = bindInfo.phone,
            mail = bindInfo.email,
            password = password,
            code = bindInfo.verificationCode,
            sn = Constants.SERIAL,
            countryCode = countryCode,
            name = name,
        )
        checkAndThrowException(result)

        val userInfo = result.body
        userInfo.mobile = bindInfo.phone ?: ""
        userInfo.mail = bindInfo.email ?: ""
        saveUserInfo(userInfo, password)
        userInfo
    }

    /**
     * 登录
     */
    suspend fun login(
        accountNo: String,
        pwd: String,
    ): UserInfo = withContext(Dispatchers.IO) {
        val result = loginService.login(accountNo, runBlocking { pwd.md5() }.uppercase())
        checkAndThrowException(result)
        logTagD(TAG, "登录接口请求")
        val userInfo = result.body
        // 记录用户信息
        saveUserInfo(userInfo, pwd)
        userInfo
    }

    /**
     * 根据设备号查找手机
     */
    suspend fun getAccountList(
        mobile: String, // 手机号
        code: String,
    ): DeviceInfo = withContext(Dispatchers.IO) {
        logTagV(TAG, "获取密保手机下的设备:邮箱$mobile, 验证码$code")
        deviceInfo = null
        val result = loginService.getAccountList(mobile, code)
        checkAndThrowException(result)

        val deviceInfo = result.body
        deviceInfo
    }

    /**
     * 重设密码
     */
    suspend fun resetPwd(
        accountNo: String,
        password: String,
        mobile: String,
        token: String
    ): Unit = withContext(Dispatchers.IO) {
        val result = loginService.updatePwd(accountNo, password, mobile, token, String::class.java)
        checkAndThrowException(result)
        logTagD(TAG, "设置密码成功")
    }

    /**
     * 获取国家列表
     */
    suspend fun getCountries(): List<AreaCode> = withContext(Dispatchers.IO) {
        val result =
            infoService.getCountries()
        checkAndThrowException(result)
        logTagD(TAG, "获取国家列表成功")
        result.bodyList
    }

    /**
     * 获取用户信息
     * 当用户信息有可能改变时调用
     */
    suspend fun getPersonalInfo(): UpdateUser = withContext(Dispatchers.IO) {
        val result = infoService.getPersonalInfo()
        checkAndThrowException(result)
        logTagD(TAG, "获取用户信息成功")
        result.body
    }

    /**
     * 记录用户个人信息
     */
    private fun saveUserInfo(userInfo: UserInfo, pwd: String) {
        logTagD(TAG, "记录用户信息")
        // 记录用户数据

        // 对于阿里云来说, 以 / 结尾代表文件夹
        // 根路径一定是一个文件夹, 所以后台如果返回的数据不对, 则修正它
        val dirPath =
            if (userInfo.dirPath.endsWith("/")) userInfo.dirPath else "${userInfo.dirPath}/"
        UserHandler.login(
            token = userInfo.token,
            accountNo = userInfo.accountNo,
            nickName = userInfo.name,
            mobile = userInfo.mobile,
            email = userInfo.mail,
            pwd = runBlocking { pwd.md5().uppercase() },
            bucketName = userInfo.bucketName,
            endpoint = userInfo.endpoint,
            czurId = userInfo.czurId,
            dirPath = dirPath,
            headImage = userInfo.headImage,
            countryCode = userInfo.countryCode
        )
    }

    /**
     * 获取旧手机的验证码
     */
    suspend fun getOldPhoneCaptcha(
        mobile: String,
    ): Result<String> = withContext(Dispatchers.IO) {
        val result = accountService.getOldPhoneCaptcha(mobile)
        if (result.isSuccess) {
            logTagD(TAG, "获取旧手机验证码成功")
            Result.success(result.body)
        } else {
            logTagW(TAG, "获取旧手机验证码失败")
            Result.failure(createMiaoHttpException(result))
        }
    }

    /**
     * 验证旧手机的验证码
     */
    suspend fun validateOldPhone(
        mobile: String,
        code: String,
    ): Result<Unit> = withContext(Dispatchers.IO) {
        val result = accountService.validateOldPhone(mobile, code)
        if (result.isSuccess) {
            logTagD(TAG, "验证旧手机验证码成功")
            Result.success(Unit)
        } else {
            logTagW(TAG, "验证旧手机验证码失败")
            Result.failure(createMiaoHttpException(result))
        }
    }

    suspend fun deactivate(code: String): Result<Unit> = withContext(Dispatchers.IO) {
        val miaoEntity = accountService.deactivateAccount(code)
        if (miaoEntity.isSuccess) {
            logTagD(TAG, "注销成功")
            Result.success(Unit)
        } else {
            logTagW(TAG, "注销失败")
            Result.failure(createMiaoHttpException(miaoEntity))
        }
    }

    /**
     * 获取新手机的验证码
     */
    suspend fun getNewPhoneCaptcha(
        mobile: String,
    ): Result<String> = withContext(Dispatchers.IO) {
        val result = accountService.getNewPhoneCaptcha(mobile)
        if (result.isSuccess) {
            logTagD(TAG, "获取新手机验证码成功")
            Result.success(result.body)
        } else {
            logTagW(TAG, "获取新手机验证码失败")
            Result.failure(createMiaoHttpException(result))
        }
    }

    /**
     * 更新手机号码
     */
    suspend fun updatePhoneNumber(
        mobile: String,
        code: String,
        updateToken: String,
    ): Result<Unit> = withContext(Dispatchers.IO) {
        val result = accountService.updatePhoneNumber(mobile, code, updateToken)
        if (result.isSuccess) {
            logTagD(TAG, "更新手机号码成功")
            Result.success(Unit)
        } else {
            logTagW(TAG, "更新手机号码失败")
            Result.failure(createMiaoHttpException(result))
        }
    }

    /**
     * 获取AITrans信息
     */
    suspend fun getAITransInfo(): Result<AITransMemberInfo> = withContext(Dispatchers.IO) {
        val result = accountService.getAITransInfo()
        if (result.isSuccess) {
            logTagD(TAG, "获取AITrans信息成功")
            Result.success(result.body)
        } else {
            logTagW(TAG, "获取AITrans信息失败")
            Result.failure(createMiaoHttpException(result))
        }
    }

    /**
     * 更新昵称
     * @param newNickName: 新的昵称
     */
    suspend fun updateNickName(newNickName: String): Result<Unit> = withContext(Dispatchers.IO) {
        val result = accountService.updateNickName(newNickName)
        if (result.isSuccess) {
            logTagD(TAG, "更新昵称成功")
            Result.success(Unit)
        } else {
            logTagW(TAG, "更新昵称失败")
            Result.failure(createMiaoHttpException(result))
        }
    }

}