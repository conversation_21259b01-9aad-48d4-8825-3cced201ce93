package com.czur.starry.device.personalcenter.account.ai.order

import android.view.ViewGroup
import com.czur.starry.device.baselib.base.BaseDifferAdapter
import com.czur.starry.device.baselib.base.BaseVH
import com.czur.starry.device.personalcenter.R
import com.czur.uilib.choose.CZImageCheckBox

/**
 * Created by 陈丰尧 on 2025/4/23
 */
class OrderListAdapter : BaseDifferAdapter<OrderItemVO>() {
    // 选择项变化的回调
    var onSelectionChangedListener: (() -> Unit)? = null

    // 创建ViewHolder
    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): BaseVH {
        return BaseVH(R.layout.item_ai_trans_order, parent)
    }

    // 绑定数据到ViewHolder
    override fun bindViewHolder(holder: BaseVH, position: Int, itemData: OrderItemVO) {
        // 设置订单名称
        holder.setText(itemData.orderDetails.productAlias, R.id.orderNameTv)
        // 设置订单号
        holder.setText(itemData.orderDetails.orderNo, R.id.orderNumberTv)
        // 设置订单时间
        holder.setText(itemData.orderDetails.createTime.split(" ")[0], R.id.orderTimeTv)
        // 设置订单金额，保留2位小数
        holder.setText(
            String.format(null, "%.2f", itemData.orderDetails.orderPrice),
            R.id.itemPriceTv
        )
        // 不能开发票的隐藏选中框
        holder.visibleNotGone(itemData.canGetInvoiced, R.id.itemCheckBox)
        holder.visibleNotGone(!itemData.canGetInvoiced, R.id.receiveTv)
        holder.setAlphaBatch(
            if (itemData.canGetInvoiced) 1F else 0.3F,
            R.id.orderNameTv,
            R.id.orderNumberTv,
            R.id.orderTimeTv,
            R.id.itemPriceTv,
            R.id.unitTv
        )


        // 设置选中状态
        val checkBox = holder.getView<CZImageCheckBox>(R.id.itemCheckBox)
        checkBox.isChecked = itemData.isSelected

        // 设置点击事件
        checkBox.setOnClickListener {
            toggleSelection(position)
        }

        // 整个条目的点击事件
        holder.itemView.setOnClickListener {
            // 如果是可以开发票的，才可以选中
            if (itemData.canGetInvoiced) {
                // 触发选中状态的变化
                toggleSelection(position)
            }
        }
    }

    // 判断两个item是否是同一个
    override fun areItemsTheSame(oldItem: OrderItemVO, newItem: OrderItemVO): Boolean {
        return oldItem.orderDetails.id == newItem.orderDetails.id
    }

    // 判断两个item的内容是否相同
    override fun areContentsTheSame(oldItem: OrderItemVO, newItem: OrderItemVO): Boolean {
        // 特别注意选中状态的变化
        return oldItem.orderDetails == newItem.orderDetails && oldItem.isSelected == newItem.isSelected
    }

    // 单选逻辑：选择一个项目，取消选择其他项目
    private fun toggleSelection(position: Int) {
        val currentList = getDataList().toMutableList()
        val item = currentList[position]

        // 如果当前项已经选中，则取消选中
        if (item.isSelected) {
            item.isSelected = false
            notifyItemChanged(position)
        } else {
            // 先取消所有选中状态
            val previousSelectedIndex = currentList.indexOfFirst { it.isSelected }
            currentList.forEach { it.isSelected = false }

            // 然后选中当前项
            item.isSelected = true

            // 更新UI
            if (previousSelectedIndex >= 0) {
                notifyItemChanged(previousSelectedIndex)
            }
            notifyItemChanged(position)
        }

        setData(currentList)

        // 通知选择变化
        onSelectionChangedListener?.invoke()
    }


    // 获取选中的订单
    fun getSelectedOrders(): List<OrderItemVO> {
        return getDataList().filter { it.isSelected }
    }
}