package com.czur.starry.device.personalcenter.bean

/**
 * Created by 陈丰尧 on 2025/4/24
 */
data class AITransMemberInfo(
    val accountNo: String,// 账号
    val expireTime: Long,//到期时间
    val remaining: Long,//剩余时长，毫秒
    val type: Int,//会员类型，1-包月会员，2-预付会员
) {
    val hasMemberTime: Boolean
        get() = expireTime > System.currentTimeMillis() || remaining > 0
    val realType: Int
        get() = if (type == 1) {
            // 包月会员
            if (expireTime > System.currentTimeMillis()) {
                1
            } else {
                0
            }
        } else {
            // 预付会员
            if (remaining > 0) {
                2
            } else {
                0
            }
        }
}