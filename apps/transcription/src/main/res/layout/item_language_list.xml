<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="222px"
    android:layout_height="wrap_content"
    android:background="@color/transparent">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/selectTargetLanguageCl"
        android:layout_width="222px"
        android:layout_height="50px"
        app:layout_constraintBottom_toBottomOf="@+id/translationComparison"
        app:layout_constraintStart_toEndOf="@+id/translationComparison"
        app:layout_constraintTop_toTopOf="@+id/translationComparison">

        <!-- CNEN -->
        <TextView
            android:id="@+id/selectTargetLanguageTv"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="24px"
            android:text="CN"
            android:textColor="@color/white"
            android:textSize="24px"
            android:textStyle="bold"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />


        <ImageView
            android:id="@+id/selectLanguageArrowRightIv"
            android:layout_width="32px"
            android:layout_height="32px"
            android:layout_marginLeft="10px"
            android:src="@drawable/ic_select_language"
            app:layout_constraintBottom_toBottomOf="parent"
            android:layout_marginEnd="10px"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent" />
    </androidx.constraintlayout.widget.ConstraintLayout>
</LinearLayout>