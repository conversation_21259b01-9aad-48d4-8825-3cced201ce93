<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="912px"
    android:layout_height="642px"
    android:background="@drawable/shape_blue_r_10"
    app:layout_constraintBottom_toBottomOf="parent"
    app:layout_constraintEnd_toEndOf="parent"
    app:layout_constraintStart_toStartOf="parent"
    app:layout_constraintTop_toTopOf="parent">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/titleCl"
        android:layout_width="match_parent"
        android:layout_height="86px"
        android:background="@drawable/shape_blue_r_10_r"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">
        <!-- AI互译字幕 -->
        <TextView
            android:id="@+id/title"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="54px"
            android:text="@string/str_translation_setting_name"
            android:textColor="@color/white"
            android:textSize="48px"
            android:textStyle="bold"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <TextView
            android:id="@+id/title2"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/str_translation_setting_name_small"
            android:textColor="@color/white"
            android:textSize="30px"
            android:textStyle="bold"
            app:layout_constraintBottom_toBottomOf="@+id/title"
            app:layout_constraintStart_toEndOf="@+id/title" />

        <ImageView
            android:id="@+id/closeIv"
            style="@style/icon_copy_dialog"
            android:layout_marginEnd="8px"
            app:float_tips="@string/float_tip_close"
            android:src="@drawable/ic_setting_close_window"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

    </androidx.constraintlayout.widget.ConstraintLayout>

    <androidx.constraintlayout.widget.Group
        android:id="@+id/showTypeGroup"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:visibility="gone"
        app:constraint_referenced_ids="displayModeLabel,displaySelfTv,displayBothTv,displaySelfCB,displayBothCB" />
    <!-- 显示方式 -->
    <TextView
        android:id="@+id/displayModeLabel"
        style="@style/White30PxBoldText"
        android:layout_marginStart="133px"
        android:layout_marginTop="64px"
        android:text="显示方式"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/titleCl" />

    <!-- 展示给自己（本地会议+视频会议） -->
    <!--        <RadioButton-->
    <!--            android:id="@+id/displaySelf"-->
    <!--            android:layout_width="wrap_content"-->
    <!--            android:layout_height="wrap_content"-->
    <!--            android:layout_marginTop="8dp"-->
    <!--            android:text="展示给自己（本地会议+视频会议）"-->
    <!--            android:textColor="@color/white"-->
    <!--            app:layout_constraintStart_toStartOf="parent"-->
    <!--            app:layout_constraintTop_toBottomOf="@id/displayModeLabel" />-->

    <com.czur.uilib.choose.CZImageCheckBox
        android:id="@+id/displaySelfCB"
        style="@style/CBSubSize"
        android:layout_marginStart="133px"
        android:layout_marginTop="32px"
        app:checked="false"
        app:checkedImg="@drawable/file_icon_share_checked"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/displayModeLabel"
        app:unCheckedImg="@drawable/file_icon_share_unchecked"
        app:withClickIDs="noiseReductionHighTv" />

    <TextView
        android:id="@+id/displaySelfTv"
        style="@style/White30PxBoldText"
        android:layout_marginStart="16px"
        android:text="展示给自己（本地会议+视频会议）"
        app:layout_constraintBottom_toBottomOf="@+id/displaySelfCB"
        app:layout_constraintStart_toEndOf="@+id/displaySelfCB"
        app:layout_constraintTop_toTopOf="@+id/displaySelfCB" />

    <!-- 展示给自己和对方（视频会议） -->
    <com.czur.uilib.choose.CZImageCheckBox
        android:id="@+id/displayBothCB"
        style="@style/CBSubSize"
        android:layout_marginStart="133px"
        android:layout_marginTop="32px"
        app:checked="false"
        app:checkedImg="@drawable/file_icon_share_checked"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/displaySelfCB"
        app:unCheckedImg="@drawable/file_icon_share_unchecked"
        app:withClickIDs="noiseReductionHighTv" />

    <TextView
        android:id="@+id/displayBothTv"
        style="@style/White30PxBoldText"
        android:layout_marginStart="16px"
        android:text="展示给自己和对方（视频会议）"
        app:layout_constraintBottom_toBottomOf="@+id/displayBothCB"
        app:layout_constraintStart_toEndOf="@+id/displayBothCB"
        app:layout_constraintTop_toTopOf="@+id/displayBothCB" />


    <!-- 仅字幕 -->
    <com.czur.uilib.choose.CZImageCheckBox
        android:id="@+id/subtitlesOnlyCB"
        style="@style/CBSubSize"
        android:layout_marginStart="232px"
        android:layout_marginTop="44px"
        app:checked="true"
        app:checkedImg="@drawable/file_icon_share_not_check"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/titleCl"
        app:unCheckedImg="@drawable/file_icon_share_unchecked"
        app:withClickIDs="noiseReductionHighTv" />

    <TextView
        android:id="@+id/subtitlesOnlyTv"
        style="@style/White30PxBoldText"
        android:layout_marginStart="16px"
        android:text="@string/str_subtitles_only"
        app:layout_constraintBottom_toBottomOf="@id/subtitlesOnlyCB"
        app:layout_constraintStart_toEndOf="@+id/subtitlesOnlyCB"
        app:layout_constraintTop_toTopOf="@+id/subtitlesOnlyCB" />

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/selectSourceLanguageCl"
        android:layout_width="222px"
        android:layout_height="50px"
        android:layout_marginStart="32px"
        android:background="@drawable/shape_grey_blue_r_10"
        app:layout_constraintBottom_toBottomOf="@+id/subtitlesOnlyTv"
        app:layout_constraintStart_toEndOf="@+id/subtitlesOnlyTv"
        app:layout_constraintTop_toTopOf="@+id/subtitlesOnlyTv">

        <!-- CNEN -->
        <TextView
            android:id="@+id/selectSourceLanguageTv"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="24px"
            android:textColor="@color/white"
            android:textSize="24px"
            android:text=""
            android:textStyle="bold"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <ImageView
            android:layout_width="32px"
            android:layout_height="32px"
            android:layout_marginEnd="22px"
            android:rotation="180"
            android:src="@drawable/ic_language_select_arrow"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent" />
    </androidx.constraintlayout.widget.ConstraintLayout>

    <com.czur.uilib.choose.CZImageCheckBox
        android:id="@+id/translationComparisonCB"
        style="@style/CBSubSize"
        android:layout_marginTop="24px"
        app:checked="false"
        app:checkedImg="@drawable/file_icon_share_checked"
        app:layout_constraintEnd_toEndOf="@id/subtitlesOnlyCB"
        app:layout_constraintStart_toStartOf="@+id/subtitlesOnlyCB"
        app:layout_constraintTop_toBottomOf="@id/subtitlesOnlyCB"
        app:unCheckedImg="@drawable/file_icon_share_unchecked"
        app:withClickIDs="noiseReductionHighTv" />
    <!-- 翻译对照 -->
    <TextView
        android:id="@+id/translationComparison"
        style="@style/White30PxBoldText"
        android:layout_marginStart="16px"
        android:text="@string/str_translation_comparison"
        app:layout_constraintBottom_toBottomOf="@+id/translationComparisonCB"
        app:layout_constraintStart_toEndOf="@+id/translationComparisonCB"
        app:layout_constraintTop_toTopOf="@id/translationComparisonCB" />

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/selectTargetLanguageCl"
        android:layout_width="222px"
        android:layout_height="50px"
        android:layout_marginStart="32px"
        android:background="@drawable/shape_grey_blue_r_10"
        app:layout_constraintBottom_toBottomOf="@+id/translationComparison"
        app:layout_constraintStart_toEndOf="@+id/translationComparison"
        app:layout_constraintTop_toTopOf="@+id/translationComparison">

        <!-- CNEN -->
        <TextView
            android:id="@+id/selectTargetLanguageTv"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="24px"
            android:textColor="@color/white"
            android:textSize="24px"
            android:textStyle="bold"
            android:text=""
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <ImageView
            android:layout_width="32px"
            android:layout_height="32px"
            android:layout_marginEnd="22px"
            android:rotation="180"
            android:src="@drawable/ic_language_select_arrow"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent" />
    </androidx.constraintlayout.widget.ConstraintLayout>


    <com.czur.uilib.choose.CZImageCheckBox
        android:id="@+id/translationSpeechCB"
        style="@style/CBSubSize"
        android:layout_marginTop="24px"
        android:layout_marginStart="74px"
        app:checked="false"
        app:checkedImg="@drawable/file_icon_share_checked"
        app:layout_constraintStart_toStartOf="@+id/subtitlesOnlyCB"
        app:layout_constraintTop_toBottomOf="@id/translationComparisonCB"
        app:unCheckedImg="@drawable/file_icon_share_unchecked"
        app:withClickIDs="noiseReductionHighTv" />
    <TextView
        android:id="@+id/translationSpeechTv"
        style="@style/White30PxBoldText"
        android:layout_marginStart="16px"
        android:text="@string/str_translation_speech"
        app:layout_constraintBottom_toBottomOf="@+id/translationSpeechCB"
        app:layout_constraintStart_toEndOf="@+id/translationSpeechCB"
        app:layout_constraintTop_toTopOf="@id/translationSpeechCB" />

    <com.czur.uilib.choose.CZImageCheckBox
        android:id="@+id/translationTalkCB"
        style="@style/CBSubSize"
        android:layout_marginTop="16px"
        android:layout_marginStart="74px"
        app:checked="false"
        app:checkedImg="@drawable/file_icon_share_checked"
        app:layout_constraintStart_toStartOf="@+id/subtitlesOnlyCB"
        app:layout_constraintTop_toBottomOf="@id/translationSpeechCB"
        app:unCheckedImg="@drawable/file_icon_share_unchecked"
        app:withClickIDs="noiseReductionHighTv" />

    <TextView
        android:id="@+id/translationTalkTv"
        style="@style/White30PxBoldText"
        android:layout_marginStart="16px"
        android:text="@string/str_translation_talk"
        app:layout_constraintBottom_toBottomOf="@+id/translationTalkCB"
        app:layout_constraintStart_toEndOf="@+id/translationTalkCB"
        app:layout_constraintTop_toTopOf="@id/translationTalkCB" />


    <!-- 开始按钮 -->
    <com.czur.uilib.btn.CZButton
        android:id="@+id/startBtn"
        android:layout_width="180px"
        android:layout_height="50px"
        android:layout_marginBottom="52px"
        android:gravity="center"
        android:text="@string/str_start_translation"
        android:textColor="@color/white"
        android:textSize="20px"
        android:textStyle="bold"
        app:colorStyle="SkyBlueWhite"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent" />

    <TextView
        android:id="@+id/membershipExpiry"
        style="@style/White20PxBoldText"
        android:layout_marginEnd="20px"
        android:layout_marginBottom="7px"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent" />

    <TextView
        android:id="@+id/five_min_stop_tips"
        style="@style/White20PxBoldText"
        android:layout_marginEnd="20px"
        android:layout_marginBottom="7px"
        android:alpha="0.55"
        android:text="@string/str_translation_tips"
        android:textColor="#FFFFFF"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent" />

    <!-- 显示内容 -->

    <FrameLayout
        android:id="@+id/line"
        android:layout_width="match_parent"
        android:layout_height="1px"
        android:layout_marginStart="50px"
        android:layout_marginTop="32px"
        android:layout_marginEnd="50px"
        android:background="#33FFFFFF"
        app:layout_constraintTop_toBottomOf="@id/translationTalkCB" />
    <!-- 仅字幕 -->
    <com.czur.uilib.choose.CZImageCheckBox
        android:id="@+id/generateMeetingMinutesCB"
        style="@style/CBSubSize"
        android:layout_marginTop="32px"
        app:checked="false"
        app:checkedImg="@drawable/file_icon_share_checked"
        app:layout_constraintStart_toStartOf="@id/subtitlesOnlyCB"
        app:layout_constraintTop_toBottomOf="@+id/line"
        app:unCheckedImg="@drawable/file_icon_share_unchecked"
        app:withClickIDs="noiseReductionHighTv" />

    <TextView
        android:id="@+id/yesGenerateMeetingMinutesTv"
        style="@style/White30PxBoldText"
        android:layout_marginStart="16px"
        android:text="@string/str_generate_meeting_minutes"
        app:layout_constraintBottom_toBottomOf="@id/generateMeetingMinutesCB"
        app:layout_constraintStart_toEndOf="@+id/generateMeetingMinutesCB"
        app:layout_constraintTop_toTopOf="@+id/generateMeetingMinutesCB" />


    <!-- 仅字幕 -->

</androidx.constraintlayout.widget.ConstraintLayout>