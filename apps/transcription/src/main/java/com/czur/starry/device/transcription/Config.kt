package com.czur.starry.device.transcription

import android.media.AudioFormat
import android.media.MediaRecorder

/**
 * Created by 陈丰尧 on 2022/8/6
 */
object Config {

    /**
     * **********客户端给的错误码**********
     * 	enum StatusCode {
     *         CZSL_OK = 0,                         /* 成功执行函数 */
     *         CZSL_CONNECT_FAIL = -1,              /* 连接/重连时出错 */
     *         CZSL_SET_PARAM_FAIL = -2,            /* 客户端设置参数错误 */
     *         CZSL_LANG_NONE = -3,                 /* 语言列表为空 */
     *         CZSL_RUNTIME_ERR = -4,				/* 运行时错误 */
     *     };
     * 	**********服务端返回的错误码**********
     * 	//http请求阶段，对应到APP应该就是init的时候
     * 	status_code == 10000001 || status_code == 10000004 || status_code == 10000006
     * 	//收发阶段，对应到APP就是start之后，收到错误码直接就不能再用了
     * 	status_code == 30000001 || status_code == 30000002 || status_code == 30000003 || status_code == 30000004
     * 	|| status_code == 30000005 || status_code == 40000001 || status_code == 40000003 || status_code == 40000102
     * 	|| status_code == 40000103 || status_code == 40000104 || status_code == 50000001 || status_code == 50000003
     * 	//收发阶段，对应到APP就是start之后，收到错误码还可以继续用，可能是类似于警告之类的东西
     * 	status_code == 30000006 || status_code == 30000007
     *
     * 	// 初始化失败 -1是有可能服务器状态不问题456都是超量报错
     */
    val ERROR_INIT_FAILED_CODE_LIST = listOf<Int>(-1,10000001, 10000004, 10000005, 10000006)
    val ERROR_USING_NO_TIME_LIST = listOf<Int>(
        10000006,
        30000001,
        30000002
    ) // 使用过程中失败,没有会员时长了返回-501，"member expired init failed"。
    val ERROR_FAILED_CODE_LIST = listOf<Int>(
        -1,
        10000006,
        30000001,
        30000002,
        30000003,
        30000004,
        30000005,
        40000001,
        40000003,
        40000102,
        40000103,
        40000104,
        50000001,
        50000003
    ) // 使用过程中失败

    const val NET_CALLBACK_ERROR_CODE = -9527 //接口报错

    const val PREFERENCE_NAME = "aiTranscription"
    const val SOURCE_LANG = "sourceLanguage"
    const val TARGET_LANG = "targetLanguage"
    const val SHOW_CONTENT = "showContent"
    const val TRANS_CHILD_CONTENT = "transChild"
    const val GENERATE_MEETING_MINUTES = "generateMeetingMinutes"

    const val SHOW_CONTENT_TEXT = "1" // 实时字幕
    const val SHOW_CONTENT_TRANS = "0" // 现场互译
    const val DEFAULT_SHOW_CONTENT = SHOW_CONTENT_TEXT // 显示内容默认值

    const val TRANS_CHILD_SPEECH = "1" // 演讲模式
    const val TRANS_CHILD_TALK = "0" // 对话模式
    const val DEFAULT_TRANS_CHILD = TRANS_CHILD_SPEECH // 显示内容默认值

    const val RENAME_DIALOG_SHOWED_ACTION = "com.transcription.renameDialogShowed"
    const val START_MUXER_ACTION = "com.czur.starry.device.transcription.startMuxer"

    const val IS_PORT = false   //是否竖屏
    const val SRC_VIDEO_WIDTH_2160 = 3840
    const val SRC_VIDEO_HEIGHT_2160 = 2160

    const val SRC_VIDEO_WIDTH_1080 = 1920
    const val SRC_VIDEO_HEIGHT_1080 = 1080

    const val SRC_VIDEO_WIDTH_720 = 1280
    const val SRC_VIDEO_HEIGHT_720 = 720

    const val SRC_VIDEO_WIDTH_360 = 640
    const val SRC_VIDEO_HEIGHT_360 = 360

    var SRC_VIDEO_WIDTH = SRC_VIDEO_WIDTH_720
    var SRC_VIDEO_HEIGHT = SRC_VIDEO_HEIGHT_720

    const val BIG_TRANS_WIDTH = 1800
    const val BIG_TRANS_HEIGHT = 242
    const val BIG_TRANS_HEIGHT_TEXT = 172
    const val BIG_TRANS_X = 60
    const val BIG_TRANS_Y = 828
    const val BIG_TRANS_TEXT_Y = 898

    const val SCREEN_FLOAT_WIDTH = 371 // 屏幕录制浮窗默认宽度
    const val SCREEN_FLOAT_HEIGHT = 216 // 高度

    const val SCREEN_FLOAT_X =
        SRC_VIDEO_WIDTH_1080 - SCREEN_FLOAT_WIDTH - 10 // 屏幕录制浮窗默认位置x 10是悬浮窗默认距离边缘的距离
    const val SCREEN_FLOAT_Y = 52 // y

    const val AUDIO_FLOAT_WIDTH = 416 // 音频录制浮窗默认宽度
    const val AUDIO_FLOAT_HEIGHT = 105 // 高度

    const val AUDIO_FLOAT_X =
        SRC_VIDEO_WIDTH_1080 - AUDIO_FLOAT_WIDTH - 10 // 音频录制浮窗默认位置x 10是悬浮窗默认距离边缘的距离
    const val AUDIO_FLOAT_Y = 52 // y


    const val TIME_FLOAT_WIDTH = 350 // 浮窗默认宽度
    const val TIME_FLOAT_HEIGHT = 60 // 高度

    const val TIME_FLOAT_X = SRC_VIDEO_WIDTH_1080 - TIME_FLOAT_WIDTH - 10 // 浮窗默认位置x
    const val TIME_FLOAT_Y = SRC_VIDEO_HEIGHT_1080 - TIME_FLOAT_HEIGHT - 10 // y 10是悬浮窗默认距离边缘的距离


    // 时间水印默认状态
    const val DEF_ENABLE_TIME_WATERMARK = true

    // 录像开始等待时间
    const val PREPARE_TIME_IN_SECOND = 3

    // 水印位置
    const val TIME_WATER_MARK_OFFSET_X = 990 // 按照1280算的
    const val TIME_WATER_MARK_OFFSET_Y = 720 - 16 - 25   // 按照720算的 水印位置在右下角, 边距25, 文字高度16
    const val TIME_WATER_MARK_PATTERN = "yyyy-MM-dd HH:mm:ss"
    const val TIME_WATER_MARK_PATTERN_EN = "MM-dd-yyyy HH:mm:ss"  // 英文版水印格式

    // 音频录制源
//    const val AUDIO_SOURCE = MediaRecorder.AudioSource.MIC
    const val AUDIO_SOURCE = MediaRecorder.AudioSource.MIC
    const val AUDIO_SOURCE_SUBMIX = MediaRecorder.AudioSource.REMOTE_SUBMIX

    // 音频采样率
    const val AUDIO_SAMPLE_RATE = 16000 // 设备平台只支持 16000的采样率
    const val AUDIO_ENCODING_BIT_RATE = 96000   // 设置编码器的码率

    // 控制栏是否自动隐藏
    const val CONTROL_BAR_AUTO_HIDE = false

    // 拍照文件的扩展名
    const val PHOTO_FILE_EXTENSION = "jpg"
    const val PHOTO_FILE_FOLDER_NAME = "Pictures/Screenshots"

    const val PHOTO_FILE_WATERMARK_FORMAT = "yyyy/MM/dd HH:mm:ss"

    const val OPEN_CAMERA_RETRY_TIMES = 6


//    const val SHOW_TYPE_SELF = 0// 展示给自己
//    const val SHOW_TYPE_BOTH = 1// 展示给自己和对方
//    const val DEFAULT_SHOW_TYPE = SHOW_TYPE_SELF// 显示方式默认值
//
//    const val SHOW_CONTENT_TEXT = 0 // 仅字幕
//    const val SHOW_CONTENT_TRANS = 1 // 翻译对照
//    const val DEFAULT_SHOW_CONTENT = SHOW_CONTENT_TEXT // 显示内容默认值

}