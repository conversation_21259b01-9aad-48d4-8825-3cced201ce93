package com.czur.starry.device.transcription.adapter

import android.content.Context
import android.graphics.PorterDuff
import android.view.ViewGroup
import android.widget.ImageView
import androidx.core.content.ContextCompat
import com.czur.starry.device.baselib.base.BaseDifferAdapter
import com.czur.starry.device.baselib.base.BaseVH
import com.czur.starry.device.transcription.R
import com.czur.starry.device.transcription.activity.TransSettingWindowActivity
import com.czur.starry.device.transcription.model.LanguageBean

class LanguageListAdapter : BaseDifferAdapter<LanguageBean>() {
    override fun bindViewHolder(holder: BaseVH, position: Int, itemData: LanguageBean) {

        holder.setText(itemData.languageStr, R.id.selectTargetLanguageTv)

        if (itemData.isSelected) {
            holder.visible(true, R.id.selectLanguageArrowRightIv)
            holder.setTextColorRes(R.color.white, R.id.selectTargetLanguageTv)
        } else {
            holder.visible(false, R.id.selectLanguageArrowRightIv)
            holder.setTextColorRes(R.color.btn_bg_back, R.id.selectTargetLanguageTv)
        }
    }

    override fun areItemsTheSame(oldItem: LanguageBean, newItem: LanguageBean): Boolean {
        return oldItem.id == newItem.id
    }

    override fun areContentsTheSame(oldItem: LanguageBean, newItem: LanguageBean): Boolean {
        return oldItem == newItem
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): BaseVH {
        return BaseVH(R.layout.item_language_list, parent)
    }

    var context: Context? = null
    fun setContext(transSettingWindowActivity: TransSettingWindowActivity) {
        context = transSettingWindowActivity
    }
}