package com.czur.starry.device.transcription.util

import android.content.Context
import android.graphics.Canvas
import android.text.Layout
import android.text.StaticLayout
import android.text.TextPaint
import android.util.AttributeSet
import android.util.TypedValue
import androidx.appcompat.widget.AppCompatTextView

/**
 * 自定义TextView，支持设置固定行高
 */
class FixedLineHeightTextView @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
) : AppCompatTextView(context, attrs, defStyleAttr) {

    private var mLineHeight = 0

    /**
     * 设置固定行高（像素值）
     */
    fun setLineHeightLine(lineHeight: Int) {
        mLineHeight = lineHeight
        // 强制重新布局
        requestLayout()
        invalidate()
    }

    /**
     * 设置固定行高（dp值）
     */
    fun setLineHeightDP(lineHeightDP: Float) {
        val lineHeight = TypedValue.applyDimension(
            TypedValue.COMPLEX_UNIT_DIP,
            lineHeightDP,
            resources.displayMetrics
        ).toInt()
        setLineHeightLine(lineHeight)
    }

    /**
     * 设置固定行高（px值）
     */
    fun setLineHeightPX(lineHeightPX: Int) {
        setLineHeightLine(lineHeightPX)
    }

    override fun onMeasure(widthMeasureSpec: Int, heightMeasureSpec: Int) {
        super.onMeasure(widthMeasureSpec, heightMeasureSpec)

        if (mLineHeight > 0) {
            val text = text
            if (text.isNotEmpty()) {
                val textPaint = TextPaint(paint)
                textPaint.textSize = textSize

                val width = measuredWidth - paddingLeft - paddingRight
                if (width > 0) {
                    val layout = StaticLayout.Builder.obtain(
                        text, 0, text.length, textPaint, width
                    )
                        .setAlignment(Layout.Alignment.ALIGN_NORMAL)
                        .setIncludePad(false)
                        .build()

                    val lineCount = layout.lineCount

                    // 单行时增加额外高度以容纳下降字符
                    val extraHeight = if (lineCount == 1) 6 else 0
                    val height = lineCount * mLineHeight + paddingTop + paddingBottom + extraHeight

                    setMeasuredDimension(measuredWidth, height)
                }
            }
        }
    }

    override fun onDraw(canvas: Canvas) {
        if (mLineHeight <= 0 || text.isEmpty()) {
            super.onDraw(canvas)
            return
        }

        val textPaint = paint
        textPaint.color = currentTextColor
        textPaint.drawableState = drawableState

        val width = width - paddingLeft - paddingRight
        val layout = StaticLayout.Builder.obtain(
            text, 0, text.length, textPaint, width
        )
            .setAlignment(Layout.Alignment.ALIGN_NORMAL)
            .setIncludePad(false)
            .build()

        canvas.save()
        canvas.translate(paddingLeft.toFloat(), paddingTop.toFloat())

        val lineCount = layout.lineCount
        for (i in 0 until lineCount) {
            val lineStart = layout.getLineStart(i)
            val lineEnd = layout.getLineEnd(i)
            val line = text.subSequence(lineStart, lineEnd).toString()

            // 计算基线位置，使文本垂直居中于行高
            val fontMetrics = textPaint.fontMetrics
            val textHeight = fontMetrics.descent - fontMetrics.ascent

            // 计算基线位置，使文本垂直居中于行高
            val baselineOffset = (mLineHeight - textHeight) / 2 - fontMetrics.ascent

            // 靠左对齐，直接从左边开始绘制
            val xPosition = 0f

            // 绘制文本
            canvas.drawText(line, xPosition, i * mLineHeight + baselineOffset, textPaint)
        }

        canvas.restore()
    }
}
