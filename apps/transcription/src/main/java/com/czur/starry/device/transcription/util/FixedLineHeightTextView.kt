package com.czur.starry.device.transcription.util

import android.content.Context
import android.graphics.Canvas
import android.text.Layout
import android.text.StaticLayout
import android.text.TextPaint
import android.util.AttributeSet
import android.util.TypedValue
import androidx.appcompat.widget.AppCompatTextView

/**
 * 自定义TextView，支持设置固定行高
 */
class FixedLineHeightTextView @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
) : AppCompatTextView(context, attrs, defStyleAttr) {

    private var mLineHeight = 0

    /**
     * 设置固定行高（像素值）
     */
    fun setLineHeightLine(lineHeight: Int) {
        mLineHeight = lineHeight
        // 强制重新布局
        requestLayout()
        invalidate()
    }

    /**
     * 设置固定行高（dp值）
     */
    fun setLineHeightDP(lineHeightDP: Float) {
        val lineHeight = TypedValue.applyDimension(
            TypedValue.COMPLEX_UNIT_DIP,
            lineHeightDP,
            resources.displayMetrics
        ).toInt()
        setLineHeightLine(lineHeight)
    }

    /**
     * 设置固定行高（px值）
     */
    fun setLineHeightPX(lineHeightPX: Int) {
        setLineHeightLine(lineHeightPX)
    }

    override fun onMeasure(widthMeasureSpec: Int, heightMeasureSpec: Int) {
        super.onMeasure(widthMeasureSpec, heightMeasureSpec)

        if (mLineHeight > 0) {
            val text = text
            if (text.isNotEmpty()) {
                val textPaint = TextPaint(paint)
                textPaint.textSize = textSize

                val width = measuredWidth - paddingLeft - paddingRight
                if (width > 0) {
                    val layout = StaticLayout.Builder.obtain(
                        text, 0, text.length, textPaint, width
                    )
                        .setAlignment(Layout.Alignment.ALIGN_NORMAL)
                        .setIncludePad(false)
                        .build()

                    val lineCount = layout.lineCount
                    val height = lineCount * mLineHeight + paddingTop + paddingBottom

                    setMeasuredDimension(measuredWidth, height)
                }
            }
        }
    }

    override fun onDraw(canvas: Canvas) {
        if (mLineHeight <= 0 || text.isEmpty()) {
            super.onDraw(canvas)
            return
        }

        val textPaint = paint
        textPaint.color = currentTextColor
        textPaint.drawableState = drawableState

        val width = width - paddingLeft - paddingRight
        val layout = StaticLayout.Builder.obtain(
            text, 0, text.length, textPaint, width
        )
            .setAlignment(Layout.Alignment.ALIGN_NORMAL)
            .setIncludePad(false)
            .build()

        canvas.save()
        canvas.translate(paddingLeft.toFloat(), paddingTop.toFloat())

        val lineCount = layout.lineCount
        val fontMetrics = textPaint.fontMetrics

        // 计算实际文本高度（包括上升和下降部分）
        val actualTextHeight = fontMetrics.descent - fontMetrics.ascent

        // 确保固定行高至少能容纳完整的文本高度
        val effectiveLineHeight = maxOf(mLineHeight.toFloat(), actualTextHeight)

        for (i in 0 until lineCount) {
            val lineStart = layout.getLineStart(i)
            val lineEnd = layout.getLineEnd(i)
            val line = text.subSequence(lineStart, lineEnd).toString()

            // 改进的基线计算：确保文本在行高内垂直居中，并为下降字符预留足够空间
            // 计算文本应该在行内的垂直居中位置
            val centerY = i * effectiveLineHeight + effectiveLineHeight / 2

            // 基线位置 = 中心位置 - (ascent + descent) / 2 - ascent
            // 这样可以确保文本的视觉中心在行的中心，同时保证下降字符有足够空间
            val baselineY = centerY - (fontMetrics.ascent + fontMetrics.descent) / 2 - fontMetrics.ascent

            // 靠左对齐，直接从左边开始绘制
            val xPosition = 0f

            // 绘制文本
            canvas.drawText(line, xPosition, baselineY, textPaint)
        }

        canvas.restore()
    }
}
