package com.czur.starry.device.update

import android.content.Context
import android.content.Intent
import android.hardware.usb.UsbDevice
import android.net.ConnectivityManager
import android.os.Environment
import android.os.IBinder
import androidx.lifecycle.LifecycleService
import com.czur.czurutils.log.logTagD
import com.czur.czurutils.log.logTagE
import com.czur.czurutils.log.logTagV
import com.czur.starry.device.baselib.base.CZURAtyManager
import com.czur.starry.device.baselib.common.Constants
import com.czur.starry.device.baselib.common.Constants.PATH_SDCARD_OTA_CAMERA
import com.czur.starry.device.baselib.data.sp.SPHandler
import com.czur.starry.device.baselib.utils.AudioUtil
import com.czur.starry.device.baselib.utils.InternetStatus
import com.czur.starry.device.baselib.utils.NetStatusUtil
import com.czur.starry.device.baselib.utils.isNetworkConnected
import com.czur.starry.device.baselib.utils.launch
import com.czur.starry.device.baselib.utils.prop.getBooleanSystemProp
import com.czur.starry.device.baselib.utils.prop.setBooleanSystemProp
import com.czur.starry.device.otalib.OTAHandler
import com.czur.starry.device.otalib.OTAHandler.allMeetingStateLive
import com.czur.starry.device.otalib.OTAHandler.allMeetingStatus
import com.czur.starry.device.otalib.OTAHandler.cameraUpgradeSuccess
import com.czur.starry.device.otalib.OTAHandler.cameraVersionCheck
import com.czur.starry.device.otalib.OTAHandler.cameraVersionCheckLive
import com.czur.starry.device.otalib.OTAHandler.currentCameraVersion
import com.czur.starry.device.otalib.OTAHandler.newCameraVersion
import com.czur.starry.device.settings.model.FWVersionModel
import com.czur.starry.device.update.UpdateApp.Companion.app
import com.czur.starry.device.update.task.OTATask.Companion.isFree
import com.czur.starry.device.update.task.OTATask.Companion.isOTARunning
import com.czur.starry.device.update.ui.UpdateDialog
import com.czur.starry.device.update.ui.VerifyDialog
import com.czur.starry.device.update.utils.CameraOTAUtil
import com.czur.starry.device.update.utils.CameraOTAUtil.cameraOtaTask
import com.czur.starry.device.update.utils.CameraOTAUtil.cameraTimerTask
import com.czur.starry.device.update.utils.CameraOTAUtil.startCameraTask
import com.czur.starry.device.update.utils.ClickDropOTAUtil.startClickDropTask
import com.czur.starry.device.update.utils.HandleUtils
import com.czur.starry.device.update.utils.HandleUtils.checkRequest
import com.czur.starry.device.update.utils.HandleUtils.getDownLoadSize
import com.czur.starry.device.update.utils.HandleUtils.otaTimerTask
import com.czur.starry.device.update.utils.HandleUtils.startOTATask
import com.czur.starry.device.update.utils.HandleUtils.timerTask
import com.czur.starry.device.update.utils.RKRecoverySystem
import com.czur.starry.device.update.utils.RTMUtil
import com.czur.starry.device.update.utils.RTMUtil.requestToken
import com.czur.starry.device.update.utils.ToastUtil
import com.serenegiant.usb.USBMonitor
import com.serenegiant.usb.UVCCamera
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.runBlocking
import kotlinx.coroutines.withContext
import java.util.concurrent.atomic.AtomicBoolean

class UpdateService : LifecycleService() {
    companion object {
        private const val TAG = "UpdateService"
        private const val DATA_ROOT = "/data/media/0"
        private const val KEY_CACHE_RECOVERY_DEL = "vendor.czur.cache.del"
        const val IS_SUPPORT_USB_UPDATE = true

        //所有视频会议prop值
        private var FLASH_ROOT = Environment.getExternalStorageDirectory().absolutePath
        private const val COMMAND_FLAG_SUCCESS = "success"
        private const val COMMAND_FLAG_UPDATING = "updating"
        private var mIsFirstStartUp = true

        //是否中断提示升级
        private var isInterruptUpdateDialog = false
        private var isUpdateFinish = false
        var searchResult: Array<String>? = null
        var fwVersion: FWVersionModel? = null
        const val COMMAND_CHECK_LOCAL_UPDATING = 1
        const val COMMAND_CHECK_USB_UPDATING = 1 shl 1

        const val EXTRA_IMAGE_PATH = "android.rockchip.update.extra.IMAGE_PATH";

        val rtmLoginListener = MutableSharedFlow<Boolean>()

        private var isSuccessLogin = false
    }

    private lateinit var mUsbMonitor: USBMonitor
    private var uvcCamera: UVCCamera? = null
    private var isCameraRunningUpdate = AtomicBoolean(false) //是否正在升级

    private var updateDialog: UpdateDialog? = null
    private var path = ""
    private val IMAGE_FILE_DIRS = arrayOf<String>(
        "$DATA_ROOT/",
        "$FLASH_ROOT/"
    )

    //网络监听
    private val netStatusUtil = NetStatusUtil(this)

    //网络状态
    private var netState = true

    private var lastMeetingStateLocalCache: Boolean? = null

    private val audioUtil: AudioUtil by lazy {
        AudioUtil()
    }


    override fun onBind(intent: Intent): IBinder {
        super.onBind(intent)
        return binder
    }

    //对应settings调用
    private val binder = object : IUpdateManager.Stub() {
        override fun checkVersion(): String {
            runBlocking {
                val fwVersionModel = HandleUtils.checkRequest()
                fwVersion = fwVersionModel
            }
            if (null == fwVersion || fwVersion!!.update == 0) {
                return "0"
            } else {
                SPHandler.updateInfo = fwVersion?.version
                if (SPHandler.firmwareUpdateVersion != fwVersion?.version) {
                    SPHandler.isreadyForUpdate = false
                    SPHandler.firmwareSize = fwVersion!!.fileSize.toString()
                    startOTATask()
                }
                return fwVersion!!.version
            }

        }

        override fun downLoadFirmware() {
            startOTATask()
        }

        override fun upgrade() {
            launch {
                searchResult = RKRecoverySystem.getValidFirmwareImageFile(IMAGE_FILE_DIRS)
                if (null != searchResult) {
                    if (1 == searchResult!!.size) {
                        path = searchResult!![0]
                    }
                }
                SPHandler.firmwareUpdateFilePath = path
                logTagD(
                    TAG,
                    "=====SPHandler.FirmwareUpdateFilePath==$path"
                )
                //安装升级包
                VerifyDialog(app, path).show()
            }
        }

        override fun downLoadSize(): Int {
            return getDownLoadSize()
        }

        override fun cancelDownLoad() {
            isFree.set(false)
            isOTARunning.set(false)
        }


        override fun upgradeCamera() {
            //升级4k camera固件
            isCameraRunningUpdate.set(true)
            prepareUSBMonitor()
        }
    }


    override fun onCreate() {
        super.onCreate()
        logTagD(TAG, "====onCreate")

        //whether is UMS or m-user
        if (RKRecoverySystem.getMultiUserState()) {
            FLASH_ROOT = DATA_ROOT
        }
        if (mIsFirstStartUp) {
            mIsFirstStartUp = false
            //检测升级文件
            fistStartUpCheck()
        }

        //监听所有视频会议和本地会议录像
        allMeetingStateLive.observe(this@UpdateService) {
            logTagD(TAG, "===allMeetingStateLive==$it")
            if (it) {
                //RTM退出
                RTMUtil.stopRTM()
                //停止下载
                stopTask()
            } else {
                startOTATask()
                if (Constants.starryHWInfo.cameraInfo.canOTA) {
                    startCameraTask()
                }
                startClickDropTask()

                launch(Dispatchers.IO) {
                    startRTMLink()
                }
                if (isInterruptUpdateDialog) {
                    launch {
                        checkLocalPackage()
                    }
                }
            }
        }
        //网络监听
        netStatusUtil.internetStatusLive.observe(this) {
            logTagD(TAG, "===连接状态==${it}")
            netState = it == InternetStatus.CONNECT
            if (netState && !allMeetingStatus) {
                launch(Dispatchers.IO) {
                    startRTMLink()
                }
                launch(Dispatchers.IO) {
                    delay(5000)
                    if (isNetworkConnected() && !OTAHandler.newVersionStatus) checkRequest()
                }
                launch(Dispatchers.IO) {
                    delay(5000)
                    if (isNetworkConnected()) {
                        if (Constants.starryHWInfo.cameraInfo.canOTA) {
                            //4K camera检测线上版本，延迟15秒，确保获取到本地camera版本
                            startCameraTask()
                        }
                        //clickDrop模块只负责下载新固件，宜享负责升级。
                        startClickDropTask()
                    }
                }
            }
        }

        //网络监听
        netStatusUtil.startWatching()

        //登陆成功后不在重新注册登陆
        launch {
            rtmLoginListener.collect {
                logTagD(TAG, "====rtmLoginListener=$it")
                isSuccessLogin = it
            }
        }

        //视频会议检测
        startCheckMeetingState()

        //检测camera当前版本
        launch {
            if (Constants.starryHWInfo.cameraInfo.canOTA) {
                if (newCameraVersion == "null" && currentCameraVersion == "400") {
                    //升级完成并且本地版本号是默认版本号，开始检测本地4k camera版本
                    prepareUSBMonitor()
                }
            }
        }

        cameraVersionCheckLive.observe(this@UpdateService) {
            if (it) {
                if (!allMeetingStatus && Constants.starryHWInfo.cameraInfo.canOTA) {
                    CameraOTAUtil.startCameraTask()
                    logTagD(TAG, "=======cameraVersionCheck=$it")
                }
                cameraVersionCheck = false
            }
        }

    }

    /**
     * 启动循环获取prop值判断是否开启视频会议
     */
    private fun startCheckMeetingState() {
        launch(Dispatchers.IO) {
            while (true) {
                getMeetingProp()
                delay(2000)
            }
        }
    }


    @Synchronized
    private fun prepareUSBMonitor() {
        mUsbMonitor = USBMonitor(this@UpdateService, onDeviceConnectedListener)
        mUsbMonitor.register()
    }

    private fun releaseCamera() {
        if (uvcCamera != null) {
            uvcCamera!!.close()
            uvcCamera = null
        }
        mUsbMonitor.unregister()
    }

    private fun getCameraVersion() {
        var version = ""
        if (uvcCamera != null) {
            version = uvcCamera!!.camVersion
        }
        logTagD(TAG, "====uvcCamera!!.camVersion=$version")
        currentCameraVersion = version.trim()
    }

    private suspend fun updateCamera(): Int = withContext(Dispatchers.IO) {
        var result = 0
        if (uvcCamera != null) {
            try {
                result = uvcCamera!!.updateCamera(PATH_SDCARD_OTA_CAMERA)
                logTagD(TAG, "=====updateCamera=result=$result")
            } catch (e: Exception) {
                result = -1
                releaseCamera()
                e.printStackTrace()
                logTagE(TAG, "=====updateCamera=exception=${e.message}")
            }
        }

        result
    }


    private fun getMeetingProp() {
        val isMeetingState = audioUtil.getUseMicPid().isNotEmpty()
        if (lastMeetingStateLocalCache != isMeetingState) {
            lastMeetingStateLocalCache = isMeetingState
            allMeetingStatus = isMeetingState
        }
    }

    private fun stopTask() {
        //停止下载
        isFree.set(false)
        if (timerTask != null) {
            timerTask?.cancel()
            timerTask?.purge()
            timerTask = null
        }
        if (otaTimerTask != null) {
            otaTimerTask?.cancel()
            otaTimerTask = null
        }
        //停止camera固件下载
        stopCameraTask()
    }

    private fun stopCameraTask() {
        if (cameraTimerTask != null) {
            cameraTimerTask?.cancel()
            cameraTimerTask?.purge()
            cameraTimerTask = null
        }
        if (cameraOtaTask != null) {
            cameraOtaTask?.cancel()
            cameraTimerTask = null
        }
    }

    override fun onDestroy() {
        super.onDestroy()
        netStatusUtil.stopWatching()
        RTMUtil.stopRTM()
        stopTask()
    }

    override fun onStartCommand(intent: Intent?, flags: Int, startId: Int): Int {
        super.onStartCommand(intent, flags, startId)
        logTagD(TAG, "====onStartCommand")
        val command = intent?.getIntExtra("command", COMMAND_CHECK_LOCAL_UPDATING)
            ?: COMMAND_CHECK_LOCAL_UPDATING
        logTagD(TAG, "onStartCommand: command = $command")
        launch {
            //检测升级update包本地路径
            if (command and COMMAND_CHECK_LOCAL_UPDATING != 0) {
                logTagD(TAG, "检测升级update包:本地路径")
                checkLocalPackage()
            }

            //检测升级update包 usb路径
            if (command and COMMAND_CHECK_USB_UPDATING != 0) {
                logTagV(TAG, "检测升级update包:usb路径")
                searchResult = RKRecoverySystem.findFromSdOrUsb()
                startUpdateNotice(searchResult)
            }
        }

        return START_REDELIVER_INTENT
    }

    private suspend fun checkLocalPackage() {
        logTagD(
            TAG,
            "===getBooleanSystemProp==${getBooleanSystemProp(KEY_CACHE_RECOVERY_DEL, false)}"
        )
        checkClearRecoveryProp()
        //检测升级update包路径
        searchResult = RKRecoverySystem.getValidFirmwareImageFile(IMAGE_FILE_DIRS)

        if (isUpdateFinish) searchResult = null
        isUpdateFinish = false

        startUpdateNotice(searchResult)

    }

    private fun fistStartUpCheck() {
        //读取升级文件
        val command = RKRecoverySystem.readFlagCommand()
        var path = ""
        if (command != null) {
            if (command.contains("\$path")) {
                path = command.substring(command.indexOf('=') + 1)
                logTagD(TAG, "last_flag: path = $path")
                isUpdateFinish = true
                //删除安装包
                launch(Dispatchers.IO) { RKRecoverySystem.deletePackage(path) }
                //升级成功失败提示
                if (command.startsWith(COMMAND_FLAG_SUCCESS)) {
                    val toast = ToastUtil(this@UpdateService, true)
                    toast.show(5000)
                }
                if (command.startsWith(COMMAND_FLAG_UPDATING)) {
                    val toast = ToastUtil(this@UpdateService, false)
                    toast.show(5000)
                    SPHandler.isreadyForUpdate = false
                    SPHandler.firmwareUpdateVersion = "1"
                }
                OTAHandler.forceVersionStatus = false
            }
        }

    }


    private fun startUpdateNotice(searchResult: Array<String>?) {

        //检测升级update包路径
        if (null != searchResult) {
            if (1 == searchResult.size) {
                path = searchResult[0]
                SPHandler.firmwareUpdateFilePath = path
                logTagD(TAG, "=====path=$path=")

                if (!allMeetingStatus) {
                    isInterruptUpdateDialog = false

                    if (updateDialog != null && updateDialog!!.isShowing) return
                    updateDialog =
                        UpdateDialog(this, path, R.style.UpdateDialog, onCancelClick = { dialog ->
                            updateDialog?.dismiss()
                            updateDialog = null
                        })
                    updateDialog?.setCanceledOnTouchOutside(false)
                    updateDialog?.show()

                } else {
                    isInterruptUpdateDialog = true
                }
            }

        }
    }

    private suspend fun checkClearRecoveryProp() = withContext(Dispatchers.IO) {
        var count = 0
        while (!getBooleanSystemProp(KEY_CACHE_RECOVERY_DEL, false)) {
            delay(1000)
            count++
            if (count == 40) {
                setBooleanSystemProp(KEY_CACHE_RECOVERY_DEL, true)
                break
            }
        }
        true
    }

    val connectivityManager: ConnectivityManager by lazy {
        val context = CZURAtyManager.getContext().applicationContext
        context.getSystemService(Context.CONNECTIVITY_SERVICE) as ConnectivityManager
    }


    private fun startRTMLink() {

        if (isNetworkConnected() && !isSuccessLogin) {
            isSuccessLogin = true
            launch {
                val token = requestToken()
                if (token != "null") {
                    // 初始化 RTM 实例
                    RTMUtil.initRTMClient()
                    //登陆RTM服务器
                    RTMUtil.loginRTM(token)
                } else {
                    isSuccessLogin = false
                    delay(5000)
                    startRTMLink()
                }
            }
        }

    }

    private val onDeviceConnectedListener = object : USBMonitor.OnDeviceConnectListener {
        override fun onAttach(device: UsbDevice?) {
            logTagD(TAG, "onAttach==${device?.productName}")
            //固定连接CZUR_TV3348
            if (device?.productName!! == ("CZUR_TV3348")) {
                mUsbMonitor.doConnect(device)
            }
        }

        override fun onDettach(device: UsbDevice?) {
            logTagD(TAG, "onDettach")
        }

        override fun onConnect(
            device: UsbDevice?,
            ctrlBlock: USBMonitor.UsbControlBlock?,
            createNew: Boolean
        ) {
            logTagD(TAG, "onConnect")
            try {
                if (uvcCamera != null) {
                    uvcCamera!!.destroy()
                }
                uvcCamera = UVCCamera()
                uvcCamera!!.open(ctrlBlock)
                logTagD(TAG, "===uvcCamera!!.open()=$isCameraRunningUpdate=")
                if (isCameraRunningUpdate.get()) {
                    launch {
                        val result = updateCamera()
                        if (result == -1) {
                            //这里失败则直接返回activity提示。成功则固定进度时间执行完成。
                            cameraUpgradeSuccess = false
                        }
                        isCameraRunningUpdate.set(false)
                    }
                } else {
                    //该情况为获取版本号
                    getCameraVersion()
                    releaseCamera()
                }
            } catch (e: Exception) {
                e.printStackTrace()
                releaseCamera()
                isCameraRunningUpdate.set(false)
                logTagE(TAG, "===uvcCamera.open failed==$e")
            }
        }

        override fun onDisconnect(device: UsbDevice?, ctrlBlock: USBMonitor.UsbControlBlock?) {
            logTagD(TAG, "onDisconnect")
            releaseCamera()
        }

        override fun onCancel(device: UsbDevice?) {
            TODO("Not yet implemented")
        }
    }

}