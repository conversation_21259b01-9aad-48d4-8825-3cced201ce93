<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/appListRv"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:clipToPadding="false"
        android:overScrollMode="never"
        android:paddingTop="22px" />

    <View
        android:id="@+id/allAppLine"
        android:layout_width="3px"
        android:layout_height="match_parent"
        android:background="#4DFFFFFF"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent" />

    <androidx.constraintlayout.widget.Group
        android:id="@+id/allAppGroup"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:constraint_referenced_ids="appListRv,allAppLine" />

    <ProgressBar
        android:id="@+id/progressBar"
        style="@style/Widget.AppCompat.ProgressBar"
        android:layout_width="100px"
        android:layout_height="100px"
        android:layout_gravity="center"
        android:indeterminateTint="@color/bg_main_blue"
        android:indeterminateTintMode="src_atop"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <ImageView
        android:id="@+id/localEmptyIv"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:src="@drawable/img_local_empty"
        app:layout_constraintBottom_toTopOf="@id/localEmptyTv"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintVertical_chainStyle="packed" />

    <TextView
        android:id="@+id/localEmptyTv"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="30px"
        android:layout_marginBottom="170px"
        android:alpha="0.7"
        android:text="@string/str_local_empty"
        android:textColor="@color/text_common"
        android:textSize="30px"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@id/localEmptyIv" />

    <androidx.constraintlayout.widget.Group
        android:id="@+id/emptyGroup"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:visibility="gone"
        app:constraint_referenced_ids="localEmptyIv,localEmptyTv" />

    <View
        android:id="@+id/loadErrorBg"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/bg_main" />

    <ImageView
        android:id="@+id/loadErrorIv"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:src="@drawable/ic_load_fail"
        app:layout_constraintBottom_toTopOf="@id/loadErrorTv"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintVertical_chainStyle="packed" />

    <TextView
        android:id="@+id/loadErrorTv"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="30px"
        android:layout_marginBottom="170px"
        android:alpha="0.7"
        android:text="@string/str_load_net_error"
        android:textColor="@color/text_common"
        android:textSize="30px"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@id/loadErrorIv" />


    <com.czur.uilib.btn.CZButton
        android:id="@+id/tryAgainBtn"
        android:layout_width="300px"
        android:layout_height="80px"
        android:layout_marginBottom="60px"
        android:text="@string/btn_load_error_try_again"
        android:textSize="30px"
        style="@style/ActionBtn.Dark"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent" />

    <androidx.constraintlayout.widget.Group
        android:id="@+id/loadErrorGroup"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:constraint_referenced_ids="loadErrorBg,loadErrorIv,loadErrorTv,tryAgainBtn" />
</androidx.constraintlayout.widget.ConstraintLayout>