package com.czur.starry.device.appstore

import android.content.Intent
import android.os.Bundle
import androidx.activity.viewModels
import androidx.fragment.app.commit
import com.czur.czurutils.log.logTagD
import com.czur.czurutils.log.logTagI
import com.czur.starry.device.appstore.databinding.ActivityMainBinding
import com.czur.starry.device.appstore.download.DownloadService
import com.czur.starry.device.appstore.menu.MainMenuAdapter
import com.czur.starry.device.appstore.menu.MenuViewModel
import com.czur.starry.device.appstore.ui.NetAppFragment
import com.czur.starry.device.appstore.ui.NetAppFragment.Companion.NET_TAG_ALL
import com.czur.starry.device.appstore.ui.NetAppFragment.Companion.instance
import com.czur.starry.device.appstore.ui.UninstallFragment
import com.czur.starry.device.appstore.ui.dialog.FeedbackAppDialog
import com.czur.starry.device.appstore.ui.vm.LocalAppVM
import com.czur.starry.device.baselib.base.v2.aty.CZViewBindingAty
import com.czur.starry.device.baselib.notice.NoticeHandler
import com.czur.starry.device.baselib.utils.basic.otherwise
import com.czur.starry.device.baselib.utils.basic.yes
import com.czur.starry.device.baselib.utils.closeDefChangeAnimations
import com.czur.starry.device.baselib.utils.doOnItemClick
import com.czur.starry.device.baselib.utils.launch
import com.czur.starry.device.baselib.utils.repeatCollectOnResume
import com.czur.starry.device.baselib.utils.toast
import com.czur.starry.device.baselib.utils.toastFail
import com.czur.starry.device.baselib.view.dialog.LoadingDialog
import kotlinx.coroutines.delay

/**
 * Created by 陈丰尧 on 2021/10/20
 */
class MainActivity : CZViewBindingAty<ActivityMainBinding>() {
    companion object {
        private const val TAG = "MainActivity"
    }

    private val localAppVM: LocalAppVM by viewModels()
    private val menuViewModel: MenuViewModel by viewModels()

    private val loadingDialog by lazy { LoadingDialog() }

    private val uninstallFragment = UninstallFragment()

    private val menuAdapter = MainMenuAdapter()

    override fun ActivityMainBinding.initBindingViews() {
        val allFragment = NetAppFragment.instance()

        supportFragmentManager.commit {
            add(R.id.mainTabContainer, allFragment, NET_TAG_ALL)
            add(R.id.mainTabContainer, uninstallFragment, UninstallFragment.NET_TAG_UNINSTALL)
            show(allFragment)
            hide(uninstallFragment)
        }

        menuRv.apply {
            closeDefChangeAnimations()
            adapter = menuAdapter

            doOnItemClick { vh, view ->
                val pos = vh.bindingAdapterPosition
                val tag = menuAdapter.getData(pos).appTag
                menuViewModel.selectTag(tag)
                true
            }
        }

        feedbackLayer.setOnClickListener {
            FeedbackAppDialog { dialog, appName ->
                dialog.dismiss()
                if (appName.isNotEmpty()) {
                    launch {
                        loadingDialog.show()
                        localAppVM.addFeedBackAppName(appName)
                            .yes {
                                toast(R.string.toast_submit_success)
                            }.otherwise {
                                toastFail()
                            }
                        loadingDialog.dismiss()
                    }
                }
            }.show()
        }
    }

    override fun initData(savedInstanceState: Bundle?) {
        super.initData(savedInstanceState)
        val intent = Intent(this, DownloadService::class.java)
        startService(intent)

        repeatCollectOnResume(menuViewModel.showMenuFlow) {
            menuAdapter.setData(it)
        }

        repeatCollectOnResume(menuViewModel.selectTagFlow) {
            val tag = it.tagCode
            logTagI(TAG, "selectTagFlow tag = $tag")
            var newAddedFragment = false
            val fragment = supportFragmentManager.findFragmentByTag(tag) ?: instance(tag).also {
                supportFragmentManager.commit {
                    newAddedFragment = true
                    add(R.id.mainTabContainer, it, tag)
                }
            }

            supportFragmentManager.commit {
                supportFragmentManager.fragments.forEach { frag ->
                    hide(frag)
                }
                show(fragment)
                if (!newAddedFragment) {
                    // 重新加载数据
                    (fragment as? NetAppFragment)?.reLoadData()
                }
            }
        }

        localAppVM.initData()

        launch {
            localAppVM.currentMenuPageFlow.collect {
                logTagI(TAG, "currentMenuPageFlow $it")
                while (menuAdapter.itemCount == 0) {
                    delay(100)
                }
                val tag = if (it == getString(R.string.voice_command_uninstall)) {
                    menuViewModel.uninstallAppTag
                }else {
                    menuViewModel.allAppTag
                }
                if (menuViewModel.selectTagFlow.value != tag) {
                    logTagD(TAG, "currentMenuPageFlow 切换tag=${menuViewModel.selectTagFlow.value}")
                    menuViewModel.selectTag(tag)
                }
            }
        }

        handleIntent(getIntent())
    }

    override fun onNewIntent(intent: Intent) {
        super.onNewIntent(intent)
        handleIntent(intent)
    }

    private fun handleIntent(intent: Intent) {
        localAppVM.handleIntent(intent)
    }

    override fun onDestroy() {
        NoticeHandler.clearAll()
        super.onDestroy()
    }
}