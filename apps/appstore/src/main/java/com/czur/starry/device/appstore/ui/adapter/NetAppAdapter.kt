package com.czur.starry.device.appstore.ui.adapter

import android.text.format.Formatter
import android.view.ViewGroup
import com.czur.starry.device.appstore.ProgressTextView
import com.czur.starry.device.appstore.R
import com.czur.starry.device.appstore.entity.AllAppVO
import com.czur.starry.device.baselib.base.BaseDifferAdapter
import com.czur.starry.device.baselib.base.BaseVH

/**
 * Created by 陈丰尧 on 2021/10/26
 */
class NetAppAdapter : BaseDifferAdapter<AllAppVO>() {
    override fun bindViewHolder(holder: BaseVH, position: Int, itemData: AllAppVO) {
        holder.setImgUrl(
            itemData.iconUrl,
            R.drawable.ic_def_icon,
            R.drawable.ic_def_icon,
            R.id.itemNetAppIconIv
        )
        holder.setText(itemData.name, R.id.itemNetAppNameTv)
        holder.setText(itemData.versionName, R.id.itemNetAppVersionTv)
        val size = Formatter.formatShortFileSize(holder.context, itemData.size)
        holder.setText(size, R.id.itemNetAppSizeTv)
        // 安装按钮
        holder.visible(itemData.canShowInstallBtn, R.id.itemInstallBtn)
        // 取消下载按钮
        holder.visible(itemData.downloading, R.id.itemCancelDownBtn)
        // 正在安装
        holder.visible(itemData.canShowInstallProgress, R.id.itemInstallProgress)
        // 打开按钮
        holder.visible(itemData.canShowOpenBtn, R.id.itemOpenBtn)
        // 更新按钮显示规则
        holder.visible(itemData.canShowUpdateBtn, R.id.itemUpdateBtn)

        // 进度
        val progressTextView:ProgressTextView = holder.getView(R.id.itemCancelDownBtn)
        progressTextView.progress = itemData.downloadProgress
    }

    override fun areItemsTheSame(oldItem: AllAppVO, newItem: AllAppVO): Boolean {
        return oldItem.id == newItem.id
    }

    override fun areContentsTheSame(oldItem: AllAppVO, newItem: AllAppVO): Boolean {
        return oldItem == newItem
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): BaseVH {
        return BaseVH(R.layout.item_net_app, parent)
    }

    fun getItemPosition(name: String): Int {
        // 先尝试精确匹配
        val exactMatchPos =
            differ.currentList.indexOfFirst { it.name.equals(name, ignoreCase = true) }
        if (exactMatchPos != -1) {
            return exactMatchPos
        }
        // 若精确匹配未找到，尝试包含匹配
        if (name.length >= 2) {
            return differ.currentList.indexOfFirst { it.name.contains(name, ignoreCase = true) }
        }
        return -1
    }
}