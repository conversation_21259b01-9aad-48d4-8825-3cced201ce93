package com.czur.starry.device.settings.ui.system.time

import android.app.Application
import androidx.lifecycle.AndroidViewModel
import com.czur.czurutils.log.logTagD
import com.czur.starry.device.baselib.utils.appContext
import com.czur.starry.device.baselib.utils.launch
import com.czur.starry.device.settings.manager.LanguageManager
import com.czur.starry.device.settings.manager.TimeManager
import com.czur.starry.device.settings.model.TimeZoneEntity
import com.czur.starry.device.settings.utils.datetime.getSortedTimeZoneList
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.NonCancellable.isActive
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.collectLatest
import kotlinx.coroutines.flow.combine
import kotlinx.coroutines.flow.debounce
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.isActive
import kotlinx.coroutines.withContext

/**
 * Created by 陈丰尧 on 2023/6/20
 */
private const val TAG = "TimeZoneViewModel"

class TimeZoneViewModel(application: Application) : AndroidViewModel(application) {
    private val timeManager: TimeManager by lazy { TimeManager() }
    private var timeZoneEntityList: List<TimeZoneEntity> = emptyList()
    private var timeZoneIndexSet = emptySet<Char>()

    private var searchWordFlow = MutableStateFlow("")
    val timeZoneDataFlow =
        MutableStateFlow<Pair<Set<Char>, List<TimeZoneItem>>>(timeZoneIndexSet to emptyList())

    // 是否显示左侧索引条
    val showIndexBar = MutableStateFlow(false)

    private var currentTimeZoneId: String = timeManager.getCurrentTimeZoneId()
    var selTimeZoneIdFlow = MutableStateFlow(currentTimeZoneId)
    var applyBtnEnableFlow = selTimeZoneIdFlow.map { it != currentTimeZoneId }

    // 搜索模式
    var searchModeFlow = MutableStateFlow(false)
    private val needHideLetterIndex by lazy {
        // 只用中文或英文显示字母索引条
        LanguageManager.getCheckedLocale() == LanguageManager.localeList[0] ||
                LanguageManager.getCheckedLocale() == LanguageManager.localeList[2]
    }

    // 是否显示搜索结果为空
    val showSearchEmptyFlow = searchModeFlow.combine(timeZoneDataFlow) { searchMode, data ->
        searchMode && data.second.isEmpty()
    }

    private var firstInitData = true


    init {
        launch {
            searchWordFlow
                .debounce {
                    if (firstInitData) { // 第一次初始化时不延迟
                        firstInitData = false
                        0L
                    } else {
                        500L
                    }
                }
                .collectLatest {
                    makeTimeZoneData()
                }
        }
    }

    fun changeSearchMode(searchMode: Boolean) {
        searchModeFlow.value = searchMode
    }

    fun changeSelectId(id: String) {
        selTimeZoneIdFlow.value = id
    }

    /**
     * 切换左侧索引条显示状态
     */
    fun changeIndexBarState(show: Boolean? = null) {
        val target = show ?: showIndexBar.value.not()
        showIndexBar.value = target && needHideLetterIndex
    }

    fun updateSearchWord(word: String) {
        searchWordFlow.value = word
    }

    suspend fun getIndexByChar(char: Char): Int = withContext(Dispatchers.Default) {
        timeZoneDataFlow.value.second.indexOfFirst { it.indexStr == char.toString() }
    }

    private suspend fun makeTimeZoneData() = withContext(Dispatchers.Default) {
        if (timeZoneEntityList.isEmpty()) {
            timeZoneEntityList = getSortedTimeZoneList(appContext)
        }
        if (!isActive) return@withContext
        val filterList = if (searchWordFlow.value.isNotEmpty()) {
            timeZoneEntityList.filter {
                it.name.contains(searchWordFlow.value, true)
                        || it.pinyin.contains(searchWordFlow.value, true)
            }
        } else {
            timeZoneEntityList
        }
        if (!isActive) return@withContext
        var lastIndex = ""
        val showIndexSet = mutableSetOf<Char>()
        val showItemList = filterList.map {
            if (!isActive) return@withContext
            val currentIndex = it.pinyin.firstOrNull()?.toString() ?: ""
            val index = if (needHideLetterIndex && currentIndex != lastIndex) {
                showIndexSet.add(currentIndex[0])
                lastIndex = currentIndex
                currentIndex
            } else {
                ""
            }
            TimeZoneItem(
                id = it.id,
                name = it.name,
                pinyin = it.pinyin,
                gmt = it.gmt,
                offset = it.offset,
                indexStr = index
            )
        }
        timeZoneDataFlow.value = showIndexSet to showItemList
    }


    fun loadTimeZoneData() {
        launch {
            makeTimeZoneData()
        }
    }

    /**
     * 应用当前选中的时区
     */
    fun applyTimeZone() {
        logTagD(TAG, "设置时区:${selTimeZoneIdFlow.value}")
        timeManager.setTimeZone(selTimeZoneIdFlow.value)
        currentTimeZoneId = selTimeZoneIdFlow.value
        // 更新Flow
        selTimeZoneIdFlow.value = ""
        selTimeZoneIdFlow.value = currentTimeZoneId
    }
}