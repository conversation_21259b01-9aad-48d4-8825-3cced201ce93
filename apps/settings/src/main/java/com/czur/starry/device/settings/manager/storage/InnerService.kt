package com.czur.starry.device.settings.manager.storage

import com.czur.starry.device.baselib.network.core.MiaoHttpEntity
import com.czur.starry.device.baselib.network.core.MiaoHttpGet
import com.czur.starry.device.baselib.network.core.MiaoHttpParam

/**
 * created by wangh 22.0610
 */


interface InnerService {

    /**
     * 验证卸载残留表版本
     */
    @MiaoHttpGet("/api/ota/cleanup/check")
    fun checkUninstallInfo(
        @MiaoHttpParam("version") version: String?,
        clazz: Class<FeatureInfo>
    ): MiaoHttpEntity<FeatureInfo>

}
