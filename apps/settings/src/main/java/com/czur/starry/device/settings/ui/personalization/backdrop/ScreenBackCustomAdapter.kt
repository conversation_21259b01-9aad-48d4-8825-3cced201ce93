package com.czur.starry.device.settings.ui.personalization.backdrop

import android.content.Context
import android.graphics.drawable.Drawable
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ImageView
import android.widget.LinearLayout
import android.widget.ProgressBar
import android.widget.TextView
import androidx.constraintlayout.utils.widget.ImageFilterView
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.recyclerview.widget.AsyncListDiffer
import androidx.recyclerview.widget.DiffUtil
import androidx.recyclerview.widget.RecyclerView
import com.bumptech.glide.Glide
import com.bumptech.glide.load.DataSource
import com.bumptech.glide.load.engine.GlideException
import com.bumptech.glide.request.RequestListener
import com.bumptech.glide.request.target.Target
import com.bumptech.glide.signature.ObjectKey
import com.czur.starry.device.baselib.utils.gone
import com.czur.starry.device.baselib.utils.show
import com.czur.starry.device.settings.R
import com.czur.starry.device.settings.model.BackdropListItem


private const val TAG = "ScreenBackCustomAdapter"

class ScreenBackCustomAdapter : RecyclerView.Adapter<RecyclerView.ViewHolder>() {

    private var mContext: Context? = null
    private var mData: MutableList<BackdropListItem>? = null


    private val diffCallback = object : DiffUtil.ItemCallback<BackdropListItem>() {
        override fun areItemsTheSame(oldItem: BackdropListItem, newItem: BackdropListItem): Boolean {
            return return (oldItem.customEntity?.absPath == newItem.customEntity?.absPath
                    && oldItem.customEntity?.lastModifyTime == newItem.customEntity?.lastModifyTime)
        }

        override fun areContentsTheSame(oldItem: BackdropListItem, newItem: BackdropListItem): Boolean {
            return (oldItem == newItem && oldItem.customEntity?.lastModifyTime == oldItem.customEntity?.lastModifyTime)
        }
    }
    private val mDiffer: AsyncListDiffer<BackdropListItem> = AsyncListDiffer(this, diffCallback)


    fun getData(position: Int): BackdropListItem {
        return mDiffer.currentList[position]
    }


    fun setData(context: Context, data: MutableList<BackdropListItem>) {
        if (mContext == null) {
            mContext = context
        }
        mData = data
        mDiffer.submitList(mData)
    }

    fun refreshData(data: MutableList<BackdropListItem>) {
        mData = data
        mDiffer.submitList(mData)
    }


    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): RecyclerView.ViewHolder {
        val itemView: View =
            LayoutInflater.from(mContext)
                .inflate(R.layout.item_wallpaper_custom, parent, false)
        return NormalHolder(itemView)
    }

    override fun onBindViewHolder(holder: RecyclerView.ViewHolder, position: Int) {
        val normalHolder = holder as NormalHolder

        if (position > 0) {
            // 重置状态，显示正常项的视图
            normalHolder.progressBar.show()
            normalHolder.selectLayout.gone()
            normalHolder.mImage.show()
            normalHolder.imShader.show()

            val data = mDiffer.currentList[position]
            Glide.with(holder.itemView.context)
                .load(data.customEntity?.absPath)
                .signature(ObjectKey(System.currentTimeMillis()))
                .listener(object : RequestListener<Drawable> {
                    override fun onLoadFailed(
                        e: GlideException?,
                        model: Any?,
                        target: Target<Drawable>,
                        isFirstResource: Boolean
                    ): Boolean {
                        return false
                    }

                    override fun onResourceReady(
                        resource: Drawable,
                        model: Any,
                        target: Target<Drawable>?,
                        dataSource: DataSource,
                        isFirstResource: Boolean
                    ): Boolean {
                        normalHolder.progressBar.gone()
                        return false
                    }
                }).into(normalHolder.mImage)

        } else if (position == 0) {
            normalHolder.progressBar.gone()
            normalHolder.selectLayout.show()
            normalHolder.mImage.gone()
            normalHolder.imShader.gone()
        }

        normalHolder.mtvName.gone()
    }

    override fun getItemCount(): Int {
        return mDiffer.currentList.size
    }

    class NormalHolder(itemView: View) : RecyclerView.ViewHolder(itemView) {
        var mImage: ImageFilterView
        var mtvName: TextView
        var selectLayout: ConstraintLayout
        var layoutLocal: LinearLayout
        var layoutQrcode: LinearLayout
        var progressBar: ProgressBar
        var imShader: ImageView

        init {
            mtvName = itemView.findViewById(R.id.tvName) as TextView
            mImage = itemView.findViewById(R.id.imCustom) as ImageFilterView
            selectLayout = itemView.findViewById(R.id.selectId) as ConstraintLayout
            layoutLocal = itemView.findViewById(R.id.layoutLocal) as LinearLayout
            layoutQrcode = itemView.findViewById(R.id.layoutQrcode) as LinearLayout
            progressBar = itemView.findViewById(R.id.progressBar) as ProgressBar
            imShader = itemView.findViewById(R.id.imShader) as ImageView
        }
    }
}