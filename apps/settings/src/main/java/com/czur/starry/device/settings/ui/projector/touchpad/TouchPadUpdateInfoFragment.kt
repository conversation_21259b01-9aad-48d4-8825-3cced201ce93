package com.czur.starry.device.settings.ui.projector.touchpad

import android.os.Bundle
import androidx.fragment.app.viewModels
import com.czur.starry.device.baselib.utils.launch
import com.czur.starry.device.settings.base.BaseBindingMenuFragment
import com.czur.starry.device.settings.databinding.FragmentTouchPadUpdateInfoBinding
import io.noties.markwon.Markwon

class TouchPadUpdateInfoFragment : BaseBindingMenuFragment<FragmentTouchPadUpdateInfoBinding>() {
    private val touchVM: TouchControlVM by viewModels({ requireParentFragment() })

    private val markwon by lazy {
        Markwon.builder(requireContext()).build()
    }

    override fun FragmentTouchPadUpdateInfoBinding.initBindingViews() {
        backBtn.setOnClickListener {
            parentFragmentManager.popBackStack()
        }
    }

    override fun initData(savedInstanceState: Bundle?) {
        launch {
            val info = touchVM.updateInfo.await()
            markwon.setMarkdown(binding.text, info)
        }
    }
}