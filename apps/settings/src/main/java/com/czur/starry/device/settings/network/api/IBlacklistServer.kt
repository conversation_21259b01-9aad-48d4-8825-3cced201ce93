package com.czur.starry.device.settings.network.api

import com.czur.starry.device.baselib.network.core.MiaoHttpEntity
import com.czur.starry.device.baselib.network.core.MiaoHttpParam
import com.czur.starry.device.baselib.network.core.MiaoHttpPost

/**
 * Created by 陈丰尧 on 2022/10/26
 */
interface IBlacklistServer {
    companion object {
        const val INSIDE_OPEN = 1
        const val INSIDE_CLOSE = 0
    }
    /**
     * 更新仅接听通讯录及企业联系人选项配置
     */
    @MiaoHttpPost("/api/starry/insideAnswer")
    fun insideAnswer(
        @MiaoHttpParam("open")
        open: Int,
        clazz: Class<String> = String::class.java
    ): MiaoHttpEntity<String>
}