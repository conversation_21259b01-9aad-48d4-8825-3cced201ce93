package com.czur.starry.device.settings.widget.speed

import android.graphics.Path
import android.graphics.PathMeasure
import android.graphics.PointF
import com.google.common.collect.EvictingQueue

/**
 * Created by 陈丰尧 on 2021/9/1
 */
class HintHistory(private val offSetPos: Float) {

    private val historyLocation = EvictingQueue.create<PointF>(2)
    private val path = Path()
    private var pathMeasure = PathMeasure()
    private val pos = FloatArray(2)
    private val lastShowPoint = PointF(0F, 0F)


    // 提示历史记录的文字
    var hintText: String = ""

    private var hasMakePath = false


    fun updateHistory(hintText: String, locationX: Float, locateY: Float) {
        this.hintText = hintText
        historyLocation.add(PointF(locationX, locateY))
    }

    fun reset() {
        historyLocation.clear()
        hintText = ""
        path.reset()
        hasMakePath = false
        lastShowPoint.set(0F, 0F)
    }

    fun makePath() {
        path.reset()
        when (historyLocation.size) {
            0 -> {
                path.moveTo(0F, 0F)
                path.lineTo(offSetPos, 0F)
            }
            1 -> {
                val point = historyLocation.element()
                path.moveTo(point.x, point.y)
                path.lineTo(point.x + offSetPos, point.y)
            }
            else -> {
                var point1 = PointF()
                var point2 = PointF()
                historyLocation.forEachIndexed { index: Int, point ->
                    when (index) {
                        0 -> point1 = point
                        1 -> point2 = point
                        else -> return@forEachIndexed
                    }
                }

                path.moveTo(point1.x, point1.y)

                val x = point2.x + offSetPos
                val y = (x - point1.x) / (point2.x - point1.x) * (point2.y - point1.y) + point1.y
                path.lineTo(x, y)
            }
        }
        pathMeasure.setPath(path, false)
        hasMakePath = true
    }

    /**
     * 根据百分比,求当前位置
     */
    fun getLocation(percent: Float): PointF? {
        val realLocation = getRealLocation(percent) ?: return null
        val location = if (realLocation.x >= lastShowPoint.x) realLocation else null
        if (location == null && lastShowPoint.equals(0F, 0F)) {
            return null
        }
        location?.let {
            lastShowPoint.set(it)
        }
        return location ?: lastShowPoint
    }

    private fun getRealLocation(percent: Float): PointF? {
        return if (hasMakePath && percent > 0) {
            pathMeasure.getPosTan(pathMeasure.length * percent, pos, null)
            PointF(pos[0], pos[1])
        } else {
            historyLocation.lastOrNull()
        }
    }
}