package com.czur.starry.device.settings.touchpad

import android.bluetooth.BluetoothDevice

/**
 * Created by 陈丰尧 on 2021/11/17
 */
class TouchPad(val macAddress: String) {
    var broadcast: String = ""

    /**
     * 广播的前3个字节
     */
    val broadcastHead: String
        get() = if (broadcast.length < 6) "" else broadcast.substring(0, 6)
    var rssi: Int = Int.MIN_VALUE
    var boundStatus: Int = 0

    var device: BluetoothDevice? = null
}