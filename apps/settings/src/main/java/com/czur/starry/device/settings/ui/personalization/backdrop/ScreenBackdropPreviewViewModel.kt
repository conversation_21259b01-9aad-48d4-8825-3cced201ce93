package com.czur.starry.device.settings.ui.personalization.backdrop

import android.app.Application
import android.content.res.Configuration
import androidx.lifecycle.AndroidViewModel
import com.czur.starry.device.settings.model.BackdropListItem
import com.czur.starry.device.settings.model.BackdropTag
import com.czur.starry.device.settings.model.BackdropType
import kotlinx.coroutines.GlobalScope
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.combine
import kotlinx.coroutines.flow.filterNotNull
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.launch
import kotlin.math.max
import kotlin.math.min

/**
 * Created by 陈丰尧 on 2024/7/24
 */
private const val TAG = "ScreenBackdropPreviewViewModel"

class ScreenBackdropPreviewViewModel(private val application: Application) : AndroidViewModel(application) {

    private val showBackdropListFlow = MutableStateFlow<List<BackdropListItem>>(emptyList())
    private val showIndexFlow = MutableStateFlow(0)

    private val _currentBackdropFlow = combine(showBackdropListFlow, showIndexFlow) { list, index ->
        list.getOrNull(index)
    }
    // 当前显示的背景图
    val currentBackdropFlow = _currentBackdropFlow.filterNotNull()

    // 当前选中的单元
    val currentBackdropEntity:BackdropListItem
        get() = showBackdropListFlow.value[showIndexFlow.value]

    val preEnableFlow = showIndexFlow.map {
        it > 0
    }

    val nextEnableFlow = combine(showBackdropListFlow, showIndexFlow) { list, index ->
        if (showBackdropListFlow.value.isEmpty()) {
            false
        } else {
            index < list.size - 1
        }
    }

    // 是否使用当前背景
    val isUseCurrentBackdropFlow = MutableStateFlow<Boolean>(false)

    private var hasInit = false
    var config = Configuration()


    fun handelParams(filterTagKey: String, currentName: String, backdropType: BackdropType) {
        if (hasInit) {
            return
        }

        val tag = BackdropTag.getBackdropTag(filterTagKey)

        GlobalScope.launch {
            val backdropList = getAllData()
            val showBackdropList = when(tag) {
                BackdropTag.All -> backdropList
                BackdropTag.Custom -> backdropList.filter{ it.fileType == BackdropType.CUSTOM }
                else -> backdropList.filter{ it.fileType == BackdropType.FIXED &&  it.fixedEntity?.tag == tag}
            }

            val showIndex = if (backdropType == BackdropType.FIXED) {
                max(0, showBackdropList.indexOfFirst { it.fixedEntity?.name == currentName })
            } else {
                max(0, showBackdropList.indexOfFirst { it.customEntity?.name == currentName })
            }

            showBackdropListFlow.value = showBackdropList
            showIndexFlow.value = showIndex
        }

        hasInit = true
    }

    /**
     * 上一页
     */
    fun previousPage() {
        showIndexFlow.value = max(0, showIndexFlow.value - 1)
    }

    /**
     * 下一页
     */
    fun nextPage() {
        showIndexFlow.value = min(showBackdropListFlow.value.size - 1, showIndexFlow.value + 1)
    }
}