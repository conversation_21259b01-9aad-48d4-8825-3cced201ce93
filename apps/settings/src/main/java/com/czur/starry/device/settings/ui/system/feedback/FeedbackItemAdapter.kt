package com.czur.starry.device.settings.ui.system.feedback

import android.view.ViewGroup
import androidx.constraintlayout.widget.ConstraintLayout
import com.czur.starry.device.baselib.base.BaseDifferAdapter
import com.czur.starry.device.baselib.base.BaseVH
import com.czur.starry.device.settings.R
import com.czur.starry.device.settings.model.FeedbackItem
import com.czur.uilib.choose.CZCheckBox

/**
 * Created by 陈丰尧 on 2023/8/14
 */
data class FeedbackItemShowEntity(
    val category: FeedbackItem,
    val select: Boolean,
    var refreshTime : Long = 0
)

class FeedbackItemAdapter : BaseDifferAdapter<FeedbackItemShowEntity>() {
    private var onItemSelectChange: (FeedbackItem, Boolean) -> Unit = { _, _ -> }

    fun setOnItemSelectChange(onItemSelectChange: (FeedbackItem, Boolean) -> Unit) {
        this.onItemSelectChange = onItemSelectChange
    }

    override fun areItemsTheSame(
        oldItem: FeedbackItemShowEntity,
        newItem: FeedbackItemShowEntity
    ): Boolean {
        return oldItem.category.categoryKey == newItem.category.categoryKey
                && oldItem.refreshTime == newItem.refreshTime
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): BaseVH {
        return BaseVH(R.layout.item_feedback_item, parent).apply {

            val czCheckBox = getView<CZCheckBox>(R.id.feedbackItemCb)
            val czCheckBg = getView<ConstraintLayout>(R.id.bgClickView)
            czCheckBox.setOnCheckedChangeListener { isOn, fromUser ->
                if (fromUser) {
                    onItemSelectChange.invoke((tag as FeedbackItemShowEntity).category, isOn)
                }
            }
            czCheckBg.setOnClickListener {
                czCheckBox.performClick()
            }
        }
    }

    override fun bindViewHolder(holder: BaseVH, position: Int, itemData: FeedbackItemShowEntity) {
        holder.setText(itemData.category.name, R.id.feedbackItemNameTv)
        holder.setTips(itemData.category.name, R.id.feedbackItemNameTv)
        holder.tag = itemData
        val czCheckBox = holder.getView<CZCheckBox>(R.id.feedbackItemCb)
        czCheckBox.setChecked(itemData.select)
    }
}