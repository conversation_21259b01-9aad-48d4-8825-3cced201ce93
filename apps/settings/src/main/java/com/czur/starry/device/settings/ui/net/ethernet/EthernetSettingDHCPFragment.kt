package com.czur.starry.device.settings.ui.net.ethernet

import android.os.Bundle
import androidx.fragment.app.viewModels
import com.czur.starry.device.baselib.base.v2.fragment.CZViewBindingFragment
import com.czur.starry.device.baselib.utils.repeatCollectOnResume
import com.czur.starry.device.settings.R
import com.czur.starry.device.settings.databinding.FragmentEthernetSettingDhcpBinding

/**
 * Created by 陈丰尧 on 2023/8/8
 */
class EthernetSettingDHCPFragment : CZViewBindingFragment<FragmentEthernetSettingDhcpBinding>() {
    private val ethernetViewModel: EthernetViewModel by viewModels({ requireParentFragment() })

    override fun FragmentEthernetSettingDhcpBinding.initBindingViews() {
        listOf(ipValueTv, subnetValueTv, gatewayValueTv, dnsValueTv).forEach {
            it.setEnable(false)
            it.setFocus(false)
            it.background = null
            it.setTextColor(resources.getColor(R.color.text_common))
        }
    }

    override fun initData(savedInstanceState: Bundle?) {
        super.initData(savedInstanceState)

        repeatCollectOnResume(ethernetViewModel.ipFlow) {
            binding.ipValueTv.setText(it)
            ethernetViewModel.customIP = it
        }
        repeatCollectOnResume(ethernetViewModel.subnetMaskFlow) {
            binding.subnetValueTv.setText(it)
            ethernetViewModel.customSubnetMask = it
        }
        repeatCollectOnResume(ethernetViewModel.gatewayFlow) {
            binding.gatewayValueTv.setText(it)
            ethernetViewModel.customGateway = it
        }
        repeatCollectOnResume(ethernetViewModel.dnsFlow) {
            binding.dnsValueTv.setText(it)
            ethernetViewModel.customDNS = it
        }
    }
}