package com.czur.starry.device.settings.touchpad

import android.app.Service
import android.content.Intent
import android.os.IBinder
import android.os.RemoteCallbackList
import android.provider.Settings
import com.czur.czurutils.log.logTagD
import com.czur.czurutils.log.logTagI
import com.czur.czurutils.log.logTagV
import com.czur.czurutils.log.logTagW
import com.czur.starry.device.baselib.common.Constants
import com.czur.starry.device.baselib.common.KEY_TOUCH_BOARD_VERSION
import com.czur.starry.device.baselib.common.VersionIndustry
import com.czur.starry.device.baselib.common.hw.StarryModel
import com.czur.starry.device.baselib.network.HttpManager
import com.czur.starry.device.baselib.utils.ONE_SECOND
import com.czur.starry.device.baselib.utils.doWithoutCatch
import com.czur.starry.device.baselib.utils.prop.setSystemProp
import com.czur.starry.device.otalib.OTAHandler
import com.czur.starry.device.settings.ITouchPad
import com.czur.starry.device.settings.ITouchPadStatusCallback
import com.czur.starry.device.settings.model.TouchPadVersionCheckInfo
import com.czur.starry.device.settings.network.api.ITouchVersionServer
import com.czur.starry.device.settings.ui.projector.touchpad.TouchPadHardwareVersion
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.Job
import kotlinx.coroutines.async
import kotlinx.coroutines.cancel
import kotlinx.coroutines.delay
import kotlinx.coroutines.isActive
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext

/**
 * Created by 陈丰尧 on 2021/11/17
 * 连接方式:
 * 1. 只有放在充电位置 + 当前没有触控板连接中, 才会开启扫描
 * 2. 扫描成功后, 会忘记之前的触控板资源
 *
 * 该Service的作用
 * 1. 负责蓝牙触控板连接等逻辑的触发, 状态维护
 * 2. 与Client进行连接, 来分发对应的蓝牙状态
 */
class TouchPadService : Service() {
    companion object {
        private const val TAG = "TouchPad-Service"
        private const val SCAN_TIME = 30 * ONE_SECOND

        private const val BROADCAST_HEAD_5 = "020105"
        private const val USE_LINUX_CHECK = false
        private var lastConnectMac: String? = ""
        private const val KEY_TOUCH_PAD_MAC = "touch_pad_mac"
    }

    private var scope = CoroutineScope(Job())
    private val bluetoothUtil = TouchPadBluetoothUtil(scope)    // 用来连接设备的
    private val statusManager = StatusManager()                 // 用来更新状态的
    private val batteryManager = TouchPadInfoManager(this)      // 更新电量版本信息
    private var gattManager: TouchPadGattManager? = null        // 管理gatt服务
    private val statusBroadcastUtil = StatusBroadcastUtil(this) // 给系统发状态广播

    private var statusJob: Job? = null

    // 扫描超时job
    private var scanTimeOutJob: Job? = null

    private val lock = Any()

    private val dockUtil = PowerDockUtil(this::onPowerDockStatusChange)

    private val touchNetService: ITouchVersionServer by lazy { HttpManager.getService(BASE_URL = Constants.OTA_BASE_URL) }

    private var connectGattJob: Job? = null

    private var checkConnectStatusByLinuxJob: Job? = null

    // 自定义快捷键的接收器
    private val customShortcutKeyReceiver = CustomShortcutKeyReceiver()

    private var touchPadVersion = TouchPadHardwareVersion.UNKNOWN
        set(value) {
            field = value
            scope.launch {
                // 写到系统属性里面, 让其他应用读取
                setSystemProp(KEY_TOUCH_BOARD_VERSION, value.hwVersion)
            }
        }

    private val binder = object : ITouchPad.Stub() {
        private val cbList = RemoteCallbackList<ITouchPadStatusCallback>()

        override fun registerStatusCallback(cb: ITouchPadStatusCallback?) {
            cb?.let {
                addCallback(cb)
            }
        }

        override fun unregisterStatusCallback(cb: ITouchPadStatusCallback?) {
            cb?.let {
                cbList.unregister(it)
            }
        }

        override fun checkTouchPadStatus() {
            logTagV(TAG, "主动检查一次触控板状态")
            scope.launch {
                if (statusManager.status == TPDeviceStatus.STATUS_INIT) {
                    logTagD(TAG, "还在初始化, 跳过检测")
                    return@launch
                }
                if (bluetoothUtil.isScanning()) {
                    logTagD(TAG, "正在扫描, 跳过检测")
                    return@launch
                }

                val connectConnStatus = bluetoothUtil.hasConnected()
                val currentConnStatus = statusManager.isConnecting
                logTagI(TAG, "主动检查状态:${connectConnStatus}, 当前状态:${currentConnStatus}")
                // 只有主动检测的结果是 连接中, 但是当前的结果是未连接, 才会更新状态
                // 因为主动检测的结果不准, 经常会已连接 但是检测出来的是未连接
                if (connectConnStatus != currentConnStatus && connectConnStatus) {
                    logTagV(TAG, "更新连接状态")
                    statusManager.updateStatus(connect = true)
                }
            }
        }

        private fun addCallback(cb: ITouchPadStatusCallback) {
            cbList.register(cb)
            val connectStatus = statusManager.isConnecting
            val battery = batteryManager.batteryLevel
            val version = batteryManager.touchPadVersion
            updateConnectStatusToClient(connectStatus, battery, version)
        }

        /**
         * 更新连接状态给Client端
         */
        fun updateConnectStatusToClient(
            connect: Boolean? = null,
            battery: Int? = null,
            version: String? = null
        ) {
            if (cbList.registeredCallbackCount == 0) {
                return
            }

            if (connect == false) {
                statusBroadcastUtil.resetAlertStatus()
            }

            scope.launch {
                synchronized(lock) {
                    logTagV(TAG, "通知Client连接状态改变")
                    cbList.beginBroadcast()
                    for (i in 0 until cbList.registeredCallbackCount) {
                        val cb = cbList.getBroadcastItem(i)
                        connect?.let {
                            cb.onConnectChange(it)
                        }
                        battery?.let {
                            cb.onBatteryLevelChange(it)
                        }
                        version?.let {
                            cb.onVersionLevelChange(it)
                        }
                    }
                    cbList.finishBroadcast()
                }
            }

        }

    }


    override fun onCreate() {
        super.onCreate()
        logTagD(TAG, "TouchPadService启动")

        lastConnectMac = Settings.Global.getString(this.contentResolver, KEY_TOUCH_PAD_MAC) ?: ""
        bluetoothUtil.enableBlueTooth()
        startWatchStatus()

        val ignoreTouchPad = checkIgnoreTouchPad()

        if (ignoreTouchPad) {
            logTagW(TAG, "不需要启动TouchPadService")
            stopSelf()
            return
        }

        // 注册自定义快捷键的广播
        customShortcutKeyReceiver.register(this)
    }

    /**
     * 检查是否忽略触控板
     */
    private fun checkIgnoreTouchPad(): Boolean {
        if (Constants.versionIndustry == VersionIndustry.DEVICE_INDUSTRY_ARMY_BUILD) {
            logTagI(TAG, "军队版本不启动触控板服务")
            return true
        }
        return false
    }

    /**
     * 开始监控状态
     */
    private fun startWatchStatus() {
        // 充电位置 和连接位置组合状态监控
        scope.launch {
            statusManager.statusFlow.collect {
                logTagD(TAG, "当前状态:${it}")

                statusJob?.cancel()
                statusJob = launch {
                    doWithNewStatus(it)
                }
            }
        }

        scope.launch {
            /**
             * 连接状态改变监听
             */
            statusManager.connectFlow.collect {
                logTagD(TAG, "连接状态发生改变:${it}")
                // 断开连接通知系统, 让系统弹出断开连接的提示
                // 连接成功的状态更新
                statusBroadcastUtil.updateStatus(it)
                // 通知系统
                if (!it) {
                    touchPadVersion = TouchPadHardwareVersion.UNKNOWN
                }
                // 开始监控电量
                if (it) {
                    // 通知客户端
                    binder.updateConnectStatusToClient(connect = true)
                    bluetoothUtil.connectingDevices?.let { devices ->
                        connectGattJob?.cancel()
                        connectGattJob = launch {
                            // 这里需要延迟一下, 否则可能出现, 连接上gatt, 但是触控不好用的情况
                            delay(5 * ONE_SECOND)
                            gattManager = TouchPadGattManager(this, devices, batteryManager).apply {
                                connect(this@TouchPadService)
                            }
                        }
                    }
                    bluetoothUtil.forgetUnConnectTouchPad()
                } else {
                    connectGattJob?.cancel()
                    connectGattJob = null
                    // 通知客户端 断开连接
                    binder.updateConnectStatusToClient(
                        connect = false,
                        battery = TouchPadInfoManager.BATTERY_DID_NOT_GET,
                        version = ""
                    )
                    gattManager?.disconnect()
                    gattManager = null
                    // 清除新版本标记
                    OTAHandler.newTouchPadVersion = false
                }
            }
        }

        scope.launch {
            /**
             * 电量监控
             */
            batteryManager.batteryLeveFlow.collect { batteryLevel ->
                logTagD(TAG, "电量发生变化:${batteryLevel}")
                statusBroadcastUtil.updateBatteryLevel(
                    batteryLevel,
                    statusManager.lastDockStatus,
                    touchPadVersion
                )
                binder.updateConnectStatusToClient(battery = batteryLevel)
            }
        }

        scope.launch {
            /**
             * 版本监控
             */
            batteryManager.touchPadVersionFlow.collect { version ->
                logTagD(TAG, "版本发生变化:${version.version}")
                launch {
                    checkHasNewVersion(version.version)
                }
                binder.updateConnectStatusToClient(version = version.version)
                val lastLetter = (version.version.lastOrNull()?.toString() ?: "").lowercase()
                val connectVersion = when {
                    TouchPadHardwareVersion.V1.versionStr.contains(lastLetter) -> TouchPadHardwareVersion.V1
                    TouchPadHardwareVersion.V2.versionStr.contains(lastLetter) -> TouchPadHardwareVersion.V2
                    else -> TouchPadHardwareVersion.UNKNOWN
                }
                statusBroadcastUtil.updateVersion(connectVersion)
            }
        }

    }

    private suspend fun checkHasNewVersion(version: String) {
        logTagV(TAG, "checkHasNewVersion:$version")
        if (version.isEmpty()) {
            logTagV(TAG, "未获取触控板的版本号")
            OTAHandler.newTouchPadVersion = false
            return
        }
        withContext(Dispatchers.IO) {
            doWithoutCatch {
                val body: TouchPadVersionCheckInfo? =
                    touchNetService.versionCheck(version).withCheck().body
                val needUpdate = (body?.update ?: 0) != 0
                logTagV(TAG, "定时检查触控板新固件:${needUpdate}")
                OTAHandler.newTouchPadVersion = needUpdate
            }

            val lastLetter = (version.lastOrNull()?.toString() ?: "").lowercase()
            touchPadVersion = when {
                TouchPadHardwareVersion.V1.versionStr.contains(lastLetter) -> TouchPadHardwareVersion.V1
                TouchPadHardwareVersion.V2.versionStr.contains(lastLetter) -> TouchPadHardwareVersion.V2
                else -> TouchPadHardwareVersion.UNKNOWN
            }
        }
    }

    private suspend fun doWithNewStatus(status: TPDeviceStatus) {
        when (status) {
            TPDeviceStatus.STATUS_INIT -> {
                logTagV(TAG, "TouchPad初始化")
                initTouchPad()
            }

            TPDeviceStatus.STATUS_CONN_ON -> {
                logTagD(TAG, "连接触控板并开始充电")
                // 弹出toast满足3个条件:  放在充电位置, 连接成功, 连接的是上次配对的那个
                val pairMac = bluetoothUtil.pairTouchPadMac
                val connectMac = bluetoothUtil.connectingDevices?.address ?: "null"
                logTagV(TAG, "pairMac:${pairMac}")
                logTagV(TAG, "connectMac:${connectMac}")
                logTagV(TAG, "lastConnectMac:${lastConnectMac}")
                if (pairMac == connectMac && lastConnectMac != connectMac) {
                    scope.launch(Dispatchers.Main) {
                        logTagV(TAG, "展示连接成功Toast")
                        statusBroadcastUtil.updateStatusConnected()  // 通知系统 会取消掉息屏
                        // 这里等1s是因为, 在连接的时候, 会和自动息屏的UI混在一起, 不方便看
                        delay(ONE_SECOND)
                        statusBroadcastUtil.onNewTouchPadConnect()
                        lastConnectMac = connectMac
                        Settings.Global.putString(
                            <EMAIL>,
                            KEY_TOUCH_PAD_MAC,
                            lastConnectMac
                        )
                    }
                }
                bluetoothUtil.pairTouchPadMac = ""  // 清空之前的信息
            }

            TPDeviceStatus.STATUS_CONN_LEAVE -> {
                logTagD(TAG, "触控板离开充电位置")
                stopScan()
            }

            TPDeviceStatus.STATUS_DIS_ON -> {
                logTagD(TAG, "未连接, 放在充电位置")
                tryConnectTouchPad()
            }

            TPDeviceStatus.STATUS_DIS_LEAVE -> {
                logTagD(TAG, "未连接, 没有触控板")
                stopScan()
            }
        }
    }

    /**
     * 初始化各种状态
     */
    private fun initTouchPad() {
        // 注册广播
        scope.launch {
            val dockStatus = async { dockUtil.loadDockStatus() }
            val connectStatus = async { bluetoothUtil.hasConnected() }
            statusManager.updateStatus(
                dockStatus = dockStatus.await().also {
                    logTagV(TAG, "手写板位置状态:${it}")
                },
                connect = connectStatus.await().also {
                    logTagV(TAG, "初始连接状态:${it}")
                }
            )
            // 添加连接回调
            logTagV(TAG, "注册触控板状态改变回调")
            dockUtil.registerDockReceiver(this@TouchPadService)
            bluetoothUtil.onTouchPadConnectChange = ::onTouchPadConnectChange
            bluetoothUtil.registerBTReceiver(this@TouchPadService)
        }

        if (USE_LINUX_CHECK) {
            // 暂时先不用这个方法
            checkConnectStatusByLinuxJob = scope.launch(Dispatchers.IO) {
                while (isActive) {
                    delay(15 * ONE_SECOND)
                    val process = Runtime.getRuntime().exec("cat /proc/bus/input/devices")
                    val checkByLinux =
                        process.inputStream.bufferedReader().lines().filter { it.startsWith("N: ") }
                            .anyMatch {
                                it.contains("StarryHub Touch")
                            }
                    logTagV(TAG, "Linux检测结果:${checkByLinux}")

                    if (checkByLinux != statusManager.isConnecting) {
                        logTagW(
                            TAG,
                            "linux检测:${checkByLinux}  蓝牙api检测:${statusManager.isConnecting}"
                        )
                        binder.checkTouchPadStatus()
                    }
                }
            }
        }
    }

    /**
     * 尝试连接到触控板
     */
    private suspend fun tryConnectTouchPad() {
        // 停止之前的扫描
        scanTimeOutJob?.let {
            stopScan()
            delay(500)
        }

        logTagD(TAG, "开始扫描")
        bluetoothUtil.startScanBroadcast { list ->
            val touchPad = list.filter {
                it.broadcastHead == BROADCAST_HEAD_5
            }.maxByOrNull {
                it.rssi // 选取信号最强的
            }

            if (touchPad == null) {
                logTagV(TAG, "没有找到符合条件的设备")
            } else {
                // 找到希望连接的设备了, 停止扫描,并开始自动连接
                scanTimeOutJob?.cancel()
                bluetoothUtil.stopScanBroadcast()
                autoConnect(touchPad)
            }
        }
        // 启动线程来处理扫描超时
        startScanTimeOut()
    }

    /**
     * 尝试自动连接
     */
    private fun autoConnect(touchPad: TouchPad) {
        logTagD(TAG, "自动连接")
        // 1. 尝试连接到新的触控板
        bluetoothUtil.connect(touchPad.device!!)
    }

    private fun startScanTimeOut() {
        scanTimeOutJob?.cancel()
        scanTimeOutJob = scope.launch {
            delay(SCAN_TIME)
            logTagW(TAG, "蓝牙扫描超时,停止扫描")
            bluetoothUtil.stopScanBroadcast()
        }
    }

    /**
     * 停止扫描
     */
    private fun stopScan() {
        scanTimeOutJob?.let {
            logTagD(TAG, "停止之前的扫描")
            it.cancel()
        }
        bluetoothUtil.stopScanBroadcast()
    }

    /**
     * 触控板位置改变回调
     */
    private fun onPowerDockStatusChange(dockStatus: Boolean) {
        logTagV(TAG, "充电位置改变:${dockStatus}")
        statusManager.updateStatus(dockStatus = dockStatus)
    }

    /**
     * 蓝牙连接状态改变回调
     */
    private fun onTouchPadConnectChange(hasConnect: Boolean) {
        logTagV(TAG, "触控板连接状态改变:${hasConnect}")
        statusManager.updateStatus(connect = hasConnect)
    }

    override fun onDestroy() {
        batteryManager.resetTouchPadInfo()
        gattManager?.disconnect()
        gattManager = null
        bluetoothUtil.unregisterBTReceiver(this)
        dockUtil.unregisterDockReceiver(this)
        scope.cancel()
        logTagW(TAG, "TouchPadService Destroy!!!")

        if (!checkIgnoreTouchPad()) {
            // 注销自定义快捷键的广播
            customShortcutKeyReceiver.unregister(this)
        }

        super.onDestroy()
    }


    override fun onBind(intent: Intent?): IBinder = binder
}