package com.czur.starry.device.settings.ui.net.wifi

import android.app.Activity
import android.content.Intent
import android.view.KeyEvent
import androidx.activity.result.contract.ActivityResultContracts
import androidx.fragment.app.viewModels
import com.czur.czurutils.log.logTagD
import com.czur.starry.device.baselib.base.CZURAtyManager.appContext
import com.czur.starry.device.baselib.base.listener.KeyDownListener
import com.czur.starry.device.baselib.view.BaseFragment
import com.czur.starry.device.settings.R
import com.czur.starry.device.settings.ui.net.wifi.WifiViewModel.Companion.SHOW_DETAIL
import com.czur.starry.device.settings.ui.net.wifi.WifiViewModel.Companion.SHOW_DISPLAY
import com.czur.starry.device.settings.ui.net.wifi.WifiViewModel.Companion.SHOW_JOIN
import com.czur.starry.device.settings.ui.net.wifi.WifiViewModel.Companion.SHOW_OTHER
import com.czur.starry.device.settings.ui.net.wifi.WifiViewModel.Companion.SHOW_VERIFY
import com.czur.starry.device.settings.ui.net.wifi.WifiViewModel.Companion.SHOW_VERIFY_KEY

/**
Wifi模块各个Fragment的结构关系图:
|---WifiFragment
    |---WifiDisplayFragment: 显示wifi列表,wifi开关等功能
        |---WifiEnableFragment:   wifi开关
        |---WifiListFragment:     显示wifi列表
    |---WifiJoinFragment:    加入指定wifi画面
    |---WifiOtherFragment:   加入其他网络画面
 **/
class WifiFragment : BaseFragment(), KeyDownListener {

    override fun getLayoutId() = R.layout.fragment_wifi

    companion object {
        private const val TAG = "WifiFragment"

        private const val FRAGMENT_TAG_DISPLAY = "displayFragment"
        private const val FRAGMENT_JOIN = "joinFragment"
        private const val FRAGMENT_OTHER = "otherFragment"
        public const val FRAGMENT_DETAIL = "detailFragment"
    }

    private val wifiViewModel: WifiViewModel by viewModels({ requireActivity() })

    private val displayFragment by lazy { WifiDisplayFragment() }


    override fun initView() {
        wifiViewModel.showDisplay()

        childFragmentManager.beginTransaction()
            .add(R.id.wifiMainContent, displayFragment, FRAGMENT_TAG_DISPLAY)
            .commit()

        wifiViewModel.showPageLive.observe(this) {
            when (it) {
                SHOW_DISPLAY -> showDisplayFragment()
                SHOW_OTHER -> showOtherFragment()
                SHOW_JOIN -> showJoinFragment()
                SHOW_DETAIL -> showDetailFragment()
                SHOW_VERIFY -> startCaptivePortalLogin()
            }
        }
    }

    /**
     * 显示默认Fragment
     */
    private fun showDisplayFragment() {
        logTagD(TAG, "显示displayFragment")
        if (displayFragment.isHidden) {
            val transaction = childFragmentManager.beginTransaction()

            // 删除其他的Fragment
            val joinFragment = childFragmentManager.findFragmentByTag(FRAGMENT_JOIN)
            joinFragment?.let {
                logTagD(TAG, "移除JoinFragment")
                transaction.remove(it)
            }

            val otherFragment = childFragmentManager.findFragmentByTag(FRAGMENT_OTHER)
            otherFragment?.let {
                logTagD(TAG, "移除OtherFragment")
                transaction.remove(it)
            }

            val detailFragment = childFragmentManager.findFragmentByTag(FRAGMENT_DETAIL)
            detailFragment?.let {
                logTagD(TAG, "移除DetailFragment")
                transaction.remove(it)
            }
            transaction.show(displayFragment).commit()
        }
    }

    /**
     * 显示输入密码页面
     */
    private fun showJoinFragment() {
        val joinFragment = childFragmentManager.findFragmentByTag(FRAGMENT_JOIN)
        joinFragment ?: run {
            //没有找到JoinFragment
            childFragmentManager.beginTransaction()
                .hide(displayFragment) // 隐藏displayFragment
                .add(R.id.wifiMainContent, WifiJoinFragment::class.java, null, FRAGMENT_JOIN)
                .commit()
        }
    }

    /**
     * 显示其他WIFI画面
     */
    private fun showOtherFragment() {
        val otherFragment = childFragmentManager.findFragmentByTag(FRAGMENT_OTHER)
        otherFragment ?: run {
            //没有找到JoinFragment
            childFragmentManager.beginTransaction()
                .hide(displayFragment) // 隐藏displayFragment
                .add(R.id.wifiMainContent, WifiOtherFragment::class.java, null, FRAGMENT_OTHER)
                .commit()
        }
    }

    /**
     * 显示详情画面
     */
    private fun showDetailFragment() {
        val detailFragment = childFragmentManager.findFragmentByTag(FRAGMENT_DETAIL)
        detailFragment ?: run {
            childFragmentManager.beginTransaction()
                .hide(displayFragment) // 隐藏displayFragment
                .add(R.id.wifiMainContent, WifiDetailFragment::class.java, null, FRAGMENT_DETAIL)
                .commit()
        }

    }

    /**
     * 启动CaptivePortal页面登陆验证
     */
    private fun startCaptivePortalLogin() {
        val intent = Intent(appContext,CaptivePortalWebViewActivity::class.java)
        startVerify.launch(intent)
    }
    private val startVerify = registerForActivityResult(ActivityResultContracts.StartActivityForResult()) { result ->
        logTagD(TAG,"==registerForActivityResult====")
        if (result.resultCode == Activity.RESULT_OK) {
            val data = result.data
            val result = data?.getBooleanExtra(SHOW_VERIFY_KEY,true) as Boolean
            if (!result) {
                wifiViewModel.isNeedVerify.postValue(result)
            }
            // 处理返回的数据
            logTagD(TAG,"Result data: ${result}")
        }
    }
    /**
     * note:只能由最外层的Fragment来控制viewModel的生命周期
     */
    override fun onResume() {
        super.onResume()
        wifiViewModel.resume()
    }

    override fun onKeyDown(keyCode: Int, event: KeyEvent?): Boolean {
        return if (keyCode == KeyEvent.KEYCODE_BACK && wifiViewModel.showPage == SHOW_JOIN) {
            wifiViewModel.showDisplay()
            true
        } else {
            false
        }
    }
}