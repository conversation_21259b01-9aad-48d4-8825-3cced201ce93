package com.czur.starry.device.settings.adapter

import android.annotation.SuppressLint
import android.graphics.drawable.Drawable
import android.view.ViewGroup
import com.czur.czurutils.log.logTagD
import com.czur.starry.device.baselib.base.BaseDifferAdapter
import com.czur.starry.device.baselib.base.BaseVH
import com.czur.starry.device.settings.R

/**
 * Created by 陈丰尧 on 2023/7/4
 */
data class FavAppItem(val packageName: String, val appName: String, val icon: Drawable)

class LauncherFavAppAdapter(private val type: Type) : BaseDifferAdapter<FavAppItem>() {
    enum class Type {
        INSTALL,        // 已安装应用
        FAVOURITE       // 收藏应用
    }

    var showActionIv: Boolean = true
        @SuppressLint("NotifyDataSetChanged")
        set(value) {
            if (field != value) {
                field = value
                notifyDataSetChanged()
            }
        }

    override fun bindViewHolder(holder: <PERSON>VH, position: Int, itemData: FavAppItem) {
        when (type) {
            Type.INSTALL -> {
                holder.setImgResource(R.drawable.ic_fav_app_add, R.id.favAppActionIv)
            }

            Type.FAVOURITE -> {
                holder.setImgResource(R.drawable.ic_fav_app_del, R.id.favAppActionIv)
            }
        }
        holder.visibleNotGone(showActionIv, R.id.favAppActionIv)
        holder.setText(itemData.appName, R.id.favAppNameTv)
        holder.setImgDrawable(itemData.icon, R.id.favAppIconIv)
    }

    override fun areItemsTheSame(oldItem: FavAppItem, newItem: FavAppItem): Boolean {
        return oldItem.packageName == newItem.packageName
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): BaseVH {
        return BaseVH(R.layout.item_launcher_fav_app, parent)
    }

    //语音获取指定应用名的位置
    fun getPositionFromAppName(currentList: List<FavAppItem>, appName: String): Int {
        val exactMatchPos =
            currentList.indexOfFirst { it.appName.equals(appName, ignoreCase = true) }
        if (exactMatchPos != -1) {
            return exactMatchPos
        }
        if (appName.length >= 2) {
            return currentList.indexOfFirst { it.appName.contains(appName, ignoreCase = true) }
        }
        return -1
    }
}