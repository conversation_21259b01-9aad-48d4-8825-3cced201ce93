package com.czur.starry.device.settings.ui.system.lang

import android.content.ComponentName
import android.content.Intent
import android.os.Bundle
import androidx.fragment.app.viewModels
import androidx.lifecycle.lifecycleScope
import com.czer.starry.device.meetlib.MeetingHandler
import com.czur.czurutils.log.logTagD
import com.czur.starry.device.baselib.utils.ONE_SECOND
import com.czur.starry.device.baselib.utils.doWithoutCatch
import com.czur.starry.device.baselib.utils.launch
import com.czur.starry.device.baselib.utils.repeatCollectOnResume
import com.czur.starry.device.baselib.utils.setOnDebounceClickListener
import com.czur.starry.device.baselib.view.floating.FloatShowMode
import com.czur.starry.device.baselib.view.floating.common.DoubleBtnCommonFloat
import com.czur.starry.device.baselib.widget.OnRecyclerItemClickListener
import com.czur.starry.device.settings.R
import com.czur.starry.device.settings.adapter.LanguageAdapter
import com.czur.starry.device.settings.base.BaseBindingMenuFragment
import com.czur.starry.device.settings.databinding.FragmentLangBinding
import com.czur.uilib.extension.rv.addCornerRadius
import kotlinx.coroutines.delay

/**
 * Created by 陈丰尧 on 2023/6/19
 */
private const val TAG = "LangFragment"

class LangFragment : BaseBindingMenuFragment<FragmentLangBinding>() {
    private val langVM: LangVM by viewModels()
    private val languageAdapter by lazy { LanguageAdapter(viewLifecycleOwner.lifecycleScope) }
    private var tipsCloseMeeting: DoubleBtnCommonFloat? = null

    override fun FragmentLangBinding.initBindingViews() {
        languageRv.addOnItemTouchListener(OnRecyclerItemClickListener().setOnClickListener { vh, view ->
            val pos = vh.bindingAdapterPosition
            changeLangSel(pos)
            true
        })
        languageRv.adapter = languageAdapter
        languageRv.addCornerRadius(backgroundColor = requireContext().getColor(R.color.bg_setting_normal))

        gBoardBgView.setOnDebounceClickListener {
            // 启动输入法设置页面
            logTagD(TAG, "启动输入法设置页面")
            doWithoutCatch {
                bootGBoardSetting() // 防止找不到对应的页面
            }
        }

        applyBtn.setOnDebounceClickListener {
            launch {
                logTagD(TAG, "设置语言")
                if (needShowHint()) {
                    showCloseMeetingDialog()
                } else {
                    changeLang(false)
                }
            }
        }
    }

    override fun initData(savedInstanceState: Bundle?) {
        repeatCollectOnResume(langVM.saveBtnEnableFlow) {
            binding.applyBtn.isEnabled = it
        }
    }

    private fun changeLangSel(sel: Int) {
        languageAdapter.updateSelect(sel)
        langVM.updateLanguageSelect(languageAdapter.getSelect())
    }

    private fun bootGBoardSetting() {
        val intent = Intent()
        intent.`package` = "com.google.android.inputmethod.latin"
        intent.action = "android.intent.action.MAIN"
        intent.component = ComponentName(
            "com.google.android.inputmethod.latin",
            "com.google.android.apps.inputmethod.latin.preference.SettingsActivity"
        )
        startActivity(intent)
    }

    /**
     * 判断是需要弹出切换语言的提示框
     */
    private fun needShowHint(): Boolean {
        /// 是否在starryMeeting,本地录音，本地录像中
        return MeetingHandler.localMeetingRecording ||
                MeetingHandler.localMeetingVideoRecording
    }

    private fun showCloseMeetingDialog() {
        tipsCloseMeeting = DoubleBtnCommonFloat(
            title = getString(R.string.startup_wifi_pwd_error_dialog_title),
            content = getString(R.string.str_switch_language_closed_app_hint),
            cancelBtnText = getString(R.string.str_switch_language_closed_app_no),
            confirmBtnText = getString(R.string.str_switch_language_closed_app_yes),
            showMode = FloatShowMode.SINGLE,
            onCommonClick = { commonFloat, position ->
                commonFloat.dismiss()
                if (position == DoubleBtnCommonFloat.DOUBLE_FLOAT_BTN_CONFIRM) {
                    launch {
                        changeLang(true)
                    }
                }
            }
        ).apply {
            setOnDismissListener {
                tipsCloseMeeting = null
            }
            show()
        }
    }

    private suspend fun changeLang(needSendBroadcast: Boolean) {
        if (needSendBroadcast) {
            logTagD(TAG, "发送切换语言广播, 停止对应应用")
            // 正常应该不用发这个广播, 应用自己能处理呀
            sendSwitchLanguageBroadcast()
            delay(1 * ONE_SECOND)
        }
        /// 开始切换语言
        val selLocale = languageAdapter.getSelect()
        langVM.saveLanguage(selLocale)
    }

    private fun sendSwitchLanguageBroadcast() {
        val intent = Intent().apply {
            action = "com.android.action.CZUR_SWITCH_LANGUAGE"
        }
        activity?.sendBroadcast(intent)
    }
}