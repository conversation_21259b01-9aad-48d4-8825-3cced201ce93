package com.czur.starry.device.settings.ui.personalization.wallpaper.utils

import android.graphics.Rect
import android.view.View
import androidx.recyclerview.widget.RecyclerView

private const val SPAN_COUNT = 3
private const val HEAD_ITEM_COUNT = 0
private const val TOP_SPACE = 20
class SpacesItemDecoration(private val space: Int = 50) : RecyclerView.ItemDecoration() {

    override fun getItemOffsets(
        outRect: Rect,
        view: View,
        parent: RecyclerView,
        state: RecyclerView.State
    ) {

        super.getItemOffsets(outRect, view, parent, state)

        // Logic to set spaces. For example, no top margin for first row items.
        val position = parent.getChildAdapterPosition(view)
        val column = position % SPAN_COUNT
        outRect.left = column * space / SPAN_COUNT
        outRect.right = space - (column + 1) * space / SPAN_COUNT
        if(position >= SPAN_COUNT){
            outRect.top = space
        }
    }


}