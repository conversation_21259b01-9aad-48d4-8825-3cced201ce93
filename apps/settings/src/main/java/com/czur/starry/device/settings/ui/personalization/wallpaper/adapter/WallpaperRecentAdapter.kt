package com.czur.starry.device.settings.ui.personalization.wallpaper.adapter


import android.content.Context
import android.graphics.drawable.Drawable
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ProgressBar
import android.widget.TextView
import androidx.constraintlayout.utils.widget.ImageFilterView
import androidx.recyclerview.widget.AsyncListDiffer
import androidx.recyclerview.widget.DiffUtil
import androidx.recyclerview.widget.RecyclerView
import com.bumptech.glide.Glide
import com.bumptech.glide.load.DataSource
import com.bumptech.glide.load.engine.GlideException
import com.bumptech.glide.request.RequestListener
import com.bumptech.glide.request.target.Target
import com.bumptech.glide.signature.ObjectKey
import com.czur.starry.device.baselib.utils.gone
import com.czur.starry.device.settings.R
import com.czur.starry.device.settings.ui.personalization.wallpaper.bean.FileEntity
import com.czur.starry.device.settings.ui.personalization.wallpaper.bean.FileMode

private const val TAG = "WallpaperRecentAdapter"

class WallpaperRecentAdapter : RecyclerView.Adapter<RecyclerView.ViewHolder>() {


    private var mContext: Context? = null
    private var mData: MutableList<FileEntity>? = null
    private var fileMode = FileMode.RECENT

    private val diffCallback = object : DiffUtil.ItemCallback<FileEntity>() {
        override fun areItemsTheSame(oldItem: FileEntity, newItem: FileEntity): Boolean {
            return (oldItem.absPath == newItem.absPath
                    && oldItem.lastModifyTime == newItem.lastModifyTime)
        }

        override fun areContentsTheSame(oldItem: FileEntity, newItem: FileEntity): Boolean {
            return (oldItem == newItem && oldItem.lastModifyTime == oldItem.lastModifyTime)
        }
    }
    private val mDiffer: AsyncListDiffer<FileEntity> = AsyncListDiffer(this, diffCallback)


    fun setData(
        context: Context,
        data: MutableList<FileEntity>,
        mode: FileMode = FileMode.RECENT
    ) {
        fileMode = mode
        mContext = context
        mData = data
        mDiffer.submitList(data)
    }


    fun refreshData(data: MutableList<FileEntity>, fileMode: FileMode = FileMode.RECENT) {
        mDiffer.submitList(data)
    }

    override fun getItemCount(): Int {
        return mDiffer.currentList.size
    }


    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): RecyclerView.ViewHolder {
        val itemView: View =
            LayoutInflater.from(parent.context)
                .inflate(R.layout.item_wallpaper_recent, parent, false)
        return NormalHolder(itemView)
    }


    override fun onBindViewHolder(holder: RecyclerView.ViewHolder, position: Int) {
        val normalHolder = holder as NormalHolder
        val data = mDiffer.currentList[position]

        Glide.with(holder.itemView.context)
            .load(data.absPath)
            .signature(ObjectKey(System.currentTimeMillis()))
            .listener(object : RequestListener<Drawable> {
                override fun onLoadFailed(
                        e: GlideException?,
                        model: Any?,
                        target: Target<Drawable>,
                    isFirstResource: Boolean
                ): Boolean {
                    return false
                }

                override fun onResourceReady(
                    resource: Drawable,
                    model: Any,
                    target: Target<Drawable>?,
                    dataSource: DataSource,
                    isFirstResource: Boolean
                ): Boolean {
                    normalHolder.progressBar.gone()
                    return false
                }

            })
            .into(normalHolder.mImage)


    }


    class NormalHolder(itemView: View) : RecyclerView.ViewHolder(itemView) {
        var mImage: ImageFilterView
        var mtvStop: TextView
        var progressBar: ProgressBar

        init {
            mtvStop = itemView.findViewById(R.id.tvStop) as TextView
            mImage = itemView.findViewById(R.id.imTemplate) as ImageFilterView
            progressBar = itemView.findViewById(R.id.progressBar) as ProgressBar
        }
    }


}
