package com.czur.starry.device.settings.ui.personalization.wallpaper.utils

import android.content.Context
import android.graphics.Bitmap
import android.graphics.BitmapFactory
import android.graphics.Matrix
import android.util.Size
import androidx.exifinterface.media.ExifInterface
import com.czur.czurutils.log.logTagD
import com.czur.czurutils.log.logTagV
import com.czur.czurutils.log.logTagW
import com.czur.starry.device.baselib.utils.getString
import com.czur.starry.device.settings.R
import com.czur.starry.device.settings.app.App.Companion.app
import com.czur.starry.device.settings.ui.personalization.wallpaper.bean.FileEntity
import com.czur.starry.device.settings.ui.personalization.wallpaper.`interface`.CopyTask
import com.czur.starry.device.settings.ui.personalization.wallpaper.task.LocalTask
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.isActive
import kotlinx.coroutines.withContext
import java.io.File
import java.io.IOException
import kotlin.coroutines.cancellation.CancellationException

/**
 *  author : WangHao
 *  time   :2024/01/15
 */

const val TAG = "WallpaperUtil"

//本地或者云文件
fun copyTask(srcFile: FileEntity): CopyTask {
    return LocalTask()
}

//获取自定义命名
@Synchronized
fun getScreenDropName(data: MutableList<FileEntity>): String {
    var name = ""
    var nameFirst = System.currentTimeMillis().toString()
    val fileNames = data.filter { it.name != "" }.map {
        it.name.substringBeforeLast(".")
    }
    logTagD(TAG, "====fileNames=$fileNames")
    var index = 1
    do {
        name = if (index == 1) nameFirst else "${nameFirst}(${index})"
        index++
    } while (name in fileNames)

    logTagD(TAG, "====name=$name")
    return name
}

//获取自定义命名
@Synchronized
fun getWallpaperName(data: MutableList<FileEntity>): String {
    var name = "(1)"
    var nameFirst = getString(R.string.wallpaper_self_define_title)
    val fileNames = data.filter { it.name != "" }.map {
            it.name.substringBeforeLast(".")
        }
    logTagD(TAG, "====fileNames=$fileNames")
    var index = 1
    do {
        name = "${nameFirst}(${index})"
        index++
    } while (name in fileNames)

    logTagD(TAG, "====name=$name")
    return name
}


suspend fun getAssetsFileNames(dir: String): MutableList<String> = withContext(Dispatchers.IO) {
    val list = app.assets.list(dir)
    list?.forEach {
        logTagD(TAG, "$dir/$it")
    }
    list!!.toMutableList()
}


/**
 * 递归计算文件大小
 */
suspend fun File.sizeRecursive(): Long {
    return withContext(Dispatchers.IO) {
        walkTopDown().sumOf {
            it.length()
        }
    }
}


/**
 * 复制或剪切文件
 * 如果目标位置有的话, 则则直接覆盖
 */

suspend fun File.copyTo(
    target: File,
    bufferSize: Int = DEFAULT_BUFFER_SIZE,
    handleSize: (handleSize: Long) -> Unit,
): Unit = withContext(Dispatchers.IO) {
    val tag = "File.copyTo(协程)"
    val srcFile = this@copyTo
    if (target.exists()) {
        // 删除源文件
        logTagD(tag, "删除源文件")
        if (!target.deleteRecursively())
            throw FileAlreadyExistsException(
                file = srcFile,
                other = target,
                reason = "Tried to overwrite the destination, but failed to delete it."
            )
    }

    // 如果是文件夹
    if (srcFile.isDirectory) {
        logTagD(tag, "复制文件夹")
        // 复制文件夹内的数据
        listFiles()?.forEach { subFile ->
            if (isActive) {
                subFile.copyTo(File(target, subFile.name), bufferSize, handleSize)
            } else {
                logTagV(tag, "跳过其他文件")
                return@forEach
            }
        }
    } else {
        // 如果是文件
        logTagV(tag, "${srcFile.name} 是文件")
        target.parentFile?.mkdirs()

        srcFile.inputStream().use { input ->
            target.outputStream().use { output ->

                try {
                    val buffer = ByteArray(bufferSize)
                    var bytes = input.read(buffer)
                    while (bytes >= 0) {
                        if (!isActive) {
                            throw CancellationException()
                        }
                        output.write(buffer, 0, bytes)
                        // 更新进度
                        handleSize(bytes.toLong())
                        bytes = input.read(buffer)
                    }
                } catch (e: Exception) {
                    target.delete()
                    if (e is CancellationException) {
                        logTagW(tag, "复制被取消")
                    } else {
                        throw e
                    }
                }

            }
        }
    }

}


fun px2dp(context: Context, dp: Int): Int {
    return (dp / context.resources.displayMetrics.density + 0.5).toInt()
}


suspend fun getBitmap(path: String, mWidth: Int, mHeight: Int): Bitmap =
    withContext(Dispatchers.IO) {
        // 1. 首先获取旋转角度
        val orientation = getImageOrientation(path)
        val targetSize = when (orientation) {
            // 旋转90度则宽高需要交换一下
            ExifInterface.ORIENTATION_ROTATE_90, ExifInterface.ORIENTATION_ROTATE_270
            -> Size(mHeight, mWidth)

            else -> Size(mWidth, mHeight)
        }
        val tmpOptions = BitmapFactory.Options()
        tmpOptions.inJustDecodeBounds = true
        BitmapFactory.decodeFile(path, tmpOptions)


        val width = tmpOptions.outWidth
        val height = tmpOptions.outHeight

        var scale = 1
        if (height > targetSize.height) {
            scale = (height / targetSize.height)
        } else if (width > targetSize.width) {
            scale = (width / targetSize.width)
        }
        if (scale <= 0) {
            scale = 1
        }
        tmpOptions.inSampleSize = scale

        tmpOptions.inJustDecodeBounds = false

        val bitmap = BitmapFactory.decodeFile(path, tmpOptions)
        val m = Matrix()
        // 将图片旋转回位
        val dig = when (orientation) {
            ExifInterface.ORIENTATION_ROTATE_90 -> 90
            ExifInterface.ORIENTATION_ROTATE_180 -> 180
            ExifInterface.ORIENTATION_ROTATE_270 -> 270
            else -> 0
        }
        if (dig != 0) {
            m.postRotate(dig.toFloat())
            Bitmap.createBitmap(bitmap, 0, 0, bitmap.width, bitmap.height, m, true)
        } else {
            bitmap
        }
    }

/**
 * 获取图片的旋转角度
 */
fun getImageOrientation(imageLocalPath: String): Int {
    return try {
        val exifInterface = ExifInterface(imageLocalPath)
        exifInterface.getAttributeInt(
            ExifInterface.TAG_ORIENTATION,
            ExifInterface.ORIENTATION_NORMAL
        )
    } catch (e: IOException) {
        e.printStackTrace()
        ExifInterface.ORIENTATION_NORMAL
    }
}
