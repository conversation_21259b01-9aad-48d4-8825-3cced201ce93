package com.czur.starry.device.settings.widget

import android.content.Context
import android.util.AttributeSet
import android.view.MotionEvent
import androidx.appcompat.widget.AppCompatImageView

/**
 * Created by 陈丰尧 on 2/3/21
 */
class HoldImageView : AppCompatImageView {
    constructor(context: Context) : super(context)
    constructor(context: Context, attrs: AttributeSet?) : super(context, attrs)
    constructor(context: Context, attrs: AttributeSet?, defStyleAttr: Int) : super(
        context,
        attrs,
        defStyleAttr
    )

    companion object {
        // 按住500ms后触发长按
        const val LONG_PRESS_THRESHOLD = 500L
    }

    var startHold = false
    var onHoldListener: OnHoldListener? = null
    var holdRunnable = Runnable {
        onHoldListener?.onHoldStart()
        startHold = true
    }

    override fun onTouchEvent(event: MotionEvent): Boolean {
        when (event.action) {
            MotionEvent.ACTION_DOWN -> {
                handler.postDelayed(holdRunnable, LONG_PRESS_THRESHOLD)
                return true
            }

            MotionEvent.ACTION_UP -> {
                handler.removeCallbacks(holdRunnable)
                if (startHold) {
                    onHoldListener?.onHoldStop()
                } else {
                    performClick()
                }
                startHold = false
                return true
            }
            MotionEvent.ACTION_CANCEL -> {
                startHold = false
                return true
            }
        }
        return super.onTouchEvent(event)
    }

    interface OnHoldListener {
        fun onHoldStart()
        fun onHoldStop()
    }
}