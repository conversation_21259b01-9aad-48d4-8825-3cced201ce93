package com.czur.starry.device.settings.utils

import androidx.fragment.app.Fragment
import com.czur.starry.device.baselib.common.Constants
import com.czur.starry.device.baselib.common.StarryDevLocale
import com.czur.starry.device.baselib.common.VersionIndustry
import com.czur.starry.device.baselib.common.hw.Q1Series
import com.czur.starry.device.baselib.common.hw.Q2Series
import com.czur.starry.device.baselib.common.hw.StarryModel
import com.czur.starry.device.baselib.common.hw.StudioSeries
import com.czur.starry.device.baselib.utils.SettingUtil
import com.czur.starry.device.baselib.utils.getString
import com.czur.starry.device.settings.R
import com.czur.starry.device.settings.ui.cameraandmic.MeetingCameraFragment
import com.czur.starry.device.settings.ui.cameraandmic.SixMicSoundPickerFragment
import com.czur.starry.device.settings.ui.personalization.apps.LauncherFavAppsFragment
import com.czur.starry.device.settings.ui.personalization.backdrop.ScreenBackdropFragment
import com.czur.starry.device.settings.ui.personalization.shortcut.CustomShortcutKeysFragment
import com.czur.starry.device.settings.ui.personalization.wallpaper.WallpaperFragmentList
import com.czur.starry.device.settings.ui.net.InternetSpeedFragment
import com.czur.starry.device.settings.ui.net.ethernet.WiredNetworkFragment
import com.czur.starry.device.settings.ui.net.wifi.WifiFragment
import com.czur.starry.device.settings.ui.personalization.assistant.AIAssistantFragment
import com.czur.starry.device.settings.ui.projector.FocusFragment
import com.czur.starry.device.settings.ui.projector.KeystoneCorrectionFragment
import com.czur.starry.device.settings.ui.projector.ScreenFragment
import com.czur.starry.device.settings.ui.projector.VoiceFragment
import com.czur.starry.device.settings.ui.projector.audio.MicChooseFragment
import com.czur.starry.device.settings.ui.projector.bluetooth.BTKeyboardFragment
import com.czur.starry.device.settings.ui.projector.cec.CECFragment
import com.czur.starry.device.settings.ui.projector.audio.SpeakerChooseFragment
import com.czur.starry.device.settings.ui.projector.brightness.ScreenBrightnessFragment
import com.czur.starry.device.settings.ui.projector.touchpad.TouchPadFragment
import com.czur.starry.device.settings.ui.system.CleanAllFragment
import com.czur.starry.device.settings.ui.system.SystemInfoFragment
import com.czur.starry.device.settings.ui.system.UpdateVersionFragment
import com.czur.starry.device.settings.ui.system.bootstart.BootStartFragment
import com.czur.starry.device.settings.ui.system.clean.CleanRoomFragment
import com.czur.starry.device.settings.ui.system.feedback.FeedbackFragment
import com.czur.starry.device.settings.ui.system.lang.LangFragment
import com.czur.starry.device.settings.ui.system.time.TimeFragment
import com.czur.starry.device.settings.ui.system.time.TimeZoneFragment

/**
 * Created by 陈丰尧 on 1/21/21
 */
class MenuUtil {
    companion object {
        private const val TAG = "MenuUtil"
        private const val MENU_GROUP_KEY_OUTPUT = "output"              // 输出
        private const val MENU_GROUP_KEY_PROJECTOR = "projector"        // 投影
        const val MENU_GROUP_KEY_CAMERA_MIC = "cameraAndMic"            // 摄像头和麦克风
        const val MENU_GROUP_KEY_PERIPHERAL = "peripheral"              // 外设
        private const val MENU_GROUP_KEY_NET = "net"                    // 网络
        const val MENU_GROUP_KEY_SYSTEM = "system"                      // 系统
        private const val MENU_GROUP_KEY_LAUNCHER = "launcher"          // 桌面

        const val SUB_MENU_KEY_WIFI = "wifi"
        const val SUB_MENU_KEY_WIRED_NET = "wiredNet"
        const val SUB_MENU_KEY_INTERNET_SPEED = "internetSpeed"

        const val SUB_MENU_KEY_FOCUS = "focus"
        const val SUB_MENU_KEY_MEETING_CAMERA = "meetingCamera"
        const val SUB_MENU_KEY_MIC_CHOOSE = "micChoose"
        const val SUB_MENU_SCREEN_BRIGHTNESS = "screenBrightness" // 屏幕亮度
        const val SUB_MENU_KEY_SIX_MIC_SOUND_PICKER = "sixMicSoundPicker" // 麦克拾音器
        const val SUB_MENU_KEY_KEYSTONE = "keystone"
        const val SUB_MENU_KEY_SCREEN = "screen"
        const val SUB_MENU_KEY_VOICE = "voice"
        const val SUB_MENU_KEY_SPEAKER_CHOOSE = "speakerChoose"
        const val SUB_MENU_KEY_TOUCH_PAD = "touchPad"
        const val SUB_MENU_KEY_BT_KEYBOARD = "btKeyboard"
        const val SUB_MENU_KEY_CEC = "cec"      // HDMI CEC

        const val SUB_MENU_KEY_LANG = "lang"
        const val SUB_MENU_KEY_TIME_ZONE = "timeZone"
        const val SUB_MENU_KEY_TIME = "time"
        const val SUB_MENU_KEY_BOOT_START = "bootStart"
        const val SUB_MENU_KEY_SYS_INFO = "systemInfo"
        const val SUB_MENU_KEY_UPDATE = "update"
        const val SUB_MENU_KEY_CLEAN_ROM = "cleanROM"
        const val SUB_MENU_KEY_CLEAN_ALL = "CleanAll"
        const val SUB_MENU_KEY_FEEDBACK = "feedback"

        // 个性设置
        const val SUB_MENU_KEY_LAUNCHER_LAYOUT = "launcherLayout"
        const val SUB_MENU_KEY_LAUNCHER_SCREEN_BACKDROP = "screenBackdrop"
        const val SUB_MENU_KEY_LAUNCHER_FAV_APPS = "launcherFavApps"
        const val SUB_MENU_KEY_WALLPAPER_SETTING = "wallpaperSetting"
        const val SUB_MENU_KEY_CUSTOM_SHORTCUT_KEYS = "customShortcutKeys"
        const val SUB_MENU_KEY_AI_VOICE = "aiVoice" // AI语音助手
    }

    val menuSheet = menuSheet {
        // 输出
        menuGroup {
            groupKey = MENU_GROUP_KEY_OUTPUT
            menuNameRes = R.string.output
            menuNameSubRes = R.string.output_sub
            iconRes = R.drawable.ic_group_menu_output

            subMenu {
                key = SUB_MENU_KEY_VOICE
                nameRes = R.string.voice
                menuFragment = VoiceFragment::class.java
            }

            subMenu {
                key = SUB_MENU_KEY_SPEAKER_CHOOSE
                nameRes = R.string.sub_menu_title_speaker_choose
                menuFragment = SpeakerChooseFragment::class.java
            }

            subMenu {
                key = SUB_MENU_SCREEN_BRIGHTNESS
                nameRes = R.string.sub_menu_title_screen_brightness
                menuFragment = ScreenBrightnessFragment::class.java
                visible = Constants.starryHWInfo.hasTouchScreen // 有触摸屏才显示屏幕亮度设置
            }

            subMenu {
                key = SUB_MENU_KEY_CEC
                nameRes = R.string.hdmi_cec
                nameSubRes = R.string.hdmi_cec_sub
                menuFragment = CECFragment::class.java
                visible = when (Constants.starryHWInfo.series) {
                    StudioSeries, Q2Series -> true
                    else -> false
                }
            }

        }

        // 投影仪
        menuGroup {
            groupKey = MENU_GROUP_KEY_PROJECTOR
            menuNameRes = R.string.projector
            iconRes = when (Constants.starryHWInfo.series) {
                Q2Series -> R.drawable.ic_group_menu_projector_q2
                else -> R.drawable.ic_group_menu_projector
            }
            visible = Constants.starryHWInfo.hasOpticalEngine   // 有光机才显示

            subMenu {
                key = SUB_MENU_KEY_FOCUS
                nameRes = R.string.focusing
                menuFragment = FocusFragment::class.java
                visible = when (Constants.starryHWInfo.series) {
                    StudioSeries -> false
                    else -> true
                }
            }

            subMenu {
                key = SUB_MENU_KEY_SCREEN
                nameRes = R.string.sub_menu_title_screen
                menuFragment = ScreenFragment::class.java
                visible = when (Constants.starryHWInfo.series) {
                    StudioSeries -> false
                    else -> true
                }
            }

            subMenu {
                key = SUB_MENU_KEY_KEYSTONE
                nameRes = R.string.keystone_correction
                menuFragment = KeystoneCorrectionFragment::class.java
                visible = when (Constants.starryHWInfo.series) {
                    StudioSeries -> false
                    else -> true
                }
            }

        }

        // 摄像头/拾音器
        menuGroup {
            groupKey = MENU_GROUP_KEY_CAMERA_MIC
            menuNameRes = R.string.camera_and_mic
            iconRes = R.drawable.ic_group_menu_camera_mic

            subMenu {
                key = SUB_MENU_KEY_MEETING_CAMERA
                nameRes = R.string.sub_menu_title_picture_scene
                menuFragment = MeetingCameraFragment::class.java
            }

            subMenu {
                key = SUB_MENU_KEY_MIC_CHOOSE
                nameRes = R.string.sub_menu_title_mic_choose
                menuFragment = MicChooseFragment::class.java
            }

            subMenu {
                key = SUB_MENU_KEY_SIX_MIC_SOUND_PICKER
                nameRes = R.string.sub_menu_title_noise_reduction
                menuFragment = SixMicSoundPickerFragment::class.java
            }


        }


        // 触控板/配件
        if (Constants.versionIndustry != VersionIndustry.DEVICE_INDUSTRY_ARMY_BUILD) {
            // 军队版本不显示触控板和蓝牙模块
            menuGroup {
                groupKey = MENU_GROUP_KEY_PERIPHERAL
                menuNameRes = R.string.touch_pad_and_peripheral
                iconRes = R.drawable.ic_group_menu_peripheral

                subMenu {
                    key = SUB_MENU_KEY_TOUCH_PAD
                    nameRes = R.string.touch_control
                    menuFragment = TouchPadFragment::class.java
                }

                subMenu {
                    key = SUB_MENU_KEY_BT_KEYBOARD
                    nameRes = R.string.blue_keyboard
                    menuFragment = BTKeyboardFragment::class.java
                }
            }
        }

        // 个性设置
        menuGroup {
            groupKey = MENU_GROUP_KEY_LAUNCHER
            menuNameRes = R.string.menu_group_launcher
            iconRes = R.drawable.ic_group_menu_launcher

            // 壁纸
            subMenu {
                key = SUB_MENU_KEY_LAUNCHER_SCREEN_BACKDROP
                nameRes = R.string.sub_menu_title_screen_backdrop
                menuFragment = ScreenBackdropFragment::class.java
            }

            // 开机自启动
            subMenu {
                key = SUB_MENU_KEY_BOOT_START
                nameRes = R.string.boot_start
                menuFragment = BootStartFragment::class.java
                visible = when (Constants.starryHWInfo.series.model) {
                    StarryModel.Q1Model.Q1,
                    StarryModel.Q1Model.Q1Pro,
                    StarryModel.Q1Model.Q1S,
                    StarryModel.Q2Model.Q2,
                    StarryModel.Q2Model.Q2Pro,
                    StarryModel.StudioModel.Studio,
                    StarryModel.StudioModel.StudioPro,
                    StarryModel.StudioModel.StudioS,
                    StarryModel.StudioModel.StudioSPro
                        -> false

                    else -> true
                }
            }

            // 常用应用
            subMenu {
                key = SUB_MENU_KEY_LAUNCHER_FAV_APPS
                nameRes = R.string.sub_menu_title_launcher_favorite_apps
                menuFragment = LauncherFavAppsFragment::class.java
            }

            // 休眠唤醒屏保
            subMenu {
                key = SUB_MENU_KEY_WALLPAPER_SETTING
                nameRes = R.string.sub_menu_title_wallpaper_setting
                menuFragment = WallpaperFragmentList::class.java
                visible = when (Constants.starryHWInfo.model) {
                    StarryModel.Q1Model.Q1,
                    StarryModel.Q1Model.Q1Pro,
                    StarryModel.StudioModel.Studio,
                    StarryModel.StudioModel.StudioPro,
                    StarryModel.StudioModel.StudioSPlus,
                    StarryModel.Q2Model.Q2,
                    StarryModel.Q2Model.Q2Pro -> false

                    else -> true
                }
            }

            // 触控板快捷键
            subMenu {
                key = SUB_MENU_KEY_CUSTOM_SHORTCUT_KEYS
                nameRes = R.string.sub_menu_title_custom_shortcut_keys
                menuFragment = CustomShortcutKeysFragment::class.java
                visible = when (Constants.starryHWInfo.series) {
                    StudioSeries -> true    // 只有Studio系列显示(Studio s plus 不显示)
                    else -> false
                }
            }

            subMenu {
                key = SUB_MENU_KEY_AI_VOICE
                nameRes = R.string.sub_menu_title_ai_assistant
                menuFragment = AIAssistantFragment::class.java
                visible = when (Constants.starryHWInfo.series) {
                    StudioSeries -> Constants.starryHWInfo.salesLocale == StarryDevLocale.Mainland
                    Q1Series -> false
                    Q2Series -> Constants.starryHWInfo.salesLocale == StarryDevLocale.Mainland
                }
            }

        }

        // 网络
        menuGroup {
            groupKey = MENU_GROUP_KEY_NET
            menuNameRes = R.string.network
            iconRes = R.drawable.ic_group_menu_net

            subMenu {
                key = SUB_MENU_KEY_WIFI
                nameRes = R.string.wifi_tab
                menuFragment = WifiFragment::class.java
                visible =
                    Constants.versionIndustry != VersionIndustry.DEVICE_INDUSTRY_ARMY_BUILD   // 军队版本不显示
            }

            subMenu {
                key = SUB_MENU_KEY_WIRED_NET
                nameRes = R.string.wired_net
                menuFragment = WiredNetworkFragment::class.java
            }

            subMenu {
                key = SUB_MENU_KEY_INTERNET_SPEED
                nameRes = R.string.internet_speed
                menuFragment = InternetSpeedFragment::class.java
            }
        }

        // 系统
        menuGroup {
            groupKey = MENU_GROUP_KEY_SYSTEM
            menuNameRes = R.string.system
            iconRes = R.drawable.ic_group_menu_system

            subMenu {
                key = SUB_MENU_KEY_LANG
                nameRes = R.string.sub_menu_title_lang
                menuFragment = LangFragment::class.java
                // 国内版本不显示语言设置(于洋:24.12.07)
                visible =
                    Constants.starryHWInfo.salesLocale == StarryDevLocale.Overseas || SettingUtil.SystemSetting.isEnableLanguageSwitch()
            }

            subMenu {
                key = SUB_MENU_KEY_TIME_ZONE
                nameRes = R.string.sub_menu_title_time_zone
                menuFragment = TimeZoneFragment::class.java
                visible =
                    Constants.versionIndustry != VersionIndustry.DEVICE_INDUSTRY_ARMY_BUILD   // 军队版本不显示时区设置
            }

            subMenu {
                key = SUB_MENU_KEY_TIME
                nameRes = R.string.sub_menu_title_time
                menuFragment = TimeFragment::class.java
                visible =
                    Constants.versionIndustry == VersionIndustry.DEVICE_INDUSTRY_ARMY_BUILD
            }

            subMenu {
                key = SUB_MENU_KEY_SYS_INFO
                nameRes = R.string.systeminfo
                menuFragment = SystemInfoFragment::class.java
            }

            subMenu {
                key = SUB_MENU_KEY_UPDATE
                nameRes = R.string.update
                menuFragment = UpdateVersionFragment::class.java
            }

            subMenu {
                key = SUB_MENU_KEY_CLEAN_ROM
                nameRes = R.string.clean_room
                menuFragment = CleanRoomFragment::class.java
            }

            subMenu {
                key = SUB_MENU_KEY_CLEAN_ALL
                nameRes = R.string.clear_data
                menuFragment = CleanAllFragment::class.java
            }

            subMenu {
                key = SUB_MENU_KEY_FEEDBACK
                nameRes = R.string.feedback
                menuFragment = FeedbackFragment::class.java
            }
        }
    }

    fun getSubMenuByKey(key: String): SubMenu? {
        menuSheet.forEach { menuGroup ->
            menuGroup.subMenus.forEach { subMenu ->
                if (subMenu.menuKey == key) {
                    return subMenu
                }
            }
        }
        return null
    }

    fun getSubMenuByName(name: String): SubMenu? {
        menuSheet.forEach { menuGroup ->
            menuGroup.subMenus.forEach { subMenu ->
                if (getString(subMenu.menuNameRes) == name) {
                    return subMenu
                }
            }
        }
        return null
    }


    private class SubMenuBuilder(val groupKey: String) {
        var key: String = ""       // 菜单Key
        var nameRes: Int = 0       // 菜单名称
        var nameSubRes: Int? = null
        var menuFragment: Class<out Fragment> = Fragment::class.java // 菜单Fragment
        var visible: Boolean = true // 是否显示

        fun build(): SubMenu {
            return SubMenu(key, nameRes, nameSubRes, groupKey, menuFragment)
        }
    }

    private class MenuGroupBuilder {
        val subMenus = mutableListOf<SubMenu>()
        var groupKey: String = ""       // 分组Key
        var menuNameRes: Int = 0        // 菜单名称
        var menuNameSubRes: Int? = null // 菜单副标题
        var iconRes: Int = 0            // 菜单图标
        var visible: Boolean = true     // 是否显示

        fun subMenu(block: SubMenuBuilder.() -> Unit) {
            val builder = SubMenuBuilder(groupKey)
            builder.block()
            if (builder.visible) {
                subMenus.add(builder.build())
            }
        }

        fun build(): MenuGroup {
            return MenuGroup(groupKey, menuNameRes, menuNameSubRes, iconRes, subMenus)
        }
    }

    private class MenuSheetBuilder {
        private val menuGroups = mutableListOf<MenuGroup>()

        fun menuGroup(block: MenuGroupBuilder.() -> Unit) {
            val builder = MenuGroupBuilder()
            builder.block()
            if (builder.visible) {
                menuGroups.add(builder.build())
            }
        }

        fun build(): List<MenuGroup> {
            return menuGroups
        }
    }

    private fun menuSheet(block: MenuSheetBuilder.() -> Unit): List<MenuGroup> {
        val builder = MenuSheetBuilder()
        builder.block()
        return builder.build()
    }
}

class MenuGroup(
    val groupKey: String,           // 分组Key
    val menuNameRes: Int,           // 菜单名称
    val menuNameSubRes: Int?,        // 菜单副标题
    val iconRes: Int,               // 菜单图标
    val subMenus: List<SubMenu>,    // 子菜单
)

data class SubMenu(
    val menuKey: String,
    val menuNameRes: Int,
    val menuNameSubRes: Int?,
    val groupKey: String,
    val menuFragment: Class<out Fragment>,
)




