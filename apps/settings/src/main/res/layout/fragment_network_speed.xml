<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <com.czur.starry.device.settings.widget.speed.SpeedBrokenLineView
        android:id="@+id/netSpeedView"
        android:layout_width="1100px"
        android:layout_height="500px"
        android:layout_marginTop="187px"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <ImageView
        android:id="@+id/downloadIv"
        android:layout_width="24px"
        android:layout_height="27px"
        android:layout_marginTop="65px"
        android:layout_marginRight="10px"
        android:src="@drawable/ic_arrow_download"
        app:layout_constraintHorizontal_chainStyle="packed"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toLeftOf="@id/downloadTv"
        app:layout_constraintTop_toBottomOf="@id/netSpeedView" />

    <TextView
        android:id="@+id/downloadTv"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginRight="77px"
        android:text="@string/download_speed"
        style="@style/TVSettingItemContentBlack"
        android:textStyle="bold"
        app:layout_constraintBottom_toBottomOf="@id/downloadIv"
        app:layout_constraintLeft_toRightOf="@id/downloadIv"
        app:layout_constraintRight_toLeftOf="@id/uploadIv"
        app:layout_constraintTop_toTopOf="@id/downloadIv" />

    <ImageView
        android:id="@+id/uploadIv"
        android:layout_width="24px"
        android:layout_height="27px"
        android:layout_marginRight="10px"
        android:src="@drawable/ic_arrow_upload"
        app:layout_constraintBottom_toBottomOf="@id/downloadIv"
        app:layout_constraintLeft_toRightOf="@id/downloadTv"
        app:layout_constraintRight_toLeftOf="@id/uploadTv"
        app:layout_constraintTop_toTopOf="@+id/downloadIv" />

    <TextView
        android:id="@+id/uploadTv"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/upload_speed"
        style="@style/TVSettingItemContentBlack"
        android:textStyle="bold"
        app:layout_constraintBottom_toBottomOf="@id/downloadIv"
        app:layout_constraintLeft_toRightOf="@id/uploadIv"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="@id/downloadIv" />

    <com.czur.uilib.btn.CZButton
        android:id="@+id/speedTryAgainBtn"
        android:layout_width="300px"
        android:layout_height="80px"
        android:text="@string/speed_try"
        android:textSize="30px"
        app:colorStyle="blueWhite"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        android:layout_marginBottom="50px"
        />


    <ProgressBar
        android:id="@+id/speedProgress"
        android:layout_width="100px"
        android:layout_height="100px"
        android:layout_gravity="center"
        app:layout_constraintBottom_toBottomOf="@id/netSpeedView"
        app:layout_constraintLeft_toLeftOf="@id/netSpeedView"
        app:layout_constraintRight_toRightOf="@id/netSpeedView"
        app:layout_constraintTop_toTopOf="@id/netSpeedView"
        android:indeterminateTint="@color/bg_main_blue"
        android:visibility="gone"/>


</androidx.constraintlayout.widget.ConstraintLayout>