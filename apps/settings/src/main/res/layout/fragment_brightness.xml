<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:background="@color/white"
    tools:ignore="PxUsage,RtlHardcoded">

    <TextView
        android:id="@+id/titleBrightnessTv"
        style="@style/TVContentTitle"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:gravity="center"
        android:includeFontPadding="false"
        android:text="@string/str_brightness_adjustment"
        app:layout_constraintBottom_toTopOf="@id/brightnessSeekBar"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintVertical_chainStyle="packed" />

    <com.czur.uilib.seek.CZSeekBar
        android:id="@+id/brightnessSeekBar"
        android:layout_width="640px"
        android:layout_height="wrap_content"
        android:layout_marginTop="55px"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@id/titleBrightnessTv"
        app:showActionBtn="false" />

    <ImageView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:src="@drawable/img_brightness_low"
        app:layout_constraintTop_toTopOf="@id/brightnessSeekBar"
        app:layout_constraintBottom_toBottomOf="@id/brightnessSeekBar"
        app:layout_constraintRight_toLeftOf="@id/brightnessSeekBar"
        android:layout_marginRight="35px"
        />

    <ImageView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:src="@drawable/img_brightness_high"
        app:layout_constraintTop_toTopOf="@id/brightnessSeekBar"
        app:layout_constraintBottom_toBottomOf="@id/brightnessSeekBar"
        app:layout_constraintLeft_toRightOf="@id/brightnessSeekBar"
        android:layout_marginLeft="35px"
        />



</androidx.constraintlayout.widget.ConstraintLayout>