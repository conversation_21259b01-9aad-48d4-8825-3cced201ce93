<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:background="@color/bg_main"
    tools:ignore="PxUsage">

    <TextView
        android:id="@+id/titleCallTv"
        style="@style/TVContentTitle"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:gravity="center"
        android:includeFontPadding="false"
        android:text="@string/volume_title_call"
        app:layout_constraintBottom_toTopOf="@id/callValueTv"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintVertical_chainStyle="packed" />

    <TextView
        android:id="@+id/callValueTv"
        style="@style/TVContentTitle"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="35px"
        android:includeFontPadding="false"
        app:layout_constraintBottom_toTopOf="@id/callSeekBar"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@id/titleCallTv"
        tools:text="8" />

    <com.czur.uilib.seek.CZSeekBar
        android:id="@+id/callSeekBar"
        android:layout_width="810px"
        android:layout_height="wrap_content"
        app:layout_constraintBottom_toTopOf="@id/titleMediaTv"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@id/callValueTv" />


    <TextView
        android:id="@+id/titleMediaTv"
        style="@style/TVContentTitle"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="150px"
        android:gravity="center"
        android:includeFontPadding="false"
        android:text="@string/volume_title_media"
        app:layout_constraintBottom_toTopOf="@id/mediaValueTv"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@id/callSeekBar" />

    <TextView
        android:id="@+id/mediaValueTv"
        style="@style/TVContentTitle"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="35px"
        android:includeFontPadding="false"
        app:layout_constraintBottom_toTopOf="@id/mediaSeekBar"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/titleMediaTv"
        tools:text="8" />


    <com.czur.uilib.seek.CZSeekBar
        android:id="@+id/mediaSeekBar"
        android:layout_width="810px"
        android:layout_height="wrap_content"
        app:layout_constraintBottom_toTopOf="@id/setVolumeDefBtn"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@id/mediaValueTv" />


    <com.czur.uilib.btn.CZButton
        android:id="@+id/setVolumeDefBtn"
        android:layout_width="300px"
        android:layout_height="80px"
        android:layout_marginBottom="50px"
        android:text="@string/common_reset_to_def"
        android:textSize="30px"
        app:colorStyle="blueWhite"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent" />

</androidx.constraintlayout.widget.ConstraintLayout>