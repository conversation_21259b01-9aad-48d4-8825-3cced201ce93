<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:background="@color/bg_main"
    tools:ignore="PxUsage,RtlHardcoded">

    <com.czur.starry.device.settings.widget.SettingItemBg
        android:id="@+id/autoKeystoneBg"
        android:layout_width="900px"
        android:layout_height="80px"
        app:ignoreHover="true"
        app:layout_constraintBottom_toTopOf="@id/manualKeystoneBg"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintVertical_chainStyle="packed"
        app:position="top" />

    <TextView
        style="@style/TVSettingItemTitle"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginLeft="30px"
        android:text="@string/keystone_correction_automatic"
        app:layout_constraintBottom_toBottomOf="@id/autoKeystoneBg"
        app:layout_constraintLeft_toLeftOf="@id/autoKeystoneBg"
        app:layout_constraintTop_toTopOf="@id/autoKeystoneBg" />

    <com.czur.uilib.CZSwitch
        android:id="@+id/autoKeystoneSwitch"
        android:layout_width="94px"
        android:layout_height="45px"
        android:layout_marginRight="30px"
        app:layout_constraintBottom_toBottomOf="@id/autoKeystoneBg"
        app:layout_constraintRight_toRightOf="@id/autoKeystoneBg"
        app:layout_constraintTop_toTopOf="@id/autoKeystoneBg" />

    <com.czur.starry.device.settings.widget.SettingItemBg
        android:id="@+id/manualKeystoneBg"
        android:layout_width="900px"
        android:layout_height="80px"
        app:ignoreHover="true"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@id/autoKeystoneBg"
        app:position="bottom"/>

    <TextView
        style="@style/TVSettingItemTitle"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginLeft="30px"
        android:text="@string/keystone_correction_manual"
        app:layout_constraintBottom_toBottomOf="@id/manualKeystoneBg"
        app:layout_constraintLeft_toLeftOf="@id/manualKeystoneBg"
        app:layout_constraintTop_toTopOf="@id/manualKeystoneBg" />

    <ImageView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginRight="30px"
        android:src="@drawable/ic_setting_item_next"
        app:layout_constraintBottom_toBottomOf="@id/manualKeystoneBg"
        app:layout_constraintRight_toRightOf="@id/manualKeystoneBg"
        app:layout_constraintTop_toTopOf="@id/manualKeystoneBg" />


</androidx.constraintlayout.widget.ConstraintLayout>