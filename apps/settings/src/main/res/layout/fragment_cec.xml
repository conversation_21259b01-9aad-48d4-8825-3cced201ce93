<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:background="@color/white"
    tools:ignore="PxUsage,RtlHardcoded">

    <androidx.constraintlayout.helper.widget.Flow
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        app:constraint_referenced_ids="openScreenByDeviceBg,closeScreenByDeviceBg,openDeviceByScreenBg,closeDeviceByScreenBg"
        app:flow_verticalGap="40px"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <com.czur.uilib.bg.CZItemBgView
        android:id="@+id/openScreenByDeviceBg"
        android:layout_width="900px"
        android:layout_height="80px"
        app:czIgnoreHover="true" />

    <TextView
        style="@style/tv_cec_item_label"
        android:text="@string/cec_open_screen_by_device"
        app:layout_constraintBottom_toBottomOf="@id/openScreenByDeviceBg"
        app:layout_constraintLeft_toLeftOf="@id/openScreenByDeviceBg"
        app:layout_constraintTop_toTopOf="@id/openScreenByDeviceBg" />

    <com.czur.uilib.CZSwitch
        android:id="@+id/openScreenByDeviceSwitch"
        style="@style/switch_cec_item"
        app:layout_constraintBottom_toBottomOf="@id/openScreenByDeviceBg"
        app:layout_constraintRight_toRightOf="@id/openScreenByDeviceBg"
        app:layout_constraintTop_toTopOf="@id/openScreenByDeviceBg" />

    <com.czur.uilib.bg.CZItemBgView
        android:id="@+id/closeScreenByDeviceBg"
        android:layout_width="900px"
        android:layout_height="80px"
        app:czIgnoreHover="true" />

    <TextView
        style="@style/tv_cec_item_label"
        android:text="@string/cec_close_screen_by_device"
        app:layout_constraintBottom_toBottomOf="@id/closeScreenByDeviceBg"
        app:layout_constraintLeft_toLeftOf="@id/closeScreenByDeviceBg"
        app:layout_constraintTop_toTopOf="@id/closeScreenByDeviceBg" />

    <com.czur.uilib.CZSwitch
        android:id="@+id/closeScreenByDeviceSwitch"
        style="@style/switch_cec_item"
        app:layout_constraintBottom_toBottomOf="@id/closeScreenByDeviceBg"
        app:layout_constraintRight_toRightOf="@id/closeScreenByDeviceBg"
        app:layout_constraintTop_toTopOf="@id/closeScreenByDeviceBg" />

    <com.czur.uilib.bg.CZItemBgView
        android:id="@+id/openDeviceByScreenBg"
        android:layout_width="900px"
        android:layout_height="80px"
        app:czIgnoreHover="true" />

    <TextView
        style="@style/tv_cec_item_label"
        android:text="@string/cec_open_device_by_screen"
        app:layout_constraintBottom_toBottomOf="@id/openDeviceByScreenBg"
        app:layout_constraintLeft_toLeftOf="@id/openDeviceByScreenBg"
        app:layout_constraintTop_toTopOf="@id/openDeviceByScreenBg" />

    <com.czur.uilib.CZSwitch
        android:id="@+id/openDeviceByScreenSwitch"
        style="@style/switch_cec_item"
        app:layout_constraintBottom_toBottomOf="@id/openDeviceByScreenBg"
        app:layout_constraintRight_toRightOf="@id/openDeviceByScreenBg"
        app:layout_constraintTop_toTopOf="@id/openDeviceByScreenBg" />

    <com.czur.uilib.bg.CZItemBgView
        android:id="@+id/closeDeviceByScreenBg"
        android:layout_width="900px"
        android:layout_height="80px"
        app:czIgnoreHover="true" />

    <TextView
        style="@style/tv_cec_item_label"
        android:text="@string/cec_close_device_by_screen"
        app:layout_constraintBottom_toBottomOf="@id/closeDeviceByScreenBg"
        app:layout_constraintLeft_toLeftOf="@id/closeDeviceByScreenBg"
        app:layout_constraintTop_toTopOf="@id/closeDeviceByScreenBg" />

    <com.czur.uilib.CZSwitch
        android:id="@+id/closeDeviceByScreenSwitch"
        style="@style/switch_cec_item"
        app:layout_constraintBottom_toBottomOf="@id/closeDeviceByScreenBg"
        app:layout_constraintRight_toRightOf="@id/closeDeviceByScreenBg"
        app:layout_constraintTop_toTopOf="@id/closeDeviceByScreenBg" />
</androidx.constraintlayout.widget.ConstraintLayout>