<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:ignore="PxUsage">

    <com.czur.uilib.btn.CZBackBtn
        android:id="@+id/joinBackBtn"
        android:layout_width="90px"
        android:layout_height="60px"
        android:layout_margin="30px"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/joinSSIDTv"
        style="@style/TVSettingItemTitle"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:gravity="center_horizontal"
        android:textSize="30px"
        app:layout_constraintBottom_toTopOf="@id/joinPwdEt"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintVertical_chainStyle="packed"
        tools:text="TP-LINK" />

    <EditText
        android:id="@+id/joinPwdEt"
        android:layout_width="700px"
        android:layout_height="80px"
        android:layout_marginTop="30px"
        android:imeOptions="actionDone"
        android:inputType="textPassword"
        android:nextFocusDown="@id/joinPwdEt"
        android:singleLine="true"
        style="@style/CZCommonEditText"
        app:layout_constraintBottom_toTopOf="@id/joinConfirmBtn"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/joinSSIDTv" />


    <com.czur.starry.device.baselib.widget.EyeView
        android:id="@+id/hidePwdIv"
        android:layout_width="40px"
        android:layout_height="24px"
        android:layout_marginLeft="30px"
        android:tint="@color/bg_main_blue"
        app:layout_constraintBottom_toBottomOf="@+id/joinPwdEt"
        app:layout_constraintLeft_toRightOf="@+id/joinPwdEt"
        app:layout_constraintTop_toTopOf="@+id/joinPwdEt" />


    <com.czur.uilib.btn.CZButton
        android:id="@+id/joinConfirmBtn"
        android:layout_width="700px"
        android:layout_height="80px"
        android:layout_marginTop="30px"
        android:text="@string/join_wifi_confirm"
        android:textSize="30px"
        app:colorStyle="blueWhite"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="@id/joinPwdEt"
        app:layout_constraintRight_toRightOf="@id/joinPwdEt"
        app:layout_constraintTop_toBottomOf="@id/joinPwdEt" />
</androidx.constraintlayout.widget.ConstraintLayout>