<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="80px"
    xmlns:tools="http://schemas.android.com/tools"
    tools:viewBindingIgnore="true">

    <com.czur.starry.device.settings.widget.SettingItemBg
        android:id="@+id/feedbackCategoryBg"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        app:position="middle"
        app:syncSelectColorIDs="feedbackCategoryNameTv,feedbackCategoryNextIv" />

    <TextView
        android:id="@+id/feedbackCategoryNameTv"
        style="@style/TVSettingItemTitle"
        android:layout_width="0px"
        android:layout_height="wrap_content"
        android:layout_marginLeft="30px"
        android:ellipsize="end"
        android:maxLines="1"
        android:layout_marginRight="10px"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toLeftOf="@+id/feedbackCategoryNextIv"
        app:layout_constraintTop_toTopOf="parent"
        app:float_tips_theme="lightBlue"
        app:float_tips=" "/>

    <ImageView
        android:id="@+id/feedbackCategoryNextIv"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginRight="30px"
        android:src="@drawable/ic_setting_item_next_small"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

</androidx.constraintlayout.widget.ConstraintLayout>