<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    app:bl_corners_radius="10px"
    app:bl_solid_color="#0D000000"
    tools:ignore="PxUsage,RtlHardcoded,RtlSymmetry">

    <com.czur.starry.device.settings.widget.SettingItemBg
        android:layout_width="match_parent"
        android:layout_height="0px"
        app:ignoreHover="true"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <Space
        android:id="@+id/topSpace"
        android:layout_width="match_parent"
        android:layout_height="80px"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/tv_switch"
        style="@style/TVSettingItemTitle"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:paddingLeft="30px"
        android:text="@string/wifi_switch"
        app:layout_constraintBottom_toBottomOf="@id/topSpace"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <com.czur.uilib.CZSwitch
        android:id="@+id/wifiEnableSwitch"
        android:layout_width="94px"
        android:layout_height="44px"
        android:layout_marginRight="30px"
        app:layout_constraintBottom_toBottomOf="@id/tv_switch"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="@+id/tv_switch" />

    <com.czur.starry.device.settings.widget.SettingItemBg
        android:id="@+id/connectedItemBg"
        android:layout_width="match_parent"
        android:layout_height="0px"
        app:ignoreHover="true"
        app:layout_constraintBottom_toBottomOf="@id/connecteditem"
        app:layout_constraintTop_toTopOf="@id/connecteditem"
        app:position="bottom"
        app:selected="true" />

    <com.czur.starry.device.baselib.widget.WifiItemView
        android:id="@+id/connecteditem"
        android:layout_width="match_parent"
        android:layout_height="80px"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/topSpace"
        app:ssid_single_line="true" />

</androidx.constraintlayout.widget.ConstraintLayout>