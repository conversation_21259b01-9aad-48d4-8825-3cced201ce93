<?xml version="1.0" encoding="utf-8"?>
<com.czur.starry.device.baselib.widget.BlockEventFrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/floatOutSide"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:clickable="true"
    android:clipChildren="false"
    android:clipToPadding="false"
    android:focusable="false">

    <FrameLayout
        android:id="@+id/floatingBgFrameLayout"
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <View
            android:id="@+id/floatingDarkBgView"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:background="@color/float_out_color_dark"
            android:clickable="false"
            android:focusable="false"
            android:visibility="gone" />

        <ImageView
            android:id="@+id/floatImgBgView"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:clickable="false"
            android:focusable="false"
            android:visibility="gone" />

        <View
            android:id="@+id/floatingDarkFrontView"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:background="@color/float_out_color_img_dark_front"
            android:clickable="false"
            android:focusable="false"
            android:visibility="gone" />
    </FrameLayout>
</com.czur.starry.device.baselib.widget.BlockEventFrameLayout>