<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="wrap_content"
    android:layout_height="60px"
    android:orientation="vertical"
    tools:ignore="PxUsage">

    <ImageView
        android:id="@+id/notifyBarMsgIv"
        android:layout_width="30px"
        android:layout_height="60px"
        android:paddingVertical="19px"
        android:src="@drawable/baselib_icon_msg"
        app:float_tips="@string/float_tip_notice_msg"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <com.czur.starry.device.baselib.widget.CircleView
        android:id="@+id/notifyBarUnReadPoint"
        android:layout_width="8px"
        android:layout_height="8px"
        android:layout_marginTop="15px"
        android:layout_marginRight="-2px"
        android:visibility="gone"
        app:circleColor="@color/notice_read"
        app:layout_constraintRight_toRightOf="@id/notifyBarMsgIv"
        app:layout_constraintTop_toTopOf="@id/notifyBarMsgIv" />

    <com.czur.starry.device.baselib.widget.NetStatusIcon
        android:id="@+id/notifyBarWifiIv"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:src="@drawable/baselib_icon_wifi"
        app:float_tips="@string/float_tip_network_info"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <androidx.constraintlayout.helper.widget.Layer
        android:id="@+id/notifyBarUserLayer"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:paddingVertical="5px"
        app:constraint_referenced_ids="notifyBarUserIv,notifyBarUserTv" />

    <ImageView
        android:id="@+id/notifyBarUserIv"
        android:layout_width="20px"
        android:layout_height="60px"
        android:paddingVertical="19px"
        android:src="@drawable/baselib_icon_user"
        app:float_tips="@string/float_tip_personal_center"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/notifyBarUserTv"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="0000001"
        android:textColor="@color/white"
        android:textSize="24px"
        android:includeFontPadding="false"
        app:float_tips="@string/float_tip_personal_center"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <androidx.constraintlayout.helper.widget.Flow
        android:id="@+id/userFlow"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:constraint_referenced_ids="notifyBarUserIv,notifyBarUserTv"
        app:flow_horizontalGap="10px"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <androidx.constraintlayout.helper.widget.Flow
        android:layout_width="wrap_content"
        android:layout_height="match_parent"
        android:layout_marginRight="60px"
        android:orientation="horizontal"
        app:constraint_referenced_ids="notifyBarMsgIv,notifyBarWifiIv,userFlow"
        app:flow_horizontalGap="30px"
        app:flow_horizontalStyle="packed"
        app:layout_constraintRight_toRightOf="parent" />

</androidx.constraintlayout.widget.ConstraintLayout>