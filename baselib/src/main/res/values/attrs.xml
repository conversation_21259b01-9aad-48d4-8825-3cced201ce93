<?xml version="1.0" encoding="utf-8"?>
<resources>
    <declare-styleable name="BaseLib_WordItem">
        <attr name="baselib_bg_color" format="color" />
        <attr name="baselib_tv_color" format="color" />
    </declare-styleable>

    <declare-styleable name="CommonButton">
        <attr name="baselib_theme">
            <enum name="dark" value="1" />
            <enum name="light" value="2" />
            <enum name="blue" value="3" />
            <enum name="white" value="4" />
            <enum name="red" value="5" />
            <enum name="white2" value="6" />
            <enum name="dark2" value="7" />
            <enum name="dark3" value="8" />
            <enum name="blue2" value="9" />
            <enum name="grey" value="10" />
        </attr>
    </declare-styleable>


    <declare-styleable name="BaseLib_TitleBar">
        <attr name="baselib_titlebar_title" format="string" />
        <attr name="baselib_titlebar_color" format="color" />
        <attr name="baselib_titlebar_top_distance" format="dimension" />
    </declare-styleable>

    <declare-styleable name="BaseLib_WifiItemView">
        <attr name="show_mode">
            <enum name="normal" value="0" />
            <enum name="setting" value="1" />
        </attr>
        <attr name="ssid_single_line" format="boolean" />
    </declare-styleable>

    <declare-styleable name="CircleView">
        <attr name="circleColor" format="color|reference" />
        <attr name="breathEffect" format="boolean" />
    </declare-styleable>

    <!--  悬浮提示  -->
    <declare-styleable name="FloatTips">
        <attr name="float_tips" format="string|reference" />
        <attr name="float_tips_theme">
            <enum name="white" value="0" />
            <enum name="blue" value="1" />
            <enum name="lightBlue" value="2" />
        </attr>
    </declare-styleable>

    <declare-styleable name="EditTextView">
        <attr name="hint" format="string|reference" />
        <attr name="showTextColor" format="color" />
        <attr name="editSolidColor" format="color|reference" />
    </declare-styleable>

    <declare-styleable name="SeekBarTouch">
        <attr name="targetSeekBar" format="reference" />
    </declare-styleable>

    <declare-styleable name="CustomProgressBar">
        <attr name="bg_color" format="color" />
        <attr name="progress_color" format="color" />
        <attr name="max" format="integer" />
        <attr name="progress" format="integer" />
    </declare-styleable>

    <declare-styleable name="SwitchIcon">
        <attr name="offBgColor" format="color" />
        <attr name="onBgColor" format="color" />
    </declare-styleable>
</resources>