package com.czur.starry.device.baselib.network.core.interceptor

import com.czur.czurutils.log.logTagV
import com.czur.czurutils.log.logTagW
import com.czur.starry.device.baselib.common.Constants
import com.czur.starry.device.baselib.common.Constants.DEVICE_APP_ID
import com.czur.starry.device.baselib.data.provider.UserHandler
import com.czur.starry.device.baselib.network.core.util.MiaoHttpUtils
import com.czur.starry.device.baselib.network.core.util.addParams
import com.czur.starry.device.baselib.utils.doWithoutCatch
import okhttp3.Interceptor
import okhttp3.Response

/**
 * Created by 陈丰尧 on 2021/7/14
 * 为所有请求添加通用参数 和 请求头
 */
class CommonParamInterceptor : Interceptor {
    companion object {
        private const val TAG = "CommonParamInterceptor"

        private const val HEADER_KEY_UDID = "UDID"
        private const val HEADER_KEY_APP_KEY = "App-Key"
        private const val HEADER_KEY_UID = "U-ID"
        private const val HEADER_KEY_TOKEN = "T-ID"
        private const val HEADER_KEY_LOCALE = "locale"
        private const val HEADER_KEY_REQ_TIME = "Request-Timestamp"

        private const val PARAM_KEY_UDID = "udid"
        private const val PARAM_KEY_RANDOM = "noncestr"
        private const val PARAM_KEY_TIME_STAMP = "timestamp"

        private const val APP_KEY_DEF = DEVICE_APP_ID

        private const val PARAM_KEY_COUNTRY_CODE = "X-COUNTRY-CODE"
    }


    override fun intercept(chain: Interceptor.Chain): Response {
        val req = chain.request()
        val builder = req.newBuilder()

        // 添加必要的头信息
        // SN
        builder.addHeader(HEADER_KEY_UDID, Constants.SERIAL)// SN号
        // APP KEY
        if (req.header(HEADER_KEY_APP_KEY) == null) {
            // 没有指定App key,则添加默认的AppKey
            builder.addHeader(HEADER_KEY_APP_KEY, APP_KEY_DEF)
        }
        //countryCode
        if (UserHandler.countryCode != "") {
            builder.addHeader(PARAM_KEY_COUNTRY_CODE, UserHandler.countryCode)
        }

        // 设备账号信息
        doWithoutCatch(TAG, "无法获取用户账号") {
            val accountNo = UserHandler.accountNo
            if (accountNo.isNotEmpty()) {
                builder.addHeader(HEADER_KEY_UID, accountNo)
            }
        }
        // Token
        doWithoutCatch(TAG, "无法获取Token") {
            val token = UserHandler.token
            if (token.isNotBlank()) {
                builder.addHeader(HEADER_KEY_TOKEN, token)
            } else {
                logTagV(TAG, "Token为空")
            }
        }

        // 请求事件
        builder.addHeader(HEADER_KEY_REQ_TIME, System.currentTimeMillis().toString())


        val headerReq = builder.build()

        // 添加固定参数
        val newReq = headerReq.addParams(
            PARAM_KEY_UDID to Constants.SERIAL,
            PARAM_KEY_RANDOM to MiaoHttpUtils.makeRandomStr16(),
            PARAM_KEY_TIME_STAMP to MiaoHttpUtils.mkTimeStamp(),
        )

        return chain.proceed(newReq)
    }
}