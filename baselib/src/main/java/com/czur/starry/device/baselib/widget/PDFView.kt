package com.czur.starry.device.baselib.widget

import android.animation.Animator
import android.animation.AnimatorListenerAdapter
import android.animation.ValueAnimator
import android.content.Context
import android.graphics.Bitmap
import android.graphics.BitmapFactory
import android.graphics.Canvas
import android.graphics.Color
import android.graphics.Paint
import android.graphics.PointF
import android.graphics.RectF
import android.graphics.pdf.PdfRenderer
import android.os.Handler
import android.os.Message
import android.os.ParcelFileDescriptor
import android.util.AttributeSet
import android.util.LruCache
import android.util.Range
import android.view.GestureDetector
import android.view.MotionEvent
import android.view.View
import android.view.animation.DecelerateInterpolator
import androidx.lifecycle.lifecycleScope
import com.czur.czurutils.log.logTagD
import com.czur.czurutils.log.logTagI
import com.czur.czurutils.log.logTagV
import com.czur.starry.device.baselib.utils.PDFUtil
import com.czur.starry.device.baselib.utils.PDFUtil.SCROLL_THRESHOLD
import com.czur.starry.device.baselib.utils.PDFUtil.dp2px
import com.czur.starry.device.baselib.utils.PDFUtil.getCachedKey
import com.czur.starry.device.baselib.utils.launch
import com.czur.starry.device.baselib.utils.requireLifecycleOwner
import com.czur.starry.device.baselib.utils.throttleFirst
import com.czur.starry.device.baselib.view.dialog.LoadingDialog
import com.jakewharton.disklrucache.DiskLruCache
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.flowOn
import kotlinx.coroutines.isActive
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import java.io.File
import java.io.FileInputStream
import java.lang.ref.WeakReference
import java.util.concurrent.Executors
import java.util.concurrent.Future
import java.util.concurrent.LinkedBlockingQueue
import java.util.concurrent.atomic.AtomicBoolean
import kotlin.math.abs
import kotlin.math.max
import kotlin.math.min

/**
 * created by wangh 22.0714
 */


class PDFView @JvmOverloads constructor(
    context: Context, attrs: AttributeSet? = null, defStyleAttr: Int = 0
) : View(context, attrs, defStyleAttr) {

    private val TAG: String =
        PDFView::class.java.simpleName


    companion object {
        //线程池，异步处理pdf转换bitmap
        private val EXECUTOR_SERVICE = Executors.newFixedThreadPool(3)
    }

    /**
     * 触摸状态，单指触摸、空闲状态
     */
    private enum class TouchState {
        SINGLE_POINTER,
        IDLE
    }

    // pdf渲染器
    private var mPdfRenderer: PdfRenderer? = null
    private var parcelFileDescriptor: ParcelFileDescriptor? = null

    //pdf文件路径
    private var mPdfName: String? = null
    private var pdfFileSize: Long = 0

    // pdf占位用的矩形
    private val mPagePlaceHolders = mutableListOf<PDFUtil.PageRect>()

    //正在加载的pdf页
    private val mLoadingPages = mutableListOf<PDFUtil.DrawingPage>()

    //正在缩放显示的pdf页
    private var mScalingPages = mutableListOf<PDFUtil.DrawingPage>()

    // pdf总宽度
    private var mPdfTotalWidth: Float = 0f

    // pdf总高度
    private var mPdfTotalHeight: Float = 0f

    // pdfView 每屏高度
    private var mPdfScreenHeight: Float = 1000f

    //滚动一次高度
    private val DEF_SCROLL_HEIGHT = -50

    //翻页一次高度
    private val DEF_PAGE_HEIGHT by lazy { -getPageTurnHeight() }

    //滚动时上一次Y坐标
    private var lastYpoint = -500f

    //快速滑动最大值
    private var MaxScroll = -1500f

    //处理滑动的手势识别器
    private val mGestureDetector by lazy {
        GestureDetector(
            context,
            OnPDFGestureListener(this)
        )
    }

    //处理pdf转换bitmap的handler
    private val mPDFHandler: PDFHandler

    //画笔
    private val mPDFPaint: Paint
    private val mDividerPaint: Paint

    //分隔线宽度（垂直分隔线）
    private val mDividerHeight = dp2px(context, 8).toFloat()

    //左右分割线
    private var mDividerBothside = dp2px(context, 35).toFloat()

    //默认word 竖屏标记
    private var isHorizontal = false

    //最后页下留白
    private val LAST_PAGE_DIVIDER = 200

    //上一次滚动时间
    private var lastScrollTime = 0L

    //缩放相关
    private var mCanvasScale: Float = 1f //画布的缩放倍数
    private var mCanvasTranslate: PointF = PointF() //滑动+缩放产生的画布平移
    private val MAX_ZOOM_IN = 3f//最大缩放倍
    private val MIN_ZOOM_OUT = 0.6f //最小缩放倍数
    private val ZOOM_STEP = 0.2f//每次缩放倍数

    //记录上一次X轴位移
    private var lastTransX = 0f

    //记录上一次缩放倍数
    private var lastScale = 1f

    //滑动相关
    private var mFlingAnim: ValueAnimator? = null

    //当前的触摸状态
    private var mTouchState = TouchState.IDLE

    //当前正在显示的页的索引
    private var mCurrentPageIndex = 0

    //当前是否是预览小窗
    private val SMALL_WINDOW_WIDTH = 1300

    //记录线程池中最新添加的任务
    private var mCreateLoadingPagesFuture: Future<*>? = null
    private var mCreateScalingPagesFuture: Future<*>? = null
    private var mInitPageFramesFuture: Future<*>? = null

    //page转换bitmap的缓存
    private var mOffscreenPageLimit = 1 //当前可见页的上下两边各缓存多少页，类似viewpager的属性
    private val mLoadingPagesBitmapMemoryCache: LruCache<String, Bitmap>//内存缓存
    private val mLoadingPagesBitmapDiskCache: DiskLruCache//磁盘缓存

    //存储bitmap
    private val linkedBlockingQueue = LinkedBlockingQueue<Pair<String, Bitmap>>()

    //获取bitmap线程正在运行
    var isRunning = AtomicBoolean(false)
    var onePageisRunning = AtomicBoolean(false)

    //鼠标滑动
    private val pageFlow = MutableSharedFlow<Boolean>()

    //滑动时的页面变动监听
    private var mOnPageChangedListener: OnPageChangedListener? = null
    private val loadingDialog by lazy { LoadingDialog() }
    var toast: PDFUtil.PDFToast? = null
    var isPreviewMode = false

    private val scope by lazy {
        requireLifecycleOwner().lifecycleScope
    }

    init {
        scope.launch {
            checkAndClearLastCache()
        }

        //主线程初始化Handler
        mPDFHandler = PDFHandler(this)

        //初始化缓存
        mLoadingPagesBitmapMemoryCache = LruCache<String, Bitmap>(mOffscreenPageLimit * 2 + 2)

        val diskCacheDir = File(context.cacheDir, "bitmap").apply {
            if (!this.exists()) {
                mkdirs()
            }
        }
        mLoadingPagesBitmapDiskCache = DiskLruCache.open(diskCacheDir, 1, 1, 1024 * 1024 * 20)


        //初始化pdf画笔
        mPDFPaint = Paint()
        mPDFPaint.color = Color.WHITE
        mPDFPaint.isAntiAlias = true
        mPDFPaint.isFilterBitmap = true
        mPDFPaint.isDither = true

        //初始化分割线画笔
        mDividerPaint = Paint()
        mDividerPaint.color = Color.parseColor("#F3F3F3")
        mDividerPaint.isAntiAlias = true

        requireLifecycleOwner().launch {
            pageFlow
                .throttleFirst(5)
                .flowOn(Dispatchers.IO)
                .collect { orien ->
                    logTagV(TAG, "接收数据:${orien}")
                    updateScrollHeight(orien)
                }
        }

        requireLifecycleOwner().launch(Dispatchers.IO) {
            while (isActive) {
                val pair = linkedBlockingQueue.take()
                //缓存到本地
                putLoadingPagesBitmapToDisk(pair.first, pair.second)
            }
        }
    }

    private suspend fun checkAndClearLastCache() = withContext(Dispatchers.IO) {
        val lastDiskCache = DiskLruCache.open(context.cacheDir, 1, 1, 1024 * 1024 * 50)
        if (lastDiskCache.size() > 0) {
            logTagI(TAG, "clear last pdf cache file")
            lastDiskCache.delete()
            lastDiskCache.close()
        }
    }

    /**
     * 打开本地pdf文件
     */
    fun showPdfFromPath(filePath: String, isPreview: Boolean = false) {
        try {
            mCurrentPageIndex = 0
            mCanvasScale = 1f
            mCanvasTranslate.set(0f, 0f)

            isPreviewMode = isPreview
            val file = File(filePath)
            parcelFileDescriptor =
                ParcelFileDescriptor.open(file, ParcelFileDescriptor.MODE_READ_ONLY)
            closePdfRenderer()
            mPdfRenderer = PdfRenderer(parcelFileDescriptor!!)
            setDividerOrienta(mPdfRenderer!!.openPage(0))
            mPdfName = file.name
            pdfFileSize = file.length()
            mInitPageFramesFuture = null
            invalidate()
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    fun closePdfRender() {
        mLoadingPagesBitmapMemoryCache?.evictAll()
        mLoadingPagesBitmapDiskCache?.close()
        closePdfRenderer()
        parcelFileDescriptor?.close()
    }

    //根据宽高设置边距
    private fun setDividerOrienta(page: PdfRenderer.Page) {

        if (page.width >= page.height) {
            isHorizontal = true
            //横屏模式
            mDividerBothside = dp2px(context, 35).toFloat()
            if (isPreviewMode) mDividerBothside = 1f
        } else {
            //竖屏模式
            mDividerBothside = dp2px(context, 150).toFloat()
            if (isPreviewMode) {
                mDividerBothside = 1f
                mPdfScreenHeight = 720f
            }
        }


        page.close()
    }


    /**
     * 设置当前页索引改变回调
     */
    fun setOnPageChangedListener(listener: OnPageChangedListener) {
        this.mOnPageChangedListener = listener
    }


    override fun onMeasure(widthMeasureSpec: Int, heightMeasureSpec: Int) {
        val parentWidth = MeasureSpec.getSize(widthMeasureSpec)
        val parentHeight = MeasureSpec.getSize(heightMeasureSpec)

        var width = parentWidth
        var height = parentHeight
        width = width.coerceAtLeast(suggestedMinimumWidth)
        height = height.coerceAtLeast(suggestedMinimumHeight)
        setMeasuredDimension(width, height)


        //初始化pdf页框架
        if (mInitPageFramesFuture == null) {
            //开始loading
            loadingDialog.show()
            mInitPageFramesFuture = EXECUTOR_SERVICE.submit(
                InitPdfFramesTask(this)
            )

        }
    }

    override fun onDraw(canvas: Canvas) {
        super.onDraw(canvas)
        //数据还没准备好，直接返回
        if (mPagePlaceHolders.isEmpty()) return

        //保存画布初始状态
        canvas.save()
        //平移缩放
        preDraw(canvas)
        //画占位图和分隔线
        drawPlaceHolderAndDivider(canvas)
        //画将要显示的完整page
        drawLoadingPages(canvas)
        //画边界分隔线
        drawEdgeDivider(canvas)

        //平移缩放
        preDraw(canvas)
    }

    /**
     * 处理画布的平移缩放
     */
    private fun preDraw(canvas: Canvas) {
        canvas.translate(mCanvasTranslate.x, mCanvasTranslate.y)
        canvas.scale(mCanvasScale, mCanvasScale)
    }

    /**
     * 画page占位图和横向分隔线
     */
    private fun drawPlaceHolderAndDivider(canvas: Canvas) {
        mPagePlaceHolders.forEachIndexed { index, pageRect ->
            val fillWidthRect = pageRect.fillWidthRect
            //画占位页
            canvas.drawRect(fillWidthRect, mPDFPaint)
            //画页分隔
            if (index < mPagePlaceHolders.lastIndex)
                canvas.drawRect(
                    paddingLeft.toFloat(),
                    fillWidthRect.bottom,
                    measuredWidth - paddingRight.toFloat(),
                    fillWidthRect.bottom + mDividerHeight,
                    mDividerPaint
                )
        }
    }


    /**
     * 画完整显示的pdf页面
     */
    private fun drawLoadingPages(canvas: Canvas) {

        mLoadingPages.filter { page ->
            //即将缩放显示的页面，不绘制它的全页bitmap
//            val isScaling = page.pageIndex in mScalingPages.map { it.pageIndex }
            page.pageRect?.fillWidthRect != null
                    && page.bitmap != null
//                    && !isScaling
        }
            .forEach {

                val fillWidthRect = it.pageRect!!.fillWidthRect
                canvas.save()
                canvas.translate(fillWidthRect.left, fillWidthRect.top)
                if (isPreviewMode) {
                    val fillWidthScale = measuredWidth.toFloat() / it.bitmap!!.width
                    canvas.scale(fillWidthScale, fillWidthScale)
                }
                canvas.drawBitmap(it.bitmap!!, 0f, 0f, mPDFPaint)
                canvas.restore()
            }
    }


    /**
     * 画缩放过得pdf页面部分区域
     */
    private fun drawScalingPages(canvas: Canvas) {
        mScalingPages.filter {
            it.pageRect?.fillWidthRect != null && it.bitmap != null
        }
            .forEach {
                val fillWidthRect = it.pageRect!!.fillWidthRect
                canvas.drawBitmap(
                    it.bitmap!!,
                    fillWidthRect.left,
                    fillWidthRect.top,
                    mPDFPaint
                )
            }
    }

    /**
     * 画边界分隔线
     */
    private fun drawEdgeDivider(canvas: Canvas) {
        var divider = mDividerBothside

        //画两边的分割线
        val firstPageRect = mPagePlaceHolders.first().fillWidthRect
        val lastPageRect = mPagePlaceHolders.last().fillWidthRect

        canvas.drawRect(
            paddingLeft.toFloat(),
            firstPageRect.top,
            paddingLeft.toFloat() + divider,
            lastPageRect.bottom,
            mDividerPaint
        )
        canvas.drawRect(
            width - paddingLeft.toFloat() - divider,
            firstPageRect.top,
            width - paddingLeft.toFloat(),
            lastPageRect.bottom,
            mDividerPaint
        )
    }

    /***
     * 获取翻页高度 ps:根据横竖屏区分不同翻页高度
     */

    fun getPageTurnHeight(): Float {
        if (isHorizontal) {
            return (mPagePlaceHolders.get(0).fillWidthRect.bottom + mDividerHeight) * mCanvasScale
        } else {
            return height.toFloat()
        }

    }

    //翻页
    fun pageChangeView(isUp: Boolean = false) {
        lastYpoint = mCanvasTranslate.y
        if (isUp) {
            lastYpoint -= -getPageTurnHeight()
        } else {
            lastYpoint += -getPageTurnHeight()
        }

        val canTranslateYRange = getCanTranslateYRange()
        val nextTranslateY = when {
            lastYpoint in canTranslateYRange -> lastYpoint
            lastYpoint > canTranslateYRange.upper -> canTranslateYRange.upper
            else -> canTranslateYRange.lower
        }
        clearScalingPages()

        stopFlingAnimIfNeeded()

        mCanvasTranslate.set(mCanvasTranslate.x, nextTranslateY)

        invalidate()
        //4.重新计算当前页索引
        calculateCurrentPageIndex()

        submitCreateLoadingPagesTask()
    }

    //选页
    fun selectPage(index: Int) {
        if (index > 0)
            lastYpoint =
                -(mPagePlaceHolders.get(index - 1).fillWidthRect.bottom) * mCanvasScale
        else
            lastYpoint = 0f

        clearScalingPages()

        mCanvasTranslate.set(mCanvasTranslate.x, lastYpoint)

        invalidate()

        //4.重新计算当前页索引
        calculateCurrentPageIndex()

        //加载选择页面
        submitCreateOnePageTask()
    }

    //是否需要加载新数据
    fun isNeedLoadPage(currentPageIndex: Int): Boolean {
        val listIndex = mLoadingPages.map {
            it.pageIndex
        }
        if (listIndex.contains(currentPageIndex)
            && listIndex.contains(currentPageIndex - 1)
            && listIndex.contains(currentPageIndex + 1)
        ) {
            return false
        }

        return true
    }

    @Synchronized
    fun updateScrollHeight(isUp: Boolean = false) {
        lastYpoint = mCanvasTranslate.y
        if (isUp) {
            lastYpoint -= DEF_SCROLL_HEIGHT
        } else {
            lastYpoint += DEF_SCROLL_HEIGHT
        }
        updatePage(y = lastYpoint)

        submitCreateLoadingPagesTask()

    }

    //滚动调整布局
    fun updatePage(x: Float = mCanvasTranslate.x, y: Float) {
        //最大最小限制
        val canTranslateYRange = getCanTranslateYRange()
        val canTranslateXRange = getCanTranslateXRange()
        var nextTranslateX = when {
            x in canTranslateXRange -> x
            x > canTranslateXRange.upper -> canTranslateXRange.upper
            else -> canTranslateXRange.lower
        }
        val nextTranslateY = when {
            y in canTranslateYRange -> y
            y > canTranslateYRange.upper -> canTranslateYRange.upper
            else -> canTranslateYRange.lower
        }


        clearScalingPages()

        stopFlingAnimIfNeeded()
        mCanvasTranslate.set(nextTranslateX, nextTranslateY)

        invalidate()
        //4.重新计算当前页索引
        calculateCurrentPageIndex()

    }


    override fun onTouchEvent(event: MotionEvent): Boolean {

        clearScalingPages()
        var handled = false
        if (event.buttonState == MotionEvent.BUTTON_SECONDARY) return super.onTouchEvent(event)
        when (event.actionMasked) {

            MotionEvent.ACTION_DOWN -> {
                /*  mTouchState =
                      TouchState.SINGLE_POINTER*/
                //如果有正在执行的 fling 动画，就重置动画
                stopFlingAnimIfNeeded()
                mGestureDetector.onTouchEvent(event)
                handled = true
            }

            MotionEvent.ACTION_MOVE -> {
                mTouchState =
                    TouchState.SINGLE_POINTER
                handled = when (mTouchState) {
                    TouchState.SINGLE_POINTER -> mGestureDetector.onTouchEvent(event)
                    else -> false
                }
            }

            MotionEvent.ACTION_UP -> {
                handled = when (mTouchState) {
                    TouchState.SINGLE_POINTER -> {
                        val isFling = mGestureDetector.onTouchEvent(event)
                        if (!isFling) {
                            //单指滑动结束，处理滑动结束（无飞速滑动的情况）
                            submitCreateLoadingPagesTask()
                        }
                        true
                    }

                    else -> false
                }
                mTouchState = TouchState.IDLE
            }
        }
        return handled || super.onTouchEvent(event)
    }

    //鼠标滚动事件及双指滑动
    override fun dispatchGenericMotionEvent(event: MotionEvent?): Boolean {

        if (event?.action == MotionEvent.ACTION_SCROLL) {
            // 是滚动事件
            if (System.currentTimeMillis() - lastScrollTime >= SCROLL_THRESHOLD) {
                // 滚动事件第一次触发, 同步抛弃掉
                lastScrollTime = System.currentTimeMillis()
                return true
            }
            val v = event.getAxisValue(MotionEvent.AXIS_VSCROLL)

            if (v > 0) {
                // 向上滚动
                requireLifecycleOwner().launch {
                    pageFlow.emit(true)
                }

            } else {
                // 向下滚动
                requireLifecycleOwner().launch {
                    pageFlow.emit(false)
                }
            }

            return true
        }

        return super.dispatchGenericMotionEvent(event)
    }


    //缩放
    fun onZoomView(zoomin: Boolean = false) {
        var resultX = 0f
        var resultY = 0f

        if (zoomin) {
            if (mCanvasScale >= MAX_ZOOM_IN) return
            mCanvasScale += ZOOM_STEP
            //
            if (mCanvasTranslate.x != lastTransX) {
                resultX = mCanvasTranslate.x * mCanvasScale / lastScale
            } else {
                //以中心为基准平移
                resultX = -(width * mCanvasScale - width) / 2
                lastTransX = resultX
            }


        } else {
            if (mCanvasScale < MIN_ZOOM_OUT) return
            //缩放小于1倍不可以横向移动
            mCanvasScale -= ZOOM_STEP
            resultX = -(width * mCanvasScale - width) / 2
        }
        resultY = (mCanvasTranslate.y / lastScale * mCanvasScale)
        lastScale = mCanvasScale

        val scaleDisplay = (mCanvasScale * 100).toInt().toString()

        if (toast != null) toast!!.cancel()
        toast = PDFUtil.PDFToast(context, scaleDisplay + "%")
        toast!!.show()
        updatePage(resultX, resultY)

        submitCreateLoadingPagesTask()

    }

    /**
     * 关闭pdf渲染器
     */
    private fun closePdfRenderer() {
        if (mPdfRenderer != null) {
            mPdfRenderer?.close()
            mPdfRenderer = null
        }
    }

    /**
     * 滑动或飞速滑动时，计算当前正在的pdf页索引
     */
    private fun calculateCurrentPageIndex() {
        val translateY = mCanvasTranslate.y
        for (index in mPagePlaceHolders.indices) {
            val pageRect = mPagePlaceHolders[index].fillWidthRect
            val offset = measuredHeight * 0.4 //pdf页占屏幕超过60%时，即为当前页
            if (abs(translateY) + offset > pageRect.top * mCanvasScale
                && abs(translateY) + offset <= (pageRect.bottom + mDividerHeight) * mCanvasScale
            ) {
//                if (index != mCurrentPageIndex) {
                var isvisible = 3

                if (mCanvasTranslate.y == 0f) isvisible = 0
                if (mCanvasTranslate.y == getCanTranslateYRange().lower) isvisible = 1
                if (mPdfTotalHeight <= height) isvisible = 2

                mOnPageChangedListener?.onPageChanged(
                    index,
                    mPagePlaceHolders.size,
                    isvisible
                )
//                }
                mCurrentPageIndex = index
                return
            }
        }
        logTagD(TAG, "calculateCurrentPageIndex-LoopFinish:$mCurrentPageIndex")
    }

    /**
     * 开始飞速滑动的动画
     */
    private fun startFlingAnim(distanceX: Float, y: Float) {
        //控制滑动距离
        var distanceY = y
        if (isPreviewMode) MaxScroll = -getPageTurnHeight()
        if (distanceY < MaxScroll) distanceY = MaxScroll
        //根据每毫秒20像素来计算动画需要的时间
        var animDuration = (max(abs(distanceX), abs(distanceY)) / 20).toLong()
        //时间最短不能小于100毫秒
        when (animDuration) {
            in 0 until 100 -> animDuration = 400
            in 100 until 600 -> animDuration = 600
        }

        logTagD(
            TAG,
            "startFlingAnim--distanceX-$distanceX--distanceY-$distanceY--animDuration-$animDuration"
        )
        mFlingAnim = ValueAnimator().apply {
            setFloatValues(0f, 1f)
            duration = animDuration
            interpolator = DecelerateInterpolator()
            addUpdateListener(
                PDFFlingAnimUpdateListener(
                    this@PDFView,
                    distanceX,
                    distanceY
                )
            )
            addListener(
                PdfFlingAnimListenerAdapter(
                    this@PDFView
                )
            )
            start()
        }
    }

    /**
     * 提交创建pdf页的任务到线程池
     * 如果线程池里有未执行或正在执行的任务，取消那个任务
     */
    fun submitCreateLoadingPagesTask() {
        if (!isNeedLoadPage(mCurrentPageIndex)) return
        requireLifecycleOwner().launch {
            createLoadingPagesTask()
        }
    }

    /**
     * 提交创建pdf当前一页的任务到线程池
     * 下拉列表创建一页 为了提升速度
     */
    fun submitCreateOnePageTask() {
        requireLifecycleOwner().launch {
            createOnePageTask()
        }
    }

    suspend fun createLoadingPagesTask() {
        withContext(Dispatchers.IO) {
            while (isRunning.get()) {
                Thread.sleep(100)
            }
            mCreateLoadingPagesFuture =
                EXECUTOR_SERVICE.submit(
                    PdfPageToBitmapTask(this@PDFView)
                )
        }
    }

    //创建当前显示页
    suspend fun createOnePageTask() {
        withContext(Dispatchers.IO) {
            while (onePageisRunning.get()) {
                Thread.sleep(50)
            }
            mCreateLoadingPagesFuture =
                EXECUTOR_SERVICE.submit(
                    submitCreateOnePageTask(this@PDFView)
                )
        }
    }


    /**
     * 提交创建pdf页的任务到线程池
     * 如果线程池里有未执行或正在执行的任务，取消那个任务
     */
    private fun clearScalingPages() {
        if (mCreateScalingPagesFuture?.isDone != true)
            mCreateScalingPagesFuture?.cancel(true)
        mScalingPages.clear()
        //暂时关闭刷新
        invalidate()
    }

    /**
     * 停止飞速滑动的动画
     */
    private fun stopFlingAnimIfNeeded() {
        mFlingAnim?.cancel()
    }

    /**
     * 获取x轴可平移的间距
     */
    private fun getCanTranslateXRange(): Range<Float> {
        var lower = 0f
        var upper = 0f

        if (mCanvasScale > 1) {
            var leftTrans =
                -(mCanvasScale * mPdfTotalWidth - width) + mCanvasScale * mDividerBothside
            var rightTrans = -mCanvasScale * mDividerBothside
            //bitmap 不大于屏幕宽不可移动
            if (width * mCanvasScale - width < mDividerBothside * mCanvasScale * 2) {
                leftTrans = -(mCanvasScale * mPdfTotalWidth - width) / 2
                rightTrans = -(mCanvasScale * mPdfTotalWidth - width) / 2
            }

            lower = min(leftTrans, rightTrans)
            upper = max(leftTrans, rightTrans)
        }

        //缩放倍速小于1时候平移固定中心
        if (mCanvasScale <= 1) {
            lower = (width - mCanvasScale * mPdfTotalWidth) / 2
            upper = (width - mCanvasScale * mPdfTotalWidth) / 2
        }


        var TranslateX = Range(lower, upper)

        return TranslateX
    }

    /**
     * 获取y轴可平移的间距
     */
    private fun getCanTranslateYRange(): Range<Float> {
        return Range(min(-(mCanvasScale * mPdfTotalHeight - height), 0f), 0f)
    }


    /**
     * 异步处理 pdf_page to bitmap
     */
    private class PDFHandler(pdfView: PDFView) : Handler() {

        companion object {
            const val MESSAGE_INIT_PDF_PLACE_HOLDER = 1
            const val MESSAGE_CREATE_LOADING_PDF_BITMAP = 2
            const val MESSAGE_CREATE_SCALED_BITMAP = 3
        }

        private val mWeakReference = WeakReference(pdfView)
        override fun handleMessage(msg: Message) {
            super.handleMessage(msg)
            val pdfView = mWeakReference.get() ?: return
            when (msg.what) {

                MESSAGE_INIT_PDF_PLACE_HOLDER -> {
                    logTagD(pdfView.TAG, "==handleMessage===MESSAGE_INIT_PDF_PLACE_HOLDER=")
                    val tempPagePlaceHolders =
                        msg.data.getParcelableArrayList<PDFUtil.PageRect>("list")
                    if (!tempPagePlaceHolders.isNullOrEmpty()) {
                        pdfView.mPdfTotalHeight =
                            tempPagePlaceHolders.last().fillWidthRect.bottom + pdfView.LAST_PAGE_DIVIDER
                        pdfView.mPdfTotalWidth = pdfView.width.toFloat()
                        pdfView.mPagePlaceHolders.clear()
                        pdfView.mPagePlaceHolders.addAll(tempPagePlaceHolders)
//                        pdfView.invalidate()
                        pdfView.submitCreateLoadingPagesTask()

                        var isvisible = 3

                        if (pdfView.mCanvasTranslate.y == 0f) isvisible = 0
                        if (pdfView.mCanvasTranslate.y == pdfView.getCanTranslateYRange().lower) isvisible =
                            1
                        if (pdfView.mPdfTotalHeight <= pdfView.height) isvisible = 2


                        //初始化时触发页面变动回调
                        pdfView.mOnPageChangedListener?.onPageChanged(
                            pdfView.mCurrentPageIndex,
                            tempPagePlaceHolders.size,
                            isvisible
                        )
                    }

                }

                MESSAGE_CREATE_LOADING_PDF_BITMAP -> {
                    val calculatedPageIndex = msg.data.getInt("index")
                    val tempLoadingPages =
                        msg.data.getParcelableArrayList<PDFUtil.DrawingPage>("list")
                    if (pdfView.mCurrentPageIndex != calculatedPageIndex) {
                        return
                    }
                    if (!tempLoadingPages.isNullOrEmpty()) {
                        pdfView.loadingDialog.dismiss()
                        pdfView.mLoadingPages.clear()
                        pdfView.mLoadingPages.addAll(tempLoadingPages)
                        pdfView.invalidate()
                    }
                }

            }
        }

    }

    /**
     * 处理触摸滑动和飞速滑动
     * P.S. 只处理单点触摸的操作
     */
    private class OnPDFGestureListener(pdfView: PDFView) :
        GestureDetector.SimpleOnGestureListener() {
        private val mWeakReference = WeakReference(pdfView)

        override fun onSingleTapConfirmed(e: MotionEvent): Boolean {
            mWeakReference.get()?.performClick()
            return true
        }

        override fun onScroll(
            e1: MotionEvent?,
            e2: MotionEvent,
            distanceX: Float,
            distanceY: Float
        ): Boolean {
            val pdfView = mWeakReference.get() ?: return false

            //判断滑动边界，重新设置滑动值
            val canTranslateXRange = pdfView.getCanTranslateXRange()
            val canTranslateYRange = pdfView.getCanTranslateYRange()
            val tempTranslateX = pdfView.mCanvasTranslate.x - distanceX
            val tempTranslateY = pdfView.mCanvasTranslate.y - distanceY

            val nextTranslateX = when {
                tempTranslateX in canTranslateXRange -> tempTranslateX
                tempTranslateX > canTranslateXRange.upper -> canTranslateXRange.upper
                else -> canTranslateXRange.lower
            }
            val nextTranslateY = when {
                tempTranslateY in canTranslateYRange -> tempTranslateY
                tempTranslateY > canTranslateYRange.upper -> canTranslateYRange.upper
                else -> canTranslateYRange.lower
            }
            //3.开始滑动，重绘
            pdfView.mCanvasTranslate.set(nextTranslateX, nextTranslateY)
            pdfView.invalidate()
            //4.重新计算当前页索引
            pdfView.calculateCurrentPageIndex()
            //5. 滑动结束监听回调，创建page位图数据（需要再 onTouchEvent 中判断滑动结束,所以这里返回 false）

            return false
        }

        override fun onFling(
            e1: MotionEvent?,
            e2: MotionEvent,
            velocityX: Float,
            velocityY: Float
        ): Boolean {
            val pdfView = mWeakReference.get() ?: return false
            var mVelocityY = velocityY
            if (
                e1 != null
                && (abs(e1.x - e2.x) > 100 || abs(e1.y - e2.y) > 100)
                && (abs(velocityX) > 500 || abs(velocityY) > 500)
            ) {
                // velocityY > 0 表示用户抛动方向向下，velocityY < 0 表示用户抛动方向向上；这个判断会出现不准确

                if (e2.y < e1.y) {
                    //抛动方向向上
                    mVelocityY = -abs(velocityY)
                } else {
                    //抛动方向向下
                    mVelocityY = abs(velocityY)
                }
                val canTranslateXRange = pdfView.getCanTranslateXRange()
                val canTranslateYRange = pdfView.getCanTranslateYRange()
                val tempTranslateX = pdfView.mCanvasTranslate.x + velocityX * 0.75f
                val tempTranslateY = pdfView.mCanvasTranslate.y + mVelocityY * 0.75f

                logTagD(pdfView.TAG, "==velocityY==${velocityY}")
                logTagD(pdfView.TAG, "==mvelocityY==${mVelocityY}")
                logTagD(pdfView.TAG, "==e1.y==${e1.y}")
                logTagD(pdfView.TAG, "==e2.y==${e2.y}")
                val endTranslateX = when {
                    tempTranslateX in canTranslateXRange -> tempTranslateX
                    tempTranslateX > canTranslateXRange.upper -> canTranslateXRange.upper
                    else -> canTranslateXRange.lower
                }
                val endTranslateY = when {
                    tempTranslateY in canTranslateYRange -> tempTranslateY
                    tempTranslateY > canTranslateYRange.upper -> canTranslateYRange.upper
                    else -> canTranslateYRange.lower
                }


                val distanceX = endTranslateX - pdfView.mCanvasTranslate.x
                var distanceY = endTranslateY - pdfView.mCanvasTranslate.y

                //滑动速度低于10000 情况下 移动距离固定
                if (abs(mVelocityY) < 4000 && abs(distanceY) > 500) {
                    if (mVelocityY > 0) {
                        distanceY = 500f
                    } else {
                        distanceY = -500f
                    }
                }

                pdfView.startFlingAnim(distanceX, distanceY)

                return true
            }

            return super.onFling(e1, e2, velocityX, mVelocityY)
        }
    }

    /**
     * 飞速滑动动画更新回调
     */
    private class PDFFlingAnimUpdateListener(
        pdfView: PDFView,
        private val distanceX: Float,
        private val distanceY: Float
    ) : ValueAnimator.AnimatorUpdateListener {
        private val mWeakReference = WeakReference(pdfView)
        private val lastCanvasTranslate = PointF(
            pdfView.mCanvasTranslate.x,
            pdfView.mCanvasTranslate.y
        )

        override fun onAnimationUpdate(animation: ValueAnimator) {
            val pdfView = mWeakReference.get() ?: return

            //飞速滑动时，不渲染缩放的 bitmap
            pdfView.clearScalingPages()

            val percent = animation.animatedValue as Float
            pdfView.mCanvasTranslate.x = lastCanvasTranslate.x + distanceX * percent
            pdfView.mCanvasTranslate.y = lastCanvasTranslate.y + distanceY * percent
            pdfView.invalidate()
            //重新计算当前页索引
            pdfView.calculateCurrentPageIndex()
        }
    }

    private class PdfFlingAnimListenerAdapter(pdfView: PDFView) : AnimatorListenerAdapter() {
        private val mWeakReference = WeakReference(pdfView)

        override fun onAnimationCancel(animation: Animator) {
            preLoadPdf()
        }

        override fun onAnimationEnd(animation: Animator) {
            preLoadPdf()
        }

        private fun preLoadPdf() {
            val pdfView = mWeakReference.get() ?: return
            pdfView.submitCreateLoadingPagesTask()
        }
    }

    /**
     * 线程任务
     * 创建要显示的pdf页的bitmap集合
     * 有本地缓存的话用本地缓存，没有缓存的话从pdf生成bitmap后缓存
     */
    private class PdfPageToBitmapTask(pdfView: PDFView) : Runnable {
        private val mWeakReference = WeakReference(pdfView)
        override fun run() {
            val pdfView = mWeakReference.get() ?: return
            pdfView.isRunning.set(true)
            val tempLoadingPages = arrayListOf<PDFUtil.DrawingPage>()
            val pdfRenderer = pdfView.mPdfRenderer ?: return
            val pagePlaceHolders = pdfView.mPagePlaceHolders
            val currentPageIndex = pdfView.mCurrentPageIndex
            val startLoadingIndex = max(0, currentPageIndex - pdfView.mOffscreenPageLimit)
            val endLoadingIndex =
                min(currentPageIndex + pdfView.mOffscreenPageLimit, pagePlaceHolders.lastIndex)

            for (index in startLoadingIndex..endLoadingIndex) {
                var disIndex = index
                val pageRect = pagePlaceHolders[index]
                val fillWidthRect = pageRect.fillWidthRect

                //预览单独存储
                if (fillWidthRect.width() < pdfView.SMALL_WINDOW_WIDTH) {
                    disIndex = index - 100
                }
                val key = getCachedKey(disIndex, pdfView.mPdfName!!, pdfView.pdfFileSize)
                var bitmap = pdfView.getLoadingPagesBitmapFromCache(key)
                if (bitmap == null) {
                    //3.本地缓存没拿到，从pdf渲染器创建bitmap
                    val page = pdfRenderer.openPage(index)
                    bitmap = Bitmap.createBitmap(
                        (fillWidthRect.width()).toInt(),
                        (fillWidthRect.height()).toInt(),
                        Bitmap.Config.ARGB_8888
                    )
                    page.render(bitmap, null, null, PdfRenderer.Page.RENDER_MODE_FOR_DISPLAY)
                    page.close()
                    //新创建的bitmap，存到内存缓存和本地缓存
                    pdfView.putLoadingPagesBitmapToCache(key, bitmap)
                    if (index == currentPageIndex)
                        pdfView.linkedBlockingQueue.offer(Pair(key, bitmap))

                }
                tempLoadingPages.add(
                    PDFUtil.DrawingPage(
                        pageRect,
                        bitmap,
                        index
                    )
                )
            }

            pdfView.isRunning.set(false)
            val message = Message()
            message.what =
                PDFHandler.MESSAGE_CREATE_LOADING_PDF_BITMAP
            message.data.putInt("index", currentPageIndex)
            message.data.putParcelableArrayList("list", tempLoadingPages)
            pdfView.mPDFHandler.sendMessage(message)
        }
    }

    /**
     * 线程任务
     * 创建当前页bitmap
     */
    private class submitCreateOnePageTask(pdfView: PDFView) : Runnable {
        private val mWeakReference = WeakReference(pdfView)
        override fun run() {
            val pdfView = mWeakReference.get() ?: return
            pdfView.onePageisRunning.set(true)
            val tempLoadingPages = arrayListOf<PDFUtil.DrawingPage>()
            val pdfRenderer = pdfView.mPdfRenderer ?: return
            val pagePlaceHolders = pdfView.mPagePlaceHolders
            val currentPageIndex = pdfView.mCurrentPageIndex
            var disIndex = currentPageIndex
            val pageRect = pagePlaceHolders[currentPageIndex]
            val fillWidthRect = pageRect.fillWidthRect

            //预览单独存储
            if (fillWidthRect.width() < pdfView.SMALL_WINDOW_WIDTH) {
                disIndex = disIndex - 100
            }
            val key = getCachedKey(disIndex, pdfView.mPdfName!!, pdfView.pdfFileSize)
            var bitmap = pdfView.getLoadingPagesBitmapFromCache(key)
            if (bitmap == null) {
                //3.本地缓存没拿到，从pdf渲染器创建bitmap
                val page = pdfRenderer.openPage(currentPageIndex)
                bitmap = Bitmap.createBitmap(
                    (fillWidthRect.width()).toInt(),
                    (fillWidthRect.height()).toInt(),
                    Bitmap.Config.ARGB_8888
                )
                page.render(bitmap, null, null, PdfRenderer.Page.RENDER_MODE_FOR_DISPLAY)
                page.close()
                //新创建的bitmap，存到内存缓存和本地缓存
                pdfView.putLoadingPagesBitmapToCache(key, bitmap)
                pdfView.linkedBlockingQueue.offer(Pair(key, bitmap))

            }
            tempLoadingPages.add(
                PDFUtil.DrawingPage(
                    pageRect,
                    bitmap,
                    currentPageIndex
                )
            )

            pdfView.onePageisRunning.set(false)
            val message = Message()
            message.what =
                PDFHandler.MESSAGE_CREATE_LOADING_PDF_BITMAP
            message.data.putInt("index", currentPageIndex)
            message.data.putParcelableArrayList("list", tempLoadingPages)
            pdfView.mPDFHandler.sendMessage(message)
        }
    }


    /**
     * 线程任务
     * 初始化pdf页框架数据
     */
    private class InitPdfFramesTask(pdfView: PDFView) : Runnable {
        private val mWeakReference = WeakReference(pdfView)
        override fun run() {
            val pdfView = mWeakReference.get() ?: return
            logTagD(pdfView.TAG, "======始化pdf页框架数据")
            val pdfRenderer =
                pdfView.mPdfRenderer ?: throw NullPointerException("pdfRenderer is null!")
            val tempPagePlaceHolders = arrayListOf<PDFUtil.PageRect>()

            var pdfTotalHeight = 0f

            var left = pdfView.mDividerBothside
            var right = pdfView.measuredWidth.toFloat() - pdfView.mDividerBothside


            var fillWidthScale: Float
            var scaledHeight: Float
            for (index in 0 until pdfRenderer.pageCount) {
                val page = pdfRenderer.openPage(index)
                //根据长宽调整 比例

                fillWidthScale = (right - left) / page.width.toFloat()
                scaledHeight = page.height * fillWidthScale

                //预留分割线的高度
                if (index != 0)
                    pdfTotalHeight += pdfView.mDividerHeight

                val rect = RectF(left, pdfTotalHeight, right, pdfTotalHeight + scaledHeight)
                pdfTotalHeight = rect.bottom

                tempPagePlaceHolders.add(
                    PDFUtil.PageRect(
                        fillWidthScale,
                        rect
                    )
                )
                page.close()
            }

            val message = Message()
            message.what =
                PDFHandler.MESSAGE_INIT_PDF_PLACE_HOLDER
            message.data.putParcelableArrayList("list", tempPagePlaceHolders)
            pdfView.mPDFHandler.sendMessage(message)
        }
    }


    interface OnPageChangedListener {
        fun onPageChanged(currentPageIndex: Int, totalPageCount: Int, isVisible: Int)
    }

    //region 缓存存取相关
    /**
     * 从缓存获取将要加载的page的bitmap
     */
    @Synchronized
    private fun getLoadingPagesBitmapFromCache(key: String): Bitmap? {
//        val key = getCachedKey(pageIndex, mPdfName!!)
        var bitmap = getLoadingPagesBitmapFromMemory(key)

        if (bitmap == null) {
            //2.内存缓存没拿到，从本地缓存拿
            bitmap = getLoadingPagesBitmapToDisk(key)
        }
        return bitmap
    }

    /**
     * 从内存缓存获取loadingPage 的 bitmap
     */
    private fun getLoadingPagesBitmapFromMemory(key: String): Bitmap? {
        return mLoadingPagesBitmapMemoryCache.get(key)
    }

    @Synchronized
    private fun getLoadingPagesBitmapToDisk(key: String): Bitmap? {
        try {
            val snapShot = mLoadingPagesBitmapDiskCache.get(key)
            snapShot ?: return null


            val fileInputStream = snapShot.getInputStream(0) as FileInputStream
            /*val fileDescriptor = fileInputStream.fd
            //create bitmap from disk
            val options = BitmapFactory.Options()
            options.inJustDecodeBounds = true
            BitmapFactory.decodeFileDescriptor(fileDescriptor, null, options)

            options.inSampleSize = calculateInSampleSize(options, width, height)

            //避免出现内存溢出的情况，进行相应的属性设置。
            options.inPreferredConfig = Bitmap.Config.RGB_565
            options.inJustDecodeBounds = false
            options.inDither = true
            var bitmap =   BitmapFactory.decodeFileDescriptor(fileDescriptor, null, options)*/
            var bitmap = BitmapFactory.decodeStream(fileInputStream, null, BitmapFactory.Options())
            bitmap = bitmap?.copy(Bitmap.Config.ARGB_8888, true)
            fileInputStream.close()
            snapShot?.close()
            if (bitmap != null)
                putLoadingPagesBitmapToMemory(key, bitmap)
            return bitmap
        } catch (e: Exception) {
            e.printStackTrace()
            return null
        }
    }

    /**
     * 把 page 的 bitmap 缓存起来
     */
    private fun putLoadingPagesBitmapToCache(key: String, bitmap: Bitmap) {
//        val key = getCachedKey(pageIndex, mPdfName!!)
        //缓存到内存
        putLoadingPagesBitmapToMemory(key, bitmap)
        //缓存到本地
//        putLoadingPagesBitmapToDisk(key, bitmap)
    }

    private fun putLoadingPagesBitmapToMemory(key: String, bitmap: Bitmap) {
        mLoadingPagesBitmapMemoryCache.put(key, bitmap)
    }

    private fun putLoadingPagesBitmapToDisk(key: String, bitmap: Bitmap) {
        try {
            val edit = mLoadingPagesBitmapDiskCache.edit(key)
            val outputStream = edit.newOutputStream(0)
            bitmap.compress(Bitmap.CompressFormat.PNG, 90, outputStream)
            edit.commit()
            outputStream.close()
            mLoadingPagesBitmapDiskCache.flush()
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }


}