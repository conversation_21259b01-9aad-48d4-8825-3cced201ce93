package com.czur.starry.device.baselib.network.core;

import android.util.Log;

import com.czur.starry.device.baselib.network.core.interceptor.ActiveInterceptor;
import com.czur.starry.device.baselib.network.core.interceptor.CommonParamInterceptor;
import com.czur.starry.device.baselib.network.core.interceptor.LoggingInterceptor;
import com.czur.starry.device.baselib.network.core.interceptor.SignatureInterceptor;
import com.czur.starry.device.baselib.network.core.interceptor.TokenInterceptor;
import com.google.gson.Gson;

import java.lang.annotation.Annotation;
import java.lang.reflect.InvocationHandler;
import java.lang.reflect.Method;
import java.lang.reflect.Proxy;
import java.lang.reflect.Type;
import java.util.Collection;
import java.util.HashMap;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;

import okhttp3.OkHttpClient;

public class MiaoHttpManager {

    public static final int STATUS_FAIL = 1001;
    public static final int STATUS_TIMEOUT = 1002;
    public static final int STATUS_NOT_MANAGER = 1003;
    public static final int STATUS_SUCCESS = 1000;

    private static final int METHOD_NONE = 0;
    private static final int METHOD_GET = 1;
    private static final int METHOD_POST = 2;

    private final ExecutorService executor = Executors.newFixedThreadPool(5);
    private volatile OkHttpClient client;
    private final Gson gson = new Gson();


    private MiaoHttpManager() {
    }

    public static MiaoHttpManager getInstance() {
        return SingletonHolder.instance;
    }

    private static class SingletonHolder {
        static final MiaoHttpManager instance = new MiaoHttpManager();
    }

    public void init() {
        client = new OkHttpClient.Builder()
                .connectTimeout(8, TimeUnit.SECONDS)
                .readTimeout(8, TimeUnit.SECONDS)
                .retryOnConnectionFailure(true)
                .addInterceptor(new TokenInterceptor())     // Token过期后 刷新Token
                .addInterceptor(new ActiveInterceptor())    // 激活检测
                .addInterceptor(new CommonParamInterceptor()) // 通用请求参数(请求头和必须参数)
                .addInterceptor(new SignatureInterceptor()) // 签名
                .addInterceptor(new LoggingInterceptor())   // 输出Log
                .build();
    }

    public <T> T create(Class<T> service, String endpoint) {
        return (T) Proxy.newProxyInstance(service.getClassLoader(), new Class<?>[]{service}, new HttpHandler<T>(endpoint));
    }

    private class HttpHandler<T> implements InvocationHandler {

        private final String endpoint;

        HttpHandler(String endpoint) {
            this.endpoint = endpoint;
        }

        @Override
        public Object invoke(Object proxy, Method method, Object[] args) throws Throwable {
            if (method.getDeclaringClass() == Object.class) {
                return method.invoke(this, args);
            }

            if (args == null || args.length == 0) {
                throw new Exception("方法参数为空或者规则不正确!");
            }

            Annotation[][] paramAnnotationArr;
            MiaoHttpMethod miaoHttpMethod;
            miaoHttpMethod = new MiaoHttpMethod();
            miaoHttpMethod.setIsAsync(args[args.length - 1] instanceof Callback);
            miaoHttpMethod.setNoParamCount(miaoHttpMethod.isAsync() ? 2 : 1);
            paramAnnotationArr = method.getParameterAnnotations();
            String body = null;
            int bodyAnnotationIndex = -1;
            for (int i = 0; i < paramAnnotationArr.length - miaoHttpMethod.getNoParamCount(); i++) {
                Annotation tempAnnotation = paramAnnotationArr[i][0];
                if (tempAnnotation instanceof MiaoHttpParam) {
                    miaoHttpMethod.getParams().put(((MiaoHttpParam) tempAnnotation), i + "");
                } else if (tempAnnotation instanceof MiaoHttpHeader) {
                    // 处理header
                    String headerValue = ((MiaoHttpHeader) tempAnnotation).value();
                    if (args[i] instanceof String) {
                        miaoHttpMethod.getHeaders().put(((MiaoHttpHeader) tempAnnotation).value(), (String) args[i]);
                    } else {
                        Log.e("MiaoHttpManager", "header value must be String");
                        String[] split = headerValue.split(":");
                        if (split.length >= 2) {
                            miaoHttpMethod.getHeaders().put(split[0].trim(), split[1].trim());
                        }
                    }


                } else if (tempAnnotation instanceof MiaoHttpPath) {
                    miaoHttpMethod.getPaths().put(((MiaoHttpPath) tempAnnotation).value(), i + "");
                } else if (tempAnnotation instanceof MiaoHttpBody) {
                    bodyAnnotationIndex = i;
                }
            }
            Annotation[] annotations = method.getAnnotations();
            int witchMethod = METHOD_NONE;
            Class<?> clazz;
            MiaoHttpGet miaoGet = null;
            MiaoHttpPost miaoPost = null;
            for (Annotation annotation : annotations) {
                clazz = annotation.annotationType();
                if (clazz == MiaoHttpGet.class) {
                    witchMethod = METHOD_GET;
                    miaoGet = (MiaoHttpGet) annotation;
                } else if (clazz == MiaoHttpPost.class) {
                    witchMethod = METHOD_POST;
                    miaoPost = (MiaoHttpPost) annotation;
                } else if (clazz == MiaoHttpHeader.class) {
                    String[] values = ((MiaoHttpHeader) annotation).value().split(":");
                    if (values.length >= 2) {
                        miaoHttpMethod.getHeaders().put(values[0].trim(), values[1].trim());
                    }
                }
            }
            if (witchMethod == METHOD_NONE) {
                throw new Exception("方法上面说好的注解呢?MiaoGet或者MiaoPost什么的?");
            }
            if (bodyAnnotationIndex != -1 && witchMethod != METHOD_POST) {
                throw new Exception("带http body的方法必须是post请求");
            }

            switch (witchMethod) {
                case METHOD_GET:
                    miaoHttpMethod.setMethod(MiaoHttpMethod.Method.Get);
                    miaoHttpMethod.setUrl(endpoint + miaoGet.value());
                    break;
                case METHOD_POST:
                    miaoHttpMethod.setMethod(MiaoHttpMethod.Method.Post);
                    miaoHttpMethod.setUrl(endpoint + miaoPost.value());
                    break;
            }


            HashMap<String, String> postParam = new HashMap<>();
            String url = "";
            // 生成参数列表
            for (Map.Entry<MiaoHttpParam, String> entry : miaoHttpMethod.getParams().entrySet()) {
                // 处理集合
                Object arg = args[Integer.parseInt(entry.getValue())];
                String value;
                if (entry.getKey().json()) {
                    // 处理Json数据
                    value = gson.toJson(arg);
                } else {
                    value = String.valueOf(arg);
                    if (arg instanceof Collection) {
                        // 去除中括号
                        value = ((Collection<?>) arg).stream()
                                .map(Object::toString)
                                .reduce((a, b) -> a + "," + b).orElse("");
                    }
                }

                if (arg == null) {
                    // 参数为空, 不添加该参数
                    continue;
                }
                postParam.put(entry.getKey().value(), value);
            }


            // 处理请求body
            if (bodyAnnotationIndex != -1) {
                if (args[bodyAnnotationIndex] instanceof String) {
                    body = (String) args[bodyAnnotationIndex];
                } else {
                    body = gson.toJson(args[bodyAnnotationIndex]);
                }
            }

            String baseUrl = miaoHttpMethod.getUrl();
            for (Map.Entry<String, String> entry : miaoHttpMethod.getPaths().entrySet()) {
                String value = (String) args[Integer.parseInt(entry.getValue())];
                if (value == null) {
                    value = "";
                }
                baseUrl = baseUrl.replace("{" + entry.getKey() + "}", value);
            }
            miaoHttpMethod.setUrl(baseUrl + url);

            if (miaoHttpMethod.isAsync()) {
                // 异步请求
                final Callback<T> callback = (Callback<T>) args[args.length - 1];
                Type type = (Type) args[args.length - 2];
                if (MiaoHttpMethod.Method.Get.equals(miaoHttpMethod.getMethod())) {
                    // get请求
                    executor.execute(new AsyncHttpTask<>(miaoHttpMethod.getUrl(), type, callback, miaoHttpMethod.getHeaders(), postParam));
                } else {
                    executor.execute(new AsyncHttpTask<>(miaoHttpMethod.getUrl(), type, postParam, callback, miaoHttpMethod.getHeaders(), body));
                }
            } else {
                // 同步请求
                Type type = (Type) args[args.length - 1];
                if (MiaoHttpMethod.Method.Get.equals(miaoHttpMethod.getMethod())) {
                    // get请求
                    return SyncHttpTask.getInstance().syncGet(miaoHttpMethod.getUrl(), type, miaoHttpMethod.getHeaders(), postParam);
                } else {
                    // post请求
                    return SyncHttpTask.getInstance().syncPost(miaoHttpMethod.getUrl(), type, postParam, miaoHttpMethod.getHeaders(), body);
                }
            }
            return null;
        }
    }

    public interface Callback<T> {
        void onResponse(MiaoHttpEntity<T> entity);

        void onFailure(MiaoHttpEntity<T> entity);

        void onError(Exception e);
    }


    OkHttpClient getHttpClient() {
        if (client == null) {
            synchronized (MiaoHttpManager.class) {
                if (client == null) {
                    init();
                }
            }
        }
        return client;
    }
}
