package com.czur.starry.device.baselib.utils

import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import java.io.File
import java.io.FileOutputStream
import java.io.OutputStream
import java.io.PrintWriter

/**
 * Created by 陈丰尧 on 2021/9/17
 */

/**
 * 为文件打开添加模式的输出流
 */
fun File.appendOutStream(): FileOutputStream = FileOutputStream(this, true)
fun OutputStream.printWriter(): PrintWriter = PrintWriter(this)

/**
 * 清空文件夹
 * @return true: 清理成功(文件夹为空)
 *          false: 清理失败,或传入的不是文件夹
 */
suspend fun File.clearDir(): Boolean = withContext(Dispatchers.IO) {
    if (!isDirectory) {
        return@withContext false
    }
    listFiles()?.fold(true) { acc, file ->
        if (file.isDirectory) {
            file.deleteRecursively()
        } else {
            file.delete()
        } && acc
    } ?: false
}