package com.czur.starry.device.baselib.view.floating

import android.graphics.Bitmap
import android.view.KeyEvent
import com.czur.starry.device.baselib.base.listener.KeyDownListener

/**
 * 默认点击返回键会dismiss弹窗
 */
abstract class KeyBackFloatFragment(
    bgDark: Boolean = false,
    outSideDismiss: Boolean = true,
    outSideClick: Boolean = false,  // float外部是否可以点击到其他内容
    inputMode: Int? = null,
    showMode: FloatShowMode = FloatShowMode.NORMAL,
    blockOutSideClickBeforeAnim: Boolean = false,
    bgImg: Bitmap? = null
) : FloatFragment(
    bgDark,
    outSideDismiss,
    outSideClick,
    inputMode,
    showMode,
    blockOutSideClickBeforeAnim = blockOutSideClickBeforeAnim,
    bgImg = bgImg
), KeyDownListener {

    override fun onKeyDown(keyCode: Int, event: KeyEvent?): Bo<PERSON>an {
        return if (keyCode == KeyEvent.KEYCODE_BACK) {
            if (!isDismissing) {
                dismiss()
            }
            true
        } else {
            false
        }
    }
}