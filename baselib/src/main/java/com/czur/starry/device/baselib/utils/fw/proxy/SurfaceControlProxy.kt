package com.czur.starry.device.baselib.utils.fw.proxy

import android.graphics.Bitmap
import android.graphics.Rect
import android.os.IBinder
import com.czur.czurutils.log.logTagE
import com.czur.starry.device.baselib.utils.getScreenHeight
import com.czur.starry.device.baselib.utils.getScreenWidth
import java.lang.reflect.Constructor
import java.lang.reflect.Method


/**
 * Created by 陈丰尧 on 2024/6/18
 */
object SurfaceControlProxy {
    private const val TAG = "SurfaceControlWrapper"
    private val screenRect: Rect by lazy(LazyThreadSafetyMode.NONE) {
        Rect(0, 0, getScreenWidth(), getScreenHeight())
    }

    private val CLASS by lazy(LazyThreadSafetyMode.NONE) {
        Class.forName("android.view.SurfaceControl")
    }

    private val builtInDisplayMethod: Method by lazy(LazyThreadSafetyMode.NONE) {
        CLASS.getMethod("getInternalDisplayToken")
    }


    private fun getBuiltInDisplay(): IBinder? {
        try {
            return builtInDisplayMethod.invoke(null) as IBinder
        } catch (e: ReflectiveOperationException) {
            logTagE(TAG, "Could not invoke method", tr = e)
            return null
        }
    }


    fun takeScreenshot(crop: Rect = screenRect): Bitmap? {
        try {
            val width = crop.width()
            val height = crop.height()
            val displayToken: IBinder = getBuiltInDisplay() ?: return null
            val builderClass =
                Class.forName("android.view.SurfaceControl\$DisplayCaptureArgs\$Builder")
            val builderConstructor: Constructor<*> =
                builderClass.getDeclaredConstructor(IBinder::class.java)
            builderConstructor.isAccessible = true
            val builder: Any = builderConstructor.newInstance(displayToken)
            val sourceCropField = builderClass.getMethod(
                "setSourceCrop",
                Rect::class.java
            )
            sourceCropField.invoke(builder, crop)

            val sizeMethod = builderClass.getMethod("setSize", Integer.TYPE, Integer.TYPE)
            sizeMethod.isAccessible = true
            sizeMethod.invoke(builder, width, height)

            val build = builderClass.getMethod("build")
            build.isAccessible = true
            val captureArgs = build.invoke(builder)

            val captureArgsClass = Class.forName("android.view.SurfaceControl\$DisplayCaptureArgs")

            val argsMethod: Method = CLASS.getMethod("captureDisplay", captureArgsClass)
            argsMethod.isAccessible = true
            val cap = argsMethod.invoke(CLASS, captureArgs)
                ?: throw java.lang.Exception("Inject SurfaceControl captureDisplay return null")
            val bufferClass = Class.forName("android.view.SurfaceControl\$ScreenshotHardwareBuffer")
            return bufferClass.getMethod("asBitmap").invoke(cap) as Bitmap
        } catch (e: java.lang.Exception) {
            // ignore exception
            logTagE(TAG, "takeScreenshot error", tr = e)
            return null
        }
    }
}