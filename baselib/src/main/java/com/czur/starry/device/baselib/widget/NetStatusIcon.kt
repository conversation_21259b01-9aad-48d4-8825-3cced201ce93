package com.czur.starry.device.baselib.widget

import android.content.Context
import android.util.AttributeSet
import android.view.ViewTreeObserver
import androidx.appcompat.widget.AppCompatImageView
import androidx.lifecycle.DefaultLifecycleObserver
import androidx.lifecycle.LifecycleOwner
import androidx.lifecycle.observe
import com.czur.czurutils.log.logTagD
import com.czur.czurutils.log.logTagW
import com.czur.starry.device.baselib.R
import com.czur.starry.device.baselib.utils.InternetStatus
import com.czur.starry.device.baselib.utils.NetStatusUtil
import com.czur.starry.device.baselib.utils.requireLifecycleOwner

/**
 * Created by 陈丰尧 on 2021/7/12
 * 根据网络状态切换UI图标
 */
private const val TAG = "NetStatusIcon"

class NetStatusIcon @JvmOverloads constructor(
    context: Context, attrs: AttributeSet? = null, defStyleAttr: Int = 0
) : AppCompatImageView(context, attrs, defStyleAttr), ViewTreeObserver.OnGlobalLayoutListener {

    private val netStatusUtil: NetStatusUtil

    // 当前图标的网络状态
    private var netStatus = NetStatusUtil.NetStatus.NO_NET_WORK

    /**
     * 网络状态图标的点击事件
     * status: 当前的网络状态
     */
    var onNetViewClick: ((status: NetStatusUtil.NetStatus) -> Unit)? = null
        set(value) {
            if (value != null) {
                field = value
                setOnClickListener {
                    onNetViewClick?.invoke(netStatus)
                }
            } else {
                setOnClickListener(null)
            }
        }

    init {
        viewTreeObserver.addOnGlobalLayoutListener(this)
        netStatusUtil = NetStatusUtil(context)
    }

    private fun setWifiImg(signalLevel: Int) {
        when (signalLevel) {
            1 -> setImageResource(R.drawable.baselib_icon_wifi_weak)
            2 -> setImageResource(R.drawable.baselib_icon_wifi_mid)
            3 -> setImageResource(R.drawable.baselib_icon_wifi)
            else -> {
                logTagW(TAG, "信号强度范围应当是0-2,当前信号强度为:${signalLevel}")
                setImageResource(R.drawable.baselib_icon_wifi_bad)
            }
        }
    }


    /**
     * 当UI加载完成后,再进行网络状态监控
     */
    override fun onGlobalLayout() {
        viewTreeObserver.removeOnGlobalLayoutListener(this)
        val lifecycleOwner = requireLifecycleOwner()
        // 监听网络状态的改变
        netStatusUtil.netStatus.observe(lifecycleOwner) {
            logTagD(TAG, "状态:${it}")
            netStatus = it
            when (it) {
                NetStatusUtil.NetStatus.NO_NET_WORK -> setImageResource(R.drawable.ic_status_no_network)
                NetStatusUtil.NetStatus.WIFI -> setWifiImg(netStatusUtil.getWifiSignalLevel())
                NetStatusUtil.NetStatus.ETHERNET ->{
                    if (netStatusUtil.internetStatusLive.value == InternetStatus.CONNECT){
                        setImageResource(R.drawable.ic_status_ethernet)
                    }else{
                        setImageResource(R.drawable.ic_ethernet_unidentified)
                    }
                }
                else -> {
                }
            }

        }

        // 绑定生命周期
        lifecycleOwner.lifecycle.addObserver(object : DefaultLifecycleObserver {
            override fun onStart(owner: LifecycleOwner) {
                super.onStart(owner)
                // 当处于可见生命周期,才开始监控网络状态
                netStatusUtil.startWatching()
            }

            override fun onStop(owner: LifecycleOwner) {
                super.onStop(owner)
                // 当处于不可见生命周期时, 停止监控网络状态
                netStatusUtil.stopWatching()
            }
        })
        //仅有线网使用时，判断是否ping通外网
        netStatusUtil.internetStatusLive.observe(lifecycleOwner) {
            if (netStatusUtil.netStatus.value == NetStatusUtil.NetStatus.ETHERNET) {
                if (InternetStatus.CONNECT == it) {
                    setImageResource(R.drawable.ic_status_ethernet)
                } else {
                    setImageResource(R.drawable.ic_ethernet_unidentified)
                }
            }
        }
    }

}

