package com.czur.starry.device.baselib.widget

import android.content.Context
import android.util.AttributeSet
import android.view.MotionEvent
import android.view.View
import android.widget.SeekBar
import com.czur.starry.device.baselib.R

/**
 * 用来增加seekBar的点击范围的
 */
class SeekBarTouch @JvmOverloads constructor(
    context: Context, attrs: AttributeSet? = null, defStyleAttr: Int = 0,
) : View(context, attrs, defStyleAttr) {

    var targetSeekBar: SeekBar? = null

    init {
        val ta = context.obtainStyledAttributes(attrs,
            R.styleable.SeekBarTouch
        )
        val seekBarId = ta.getResourceId(R.styleable.SeekBarTouch_targetSeekBar, -1)
        if (seekBarId != -1) {
            post {
                targetSeekBar = (parent as View).findViewById(seekBarId) as SeekBar
            }
        }
        ta.recycle()
    }

    override fun onTouchEvent(event: MotionEvent): Boolean {
        targetSeekBar?.let {
            val y = height / 2f
            val x = event.x
            val me = MotionEvent.obtain(
                event.downTime,
                event.eventTime,
                event.action,
                x,
                y,
                event.metaState
            )
            return it.onTouchEvent(me)
        }
        return super.onTouchEvent(event)
    }
}