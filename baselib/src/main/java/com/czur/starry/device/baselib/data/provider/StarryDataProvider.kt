package com.czur.starry.device.baselib.data.provider

import android.app.Application
import android.content.ContentResolver
import com.google.gson.Gson
import com.google.gson.GsonBuilder
import java.text.SimpleDateFormat
import java.util.*

object StarryDataProvider {

    const val PARAM_TYPE_STRING = "String"
    const val PARAM_TYPE_INT = "Int"
    const val PARAM_TYPE_LONG = "Long"
    const val PARAM_TYPE_DATE = "Date"
    const val PARAM_MERGE_TYPE_LIST = "StarryDataProviderInsertTypeList"

    const val CONTENT = "content"
    const val AUTHORITY = "com.czur.starry.launcher.dataProvider"

    private lateinit var application: Application
    private lateinit var contentResolver: ContentResolver
    lateinit var gson: Gson
    val simpleDateFormat = SimpleDateFormat("yyyy-MM-dd HH:mm:ss", Locale.CHINA)

    fun init(application: Application) {
        this.application = application
        contentResolver = application.contentResolver
        gson = GsonBuilder().setDateFormat("yyyy-MM-dd HH:mm:ss").create()
    }


    fun stringToDate(str: String?): Date? = if (str == null) {
        null
    } else {
        try {
            simpleDateFormat.parse(str)
        } catch (e: Exception) {
            e.printStackTrace()
            null
        }
    }

}
