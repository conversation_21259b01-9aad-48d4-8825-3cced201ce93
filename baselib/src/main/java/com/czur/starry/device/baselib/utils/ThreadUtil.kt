package com.czur.starry.device.baselib.utils

import android.os.Handler
import android.os.Looper
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.MainScope
import kotlinx.coroutines.launch

private val mainHandler by lazy { Handler(Looper.getMainLooper()) }
private val mainScope by lazy { MainScope() }

private val currentProcessNameMethod by lazy {
    Class.forName("android.app.ActivityThread")
        .getMethod("currentProcessName")
}

fun getProcessName(): String {
    return currentProcessNameMethod.invoke(null)?.toString() ?: ""
}

/**
 * 当前线程名
 */
val currentThreadName: String
    get() = Thread.currentThread().name

/**
 * 判断当前线程是否是主线程
 */
fun inMainThread(): Boolean = Looper.getMainLooper().isCurrentThread

/**
 * 在主线程中执行
 */
fun doOnUIThread(block: () -> Unit) {
    mainHandler.post(block)
}

/**
 * 在MainScope中执行
 */
fun onOnMainScope(block: CoroutineScope.() -> Unit) {
    mainScope.launch {
        block()
    }
}

fun onSuspendMainScope(block: suspend CoroutineScope.() -> Unit) {
    mainScope.launch {
        block()
    }
}