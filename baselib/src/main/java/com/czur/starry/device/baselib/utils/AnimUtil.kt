package com.czur.starry.device.baselib.utils

import android.animation.ValueAnimator
import android.view.ViewPropertyAnimator

/**
 * Created by 陈丰尧 on 2022/5/7
 */
/**
 * 同时放大XY
 */
fun ViewPropertyAnimator.scaleXY(value: Float): ViewPropertyAnimator {
    return scaleX(value)
        .scaleY(value)
}

fun createFloatFrameLockAnim(
    fps: Int,
    vararg values: Float,
    onUpdate: (valueAnim: ValueAnimator) -> Unit
): ValueAnimator {
    require(fps in 1..60) { "锁定的帧率必须在1..60" }
    var lastUpdateTime = 0L
    val interval = ONE_SECOND / fps
    return ValueAnimator.ofFloat(*values).apply {
        addUpdateListener {
            val currentTime = System.currentTimeMillis()
            if (currentTime - lastUpdateTime > interval) {
                lastUpdateTime = currentTime
                onUpdate(it)
            }
        }
    }
}