package com.czur.starry.device.baselib.tips;

import android.content.Context;
import android.content.res.TypedArray;
import android.text.TextUtils;
import android.util.ArrayMap;
import android.util.AttributeSet;
import android.view.InflateException;
import android.view.LayoutInflater;
import android.view.View;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.appcompat.app.AppCompatActivity;

import com.czur.czurutils.log.CZURLogUtilsKt;
import com.czur.starry.device.baselib.R;

import java.lang.reflect.Constructor;
import java.lang.reflect.Method;
import java.util.HashMap;
import java.util.Map;

/**
 * Created by 陈丰尧 on 2021/10/11
 */
public class FloatTipFactory implements LayoutInflater.Factory2 {
    private LayoutInflater.Factory mViewCreateFactory;
    private LayoutInflater.Factory2 mViewCreateFactory2;

    private static final Class<?>[] sConstructorSignature = new Class[]{Context.class, AttributeSet.class};
    private static final Object[] mConstructorArgs = new Object[2];
    private static final Map<String, Constructor<? extends View>> sConstructorMap = new ArrayMap<>();
    private static final HashMap<String, HashMap<String, Method>> methodMap = new HashMap<>();


    @Nullable
    @Override
    public View onCreateView(@Nullable View parent, @NonNull String name, @NonNull Context context, @NonNull AttributeSet attrs) {

        return onCreateView(name, context, attrs);
    }

    @Nullable
    @Override
    public View onCreateView(@NonNull String name, @NonNull Context context, @NonNull AttributeSet attrs) {

        View view = null;
        if (mViewCreateFactory2 != null) {
            view = mViewCreateFactory2.onCreateView(name, context, attrs);
            if (view == null) {
                view = mViewCreateFactory2.onCreateView(null, name, context, attrs);
            }
        } else if (mViewCreateFactory != null) {
            view = mViewCreateFactory.onCreateView(name, context, attrs);
        }

        if (context instanceof AppCompatActivity) {
            view = ((AppCompatActivity) context).getDelegate().createView(null, name, context, attrs);

        }
        return setViewFloatTips(name, context, attrs, view);
    }

    private View setViewFloatTips(String name, Context context, AttributeSet attrs, View view) {
        TypedArray typedArray = context.obtainStyledAttributes(attrs, R.styleable.FloatTips);
        try {
            if (typedArray.getIndexCount() == 0) {
                // 没有设置任何属性
                return view;
            }
            // 检查是否有tip属性
            String tip = typedArray.getString(R.styleable.FloatTips_float_tips);
            int bgColor = typedArray.getInt(R.styleable.FloatTips_float_tips_theme, 0);
            if (TextUtils.isEmpty(tip)) {
                return view;
            }
            if (view == null) {
                view = createViewFromTag(context, name, attrs);
            }
            if (view == null) {
                return null;
            }
            // 设置鼠标事件
            view.setOnHoverListener(new FloatTipHoverListener(tip, bgColor));
            view.setTag(R.id.tag_float_tip_xml, tip);
            return view;
        } catch (Exception e) {
            e.printStackTrace();
            return view;
        } finally {
            typedArray.recycle();
        }
    }


    private static View createViewFromTag(Context context, String name, AttributeSet attrs) {
        if (TextUtils.isEmpty(name)) {
            return null;
        }
        if (name.equals("view")) {
            name = attrs.getAttributeValue(null, "class");
        }
        try {
            mConstructorArgs[0] = context;
            mConstructorArgs[1] = attrs;

            if (-1 == name.indexOf('.')) {
                View view = null;
                if ("View".equals(name)) {
                    view = createView(context, name, "android.view.");
                }
                if (view == null) {
                    view = createView(context, name, "android.widget.");
                }
                if (view == null) {
                    view = createView(context, name, "android.webkit.");
                }
                return view;
            } else {
                return createView(context, name, null);
            }
        } catch (Exception e) {
            CZURLogUtilsKt.logTagE("BackgroundLibrary", new String[]{"cannot create 【" + name + "】 : "}, e);
            return null;
        } finally {
            mConstructorArgs[0] = null;
            mConstructorArgs[1] = null;
        }


    }

    private static View createView(Context context, String name, String prefix) throws InflateException {
        Constructor<? extends View> constructor = sConstructorMap.get(name);
        try {
            if (constructor == null) {
                Class<? extends View> clazz = context.getClassLoader().loadClass(
                        prefix != null ? (prefix + name) : name).asSubclass(View.class);

                constructor = clazz.getConstructor(sConstructorSignature);
                sConstructorMap.put(name, constructor);
            }
            constructor.setAccessible(true);
            return constructor.newInstance(mConstructorArgs);
        } catch (Exception e) {
            CZURLogUtilsKt.logTagE("BackgroundLibrary", new String[]{"cannot create 【" + name + "】 : "}, e);
            return null;
        }
    }

    public void setInterceptFactory2(LayoutInflater.Factory2 factory2) {
        this.mViewCreateFactory2 = factory2;
    }

    public void setInterceptFactory(LayoutInflater.Factory interceptFactory) {
        this.mViewCreateFactory = interceptFactory;
    }

}
