package com.czur.starry.device.baselib.utils

import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.content.IntentFilter
import android.os.PowerManager
import android.os.SystemClock
import androidx.appcompat.app.AppCompatActivity
import com.czur.czurutils.log.logTagV
import com.czur.starry.device.baselib.base.CZURAtyManager

/**
 * Created by 陈丰尧 on 2024/1/26
 */
private const val TAG = "CZPowerManager"

object CZPowerManager {
    private val pm by lazy(LazyThreadSafetyMode.NONE) {
        val context = CZURAtyManager.appContext
        context.getSystemService(AppCompatActivity.POWER_SERVICE) as PowerManager
    }

    var lastScreenOnTime = 0L
        private set

    /**
     * 屏幕点亮时长
     */
    val screenOnDuration: Long
        get() = SystemClock.elapsedRealtime() - lastScreenOnTime

    fun isScreenOn(): Boolean {
        return pm.isInteractive
    }

    /**
     * 创建一个屏幕常亮的锁
     */
    fun createOneWakeLock(tag: String): PowerManager.WakeLock {
        return pm.newWakeLock(
            PowerManager.ACQUIRE_CAUSES_WAKEUP or PowerManager.SCREEN_BRIGHT_WAKE_LOCK,
            "czPowerManager:$tag"
        ).apply {
            setReferenceCounted(false)
        }

    }

    /**
     * 点亮屏幕
     */
    fun wakeUpScreen(tag: String = "bright") {
        val screenOn = pm.isInteractive
        if (!screenOn) {
            // 点亮屏幕
            val wl = pm.newWakeLock(
                PowerManager.ACQUIRE_CAUSES_WAKEUP or PowerManager.SCREEN_BRIGHT_WAKE_LOCK,
                "czPowerManager:$tag"
            )
            wl.acquire(ONE_SECOND)
            wl.release()
            lastScreenOnTime = SystemClock.elapsedRealtime()
        }
    }

    fun createOneLockScreenReceiver(): LockScreenReceiver {
        return LockScreenReceiver()
    }

    class LockScreenReceiver : BroadcastReceiver() {
        private var onReceiverBlock: ((lock: Boolean) -> Unit)? = null
        private var registerContext: Context? = null
        fun register(context: Context, onReceiverBlock: (lock: Boolean) -> Unit) {
            logTagV(TAG, "注册锁屏广播:${context.javaClass.simpleName}")
            this.onReceiverBlock = onReceiverBlock
            val intentFilter = IntentFilter()
            intentFilter.addAction(Intent.ACTION_SCREEN_OFF)
            intentFilter.addAction(Intent.ACTION_SCREEN_ON)
            context.registerReceiver(this, intentFilter)
            registerContext = context
        }

        fun unregister() {
            logTagV(TAG, "注销锁屏广播:${registerContext?.javaClass?.simpleName}")
            registerContext?.unregisterReceiver(this)
            registerContext = null
        }

        override fun onReceive(context: Context?, intent: Intent?) {
            if (intent?.action == Intent.ACTION_SCREEN_OFF) {
                logTagV(TAG, "锁屏")
                lastScreenOnTime = 0L
                onReceiverBlock?.invoke(true)
            } else if (intent?.action == Intent.ACTION_SCREEN_ON) {
                logTagV(TAG, "解锁")
                lastScreenOnTime = SystemClock.elapsedRealtime()
                onReceiverBlock?.invoke(false)
            }
        }
    }
}

