package com.czur.starry.device.baselib.utils.view

import android.view.View
import com.czur.starry.device.baselib.base.BaseActivity
import com.czur.starry.device.baselib.base.BaseDialog
import com.czur.starry.device.baselib.base.v2.fragment.CZBaseFragment

/**
 * Created by 陈丰尧 on 2022/8/26
 */
inline fun <reified V : View> View.findView(viewID: Int): Lazy<V> {
    return lazy {
        findViewById(viewID)
    }
}

inline fun <reified V : View> BaseActivity.findView(viewID: Int): Lazy<V> {
    return lazy {
        findViewById(viewID)
    }
}

inline fun <reified V : View> CZBaseFragment.findView(viewID: Int): Lazy<V> {
    return lazy {
        requireView().findViewById(viewID)
    }
}

inline fun <reified V : View> BaseDialog.findView(viewID: Int): Lazy<V> {
    return lazy {
        requireView().findViewById(viewID)
    }
}