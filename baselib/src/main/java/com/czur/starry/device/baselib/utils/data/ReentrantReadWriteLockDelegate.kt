package com.czur.starry.device.baselib.utils.data

import com.czur.starry.device.baselib.utils.SimpleLock
import kotlin.properties.ReadWriteProperty
import kotlin.reflect.KProperty

/**
 * Created by 陈丰尧 on 2023/3/7
 */
class ReentrantReadWriteLockDelegate<T>(block: () -> T) : ReadWriteProperty<Any, T> {
    private val simpleLock = SimpleLock()
    private var value: T = block()

    override fun getValue(thisRef: Any, property: KProperty<*>): T {
        return simpleLock.read {
            value!!
        }
    }

    override fun setValue(thisRef: Any, property: KProperty<*>, value: T) {
        simpleLock.write {
            this.value = value
        }
    }
}