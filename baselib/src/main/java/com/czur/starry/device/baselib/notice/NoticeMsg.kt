package com.czur.starry.device.baselib.notice

import android.os.Bundle
import com.czur.czurutils.log.logTagW
import com.czur.starry.device.baselib.utils.createUUID
import java.io.Serializable
import java.util.Locale

/**
 * Created by 陈丰尧 on 4/7/21
 */
private const val TAG = "NoticeMsg"

class NoticeMsg : Serializable {
    companion object {
        @JvmStatic
        private val serialVersionUID = 1L

        // 默认数据的KEY, 基本不会冲突
        private const val DEF_DATA_KEY = "*#defaultData#*"

        private const val KEY_MSG_ID = "*#msgID#*"

        private const val KEY_SYNC_BACK = "*#backMsg#*"
    }

    // 用来存放与消息中心通信的数据
    // TODO 目前先用一个map来存放, 之后看情况可能有所改变
    val data = mutableMapOf<String, Serializable>()


    /**
     * 是否是同步消息的回执消息
     */
    var isBackMsg: Boolean
        set(value) {
            put(KEY_SYNC_BACK, value)
        }
        get() = getBoolean(KEY_SYNC_BACK)

    /**
     * 获取消息ID
     */
    var msgId: String
        set(value) {
            put(KEY_MSG_ID, value)
        }
        get() = get(KEY_MSG_ID) ?: ""

    init {
        // 生成消息ID
        msgId = createUUID().replace("-", "").uppercase(Locale.getDefault())

    }

    fun put(key: String, value: Serializable) {
        data[key] = value
    }

    /**
     * 放默认数据, key是由NoticeMsg内部指定的
     */
    fun put(value: Serializable) = put(DEF_DATA_KEY, value)

    /**
     * 获取默认数据
     * @param key value的key, 如果没有指定,会使用默认key
     */
    fun <T : Serializable> get(key: String = DEF_DATA_KEY): T? {
        if (!data.containsKey(key)) {
            return null
        }
        return (data[key]) as T
    }

    fun getStr(key: String = DEF_DATA_KEY): String = get(key) ?: ""
    fun getInt(key: String = DEF_DATA_KEY): Int = get(key) ?: Int.MIN_VALUE
    fun getLong(key: String = DEF_DATA_KEY): Long = get(key) ?: Long.MIN_VALUE
    fun getBoolean(key: String = DEF_DATA_KEY): Boolean = get(key) ?: false

    // 生成Bundle对象
    fun toBundle(): Bundle {
        val bundle = Bundle()
        data.forEach { (key, value) ->
            when (value) {
                is String -> bundle.putString(key, value)
                is Boolean -> bundle.putBoolean(key, value)
                is Int -> bundle.putInt(key, value)
                is Long -> bundle.putLong(key, value)
                is Short -> bundle.putShort(key, value)
                else -> {
                    logTagW(TAG, "NoticeMsg 目前只支持String, 并不支持${value.javaClass.simpleName}")
                }
            }
        }
        return bundle
    }

    override fun toString(): String {
        return data.toString()
    }
}