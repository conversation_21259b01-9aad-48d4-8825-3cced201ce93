/*
 * Copyright (C) 2012 The Android Open Source Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.czur.starry.device.bluetoothlib.bluetooth;

import static com.czur.starry.device.bluetoothlib.util.BluetoothAdapterProxy.ACTIVE_DEVICE_PHONE_CALL;
import static com.czur.starry.device.bluetoothlib.util.BluetoothProfileProxy.CONNECTION_POLICY_ALLOWED;
import static com.czur.starry.device.bluetoothlib.util.BluetoothProfileProxy.CONNECTION_POLICY_FORBIDDEN;

import android.annotation.SuppressLint;
import android.bluetooth.BluetoothAdapter;
import android.bluetooth.BluetoothClass;
import android.bluetooth.BluetoothDevice;
import android.bluetooth.BluetoothHeadset;
import android.bluetooth.BluetoothProfile;
import android.bluetooth.BluetoothUuid;
import android.content.Context;
import android.os.Build;
import android.os.ParcelUuid;
import android.util.Log;


import com.czur.starry.device.bluetoothlib.R;
import com.czur.starry.device.bluetoothlib.util.BluetoothAdapterProxy;
import com.czur.starry.device.bluetoothlib.util.BluetoothHeadsetProxy;

import java.lang.reflect.Method;
import java.util.ArrayList;
import java.util.List;

/**
 * HeadsetProfile handles Bluetooth HFP and Headset profiles.
 */
public class HeadsetProfile implements LocalBluetoothProfile {
    private static final String TAG = "HeadsetProfile";

    private BluetoothHeadset mService;
    private BluetoothHeadsetProxy mServiceProxy;
    private boolean mIsProfileReady;

    private final CachedBluetoothDeviceManager mDeviceManager;
    private final LocalBluetoothProfileManager mProfileManager;
    private final BluetoothAdapter mBluetoothAdapter;
    private  BluetoothAdapterProxy mBluetoothAdapterProxy;

    static final ParcelUuid[] UUIDS = {
        BluetoothUuid.HSP,
        BluetoothUuid.HFP,
    };

    static final String NAME = "HEADSET";

    // Order of this profile in device profiles list
    private static final int ORDINAL = 0;

    // These callbacks run on the main thread.
    private final class HeadsetServiceListener
            implements BluetoothProfile.ServiceListener {

        public void onServiceConnected(int profile, BluetoothProfile proxy) {
            mService = (BluetoothHeadset) proxy;
            mServiceProxy = new BluetoothHeadsetProxy(mService);
            // We just bound to the service, so refresh the UI for any connected HFP devices.
            @SuppressLint("MissingPermission") List<BluetoothDevice> deviceList = mService.getConnectedDevices();
            while (!deviceList.isEmpty()) {
                BluetoothDevice nextDevice = deviceList.remove(0);
                CachedBluetoothDevice device = mDeviceManager.findDevice(nextDevice);
                // we may add a new device here, but generally this should not happen
                if (device == null) {
                    Log.w(TAG, "HeadsetProfile found new device: " + nextDevice);
                    device = mDeviceManager.addDevice(nextDevice);
                }
                device.onProfileStateChanged(HeadsetProfile.this,
                        BluetoothProfile.STATE_CONNECTED);
                device.refresh();
            }
            mIsProfileReady=true;
            mProfileManager.callServiceConnectedListeners();
        }

        public void onServiceDisconnected(int profile) {
            mProfileManager.callServiceDisconnectedListeners();
            mIsProfileReady=false;
        }
    }

    public boolean isProfileReady() {
        return mIsProfileReady;
    }

    @Override
    public int getProfileId() {
        return BluetoothProfile.HEADSET;
    }

    HeadsetProfile(Context context, CachedBluetoothDeviceManager deviceManager,
            LocalBluetoothProfileManager profileManager) {
        mDeviceManager = deviceManager;
        mProfileManager = profileManager;
        mBluetoothAdapter = BluetoothAdapter.getDefaultAdapter();
        mBluetoothAdapter.getProfileProxy(context, new HeadsetServiceListener(),
                BluetoothProfile.HEADSET);
        mBluetoothAdapterProxy = new BluetoothAdapterProxy(mBluetoothAdapter);
    }

    public boolean accessProfileEnabled() {
        return true;
    }

    public boolean isAutoConnectable() {
        return true;
    }

    @SuppressLint("MissingPermission")
    public int getConnectionStatus(BluetoothDevice device) {
        if (mService == null) {
            return BluetoothProfile.STATE_DISCONNECTED;
        }
        return mService.getConnectionState(device);
    }

    public boolean setActiveDevice(BluetoothDevice device) {
        if (mBluetoothAdapter == null) {
            return false;
        }

        return device == null
                ? mBluetoothAdapterProxy.removeActiveDevice(ACTIVE_DEVICE_PHONE_CALL)
                : mBluetoothAdapterProxy.setActiveDevice(device, ACTIVE_DEVICE_PHONE_CALL);
    }

    public BluetoothDevice getActiveDevice() {
        if (Build.VERSION.SDK_INT >= 33) {
            if (mBluetoothAdapter == null) {
                return null;
            }
            try {
                Class<?> bluetoothAdapterClass = mBluetoothAdapter.getClass();
                Method getActiveDevicesMethod = bluetoothAdapterClass.getDeclaredMethod("getActiveDevices", int.class);
                getActiveDevicesMethod.setAccessible(true);

                final List<BluetoothDevice> activeDevices = (List<BluetoothDevice>) getActiveDevicesMethod.invoke(mBluetoothAdapter, BluetoothProfile.HEADSET);
                return (activeDevices.size() > 0) ? activeDevices.get(0) : null;
            } catch (Exception e) {
                throw new RuntimeException(e);
            }

        } else {
            if (mService == null) {
                return null;
            }
            return mServiceProxy.getActiveDevice();
        }

    }

    public boolean isAudioOn() {
        if (mService == null) {
            return false;
        }
        return mServiceProxy.isAudioOn();
    }

    public int getAudioState(BluetoothDevice device) {
        if (mService == null) {
            return BluetoothHeadset.STATE_AUDIO_DISCONNECTED;
        }
        return mServiceProxy.getAudioState(device);
    }

    @Override
    public boolean isEnabled(BluetoothDevice device) {
        if (mService == null) {
            return false;
        }
        return mServiceProxy.getConnectionPolicy(device) > CONNECTION_POLICY_FORBIDDEN;
    }

    @Override
    public int getConnectionPolicy(BluetoothDevice device) {
        if (mService == null) {
            return CONNECTION_POLICY_FORBIDDEN;
        }
        return mServiceProxy.getConnectionPolicy(device);
    }

    @Override
    public boolean setEnabled(BluetoothDevice device, boolean enabled) {
        boolean isEnabled = false;
        if (mService == null) {
            return false;
        }
        if (enabled) {
            if (mServiceProxy.getConnectionPolicy(device) < CONNECTION_POLICY_ALLOWED) {
                isEnabled = mServiceProxy.setConnectionPolicy(device, CONNECTION_POLICY_ALLOWED);
            }
        } else {
            isEnabled = mServiceProxy.setConnectionPolicy(device, CONNECTION_POLICY_FORBIDDEN);
        }

        return isEnabled;
    }
    @SuppressLint("MissingPermission")
    public List<BluetoothDevice> getConnectedDevices() {
        if (mService == null) {
            return new ArrayList<BluetoothDevice>(0);
        }
        return mService.getDevicesMatchingConnectionStates(
              new int[] {BluetoothProfile.STATE_CONNECTED,
                         BluetoothProfile.STATE_CONNECTING,
                         BluetoothProfile.STATE_DISCONNECTING});
    }

    public String toString() {
        return NAME;
    }

    public int getOrdinal() {
        return ORDINAL;
    }

    public int getNameResource(BluetoothDevice device) {
        return 0;//R.string.bluetooth_profile_headset;
    }

    public int getSummaryResourceForDevice(BluetoothDevice device) {
        int state = getConnectionStatus(device);
        switch (state) {
            case BluetoothProfile.STATE_DISCONNECTED:
                return 0;//R.string.bluetooth_headset_profile_summary_use_for;

            case BluetoothProfile.STATE_CONNECTED:
                return 0;//R.string.bluetooth_headset_profile_summary_connected;

            default:
                return BluetoothUtils.getConnectionStateSummary(state);
        }
    }

    public int getDrawableResource(BluetoothClass btClass) {
        return com.android.internal.R.drawable.ic_bt_headset_hfp;
    }

    protected void finalize() {
        Log.d(TAG, "finalize()");
        if (mService != null) {
            try {
                BluetoothAdapter.getDefaultAdapter().closeProfileProxy(BluetoothProfile.HEADSET,
                                                                       mService);
                mService = null;
            }catch (Throwable t) {
                Log.w(TAG, "Error cleaning up HID proxy", t);
            }
        }
    }
}
