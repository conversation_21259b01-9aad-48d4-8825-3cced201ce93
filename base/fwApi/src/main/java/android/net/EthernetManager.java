package android.net;

/**
 * Created by 陈丰尧 on 2024/7/5
 */
public class EthernetManager {
    /**
     * 获取IP地址
     *
     * @param iface 网卡名称 @see getAvailableInterfaces
     * @return IP地址
     */
    public String getIpAddress(String iface) {
        return "";
    }

    /**
     * 获取子网掩码
     *
     * @param iface 网卡名称 @see getAvailableInterfaces
     * @return 子网掩码
     */
    public String getNetmask(String iface) {
        return "";
    }

    /**
     * 获取网关
     *
     * @param iface 网卡名称 @see getAvailableInterfaces
     * @return 网关
     */
    public String getGateway(String iface) {
        return null;
    }

    /**
     * 获取DNS
     *
     * @param iface 网卡名称 @see getAvailableInterfaces
     * @return DNS
     */
    public String getDns(String iface) {
        return null;
    }

    /**
     * 获取网卡名称
     *
     * @return 网卡名称 默认是eth0
     */
    public String[] getAvailableInterfaces() {
        return new String[0];
    }

    /**
     * 获取配置文件
     */
    public IpConfiguration getConfiguration(String iface) {
        return null;
    }

    /**
     * 设置配置文件
     *
     * @param iface  网卡名称 @see getAvailableInterfaces
     * @param config 配置文件
     */
    public void setConfiguration(String iface, IpConfiguration config) {

    }

    public boolean isAvailable() {
        return false;
    }

    public boolean isAvailable(String iface) {
        return false;
    }

    /**
     * Indicates whether the interface is up
     */
    public boolean isInterfaceup(String iface) {
        return false;
    }
}
