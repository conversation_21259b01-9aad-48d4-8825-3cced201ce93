package android.os.storage;

import android.content.Context;
import android.content.Intent;
import android.os.Parcel;
import android.os.Parcelable;
import android.util.IndentingPrintWriter;

import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

import java.io.File;
import java.util.Comparator;

/**
 * Created by 陈丰尧 on 2024/12/6
 */
public class VolumeInfo implements Parcelable {
    public static int TYPE_PUBLIC = 0;
    public static int TYPE_PRIVATE = 0;
    public static int TYPE_EMULATED = 0;
    public static int TYPE_ASEC = 0;
    public static int TYPE_OBB = 0;
    public static int TYPE_STUB = 0;

    public static int STATE_UNMOUNTED = 0;
    public static int STATE_CHECKING = 0;
    public static int STATE_MOUNTED = 0;
    public static int STATE_MOUNTED_READ_ONLY = 0;
    public static int STATE_FORMATTING = 0;
    public static int STATE_EJECTING = 0;
    public static int STATE_UNMOUNTABLE = 0;
    public static int STATE_REMOVED = 0;
    public static int STATE_BAD_REMOVAL = 0;

    public static int MOUNT_FLAG_PRIMARY = 0;
    public static int MOUNT_FLAG_VISIBLE = 0;


    public String id;

    public int type;

    public DiskInfo disk = null;
    public String partGuid;
    public int mountFlags = 0;
    public int mountUserId = 0;

    public int state = 0;
    public String fsType;

    public String fsUuid;

    public String fsLabel;

    public String path;

    public String internalPath;

    protected VolumeInfo(Parcel in) {

    }

    public static final Creator<VolumeInfo> CREATOR = new Creator<VolumeInfo>() {
        @Override
        public VolumeInfo createFromParcel(Parcel in) {
            return new VolumeInfo(in);
        }

        @Override
        public VolumeInfo[] newArray(int size) {
            return new VolumeInfo[size];
        }
    };

    public @Nullable DiskInfo getDisk() {
        throw new RuntimeException("Stub!");
    }

    public static String getEnvironmentForState(int state) {
        throw new RuntimeException("Stub!");
    }

    public static @Nullable String getBroadcastForEnvironment(String envState) {
        throw new RuntimeException("Stub!");
    }

    public static @Nullable String getBroadcastForState(int state) {
        throw new RuntimeException("Stub!");
    }

    public static Comparator<VolumeInfo> getDescriptionComparator() {
        throw new RuntimeException("Stub!");
    }

    public String getId() {
        throw new RuntimeException("Stub!");
    }

    public @Nullable String getDiskId() {
        throw new RuntimeException("Stub!");
    }

    public int getType() {
        throw new RuntimeException("Stub!");
    }

    public int getState() {
        throw new RuntimeException("Stub!");
    }

    public int getStateDescription() {
        throw new RuntimeException("Stub!");
    }

    public @Nullable String getFsUuid() {
        return fsUuid;
    }

    public @Nullable String getNormalizedFsUuid() {
        throw new RuntimeException("Stub!");
    }

    public int getMountUserId() {
        return mountUserId;
    }

    public boolean isMountedReadable() {
        throw new RuntimeException("Stub!");
    }

    public boolean isMountedWritable() {
        throw new RuntimeException("Stub!");
    }

    public boolean isPrimary() {
        throw new RuntimeException("Stub!");
    }

    public boolean isPrimaryPhysical() {
        throw new RuntimeException("Stub!");
    }

    public boolean isVisible() {
        throw new RuntimeException("Stub!");
    }

    public boolean isVisibleForUser(int userId) {
        throw new RuntimeException("Stub!");
    }

    /**
     * Returns {@code true} if this volume is the primary emulated volume for {@code userId},
     * {@code false} otherwise.
     */
    public boolean isPrimaryEmulatedForUser(int userId) {
        throw new RuntimeException("Stub!");
    }

    public boolean isVisibleForRead(int userId) {
        throw new RuntimeException("Stub!");
    }

    public boolean isVisibleForWrite(int userId) {
        throw new RuntimeException("Stub!");
    }

    public File getPath() {
        throw new RuntimeException("Stub!");
    }

    public File getInternalPath() {
        throw new RuntimeException("Stub!");
    }

    public File getPathForUser(int userId) {
        throw new RuntimeException("Stub!");
    }

    /**
     * Path which is accessible to apps holding
     */
    public File getInternalPathForUser(int userId) {
        throw new RuntimeException("Stub!");
    }

    public StorageVolume buildStorageVolume(Context context, int userId, boolean reportUnmounted) {
        throw new RuntimeException("Stub!");
    }

    public static int buildStableMtpStorageId(String fsUuid) {
        throw new RuntimeException("Stub!");
    }

    /**
     * Build an intent to browse the contents of this volume. Only valid for
     * {@link #TYPE_EMULATED} or {@link #TYPE_PUBLIC}.
     */
    public @Nullable Intent buildBrowseIntent() {
        throw new RuntimeException("Stub!");
    }

    public @Nullable Intent buildBrowseIntentForUser(int userId) {
        throw new RuntimeException("Stub!");
    }

    public void dump(IndentingPrintWriter pw) {
        throw new RuntimeException("Stub!");
    }

    @Override
    public int describeContents() {
        return 0;
    }

    @Override
    public void writeToParcel(@NotNull Parcel parcel, int i) {

    }
}
