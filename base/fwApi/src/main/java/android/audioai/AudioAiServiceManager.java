package android.audioai;

import android.content.Context;

import androidx.annotation.IntDef;
import androidx.annotation.NonNull;

import java.util.List;
import java.util.concurrent.Executor;

/**
 * Created by 陈丰尧 on 2025/1/18
 */
public class AudioAiServiceManager implements AudioAiServiceCallback, InteractionAiServiceCallback {

    public static final int ASR_LOCAL = 1;
    public static final int ASR_LOCAL_REMOTE = 2;
    public static final int ASR_REMOTE = 3;

    @IntDef({ASR_LOCAL, ASR_LOCAL_REMOTE, ASR_REMOTE})
    private @interface Mode {
    }


    public AudioAiServiceManager(Context ctx) {
        throw new UnsupportedOperationException("Not supported by FW");
    }

    /**
     * 获取语音助手的状态
     *
     * @return 1: 开启，0: 关闭
     */
    public int getInteractionStageStatus() {
        throw new UnsupportedOperationException("Not supported by FW");
    }

    /**
     * 设置语音助手的状态
     *
     * @param status 1: 开启，0: 关闭
     */
    public void interactionKwsStatus(int status) {
        throw new UnsupportedOperationException("Not supported by FW");
    }

    /**
     * 获取语音助手总开关
     *
     * @return 1: 开启，0: 关闭
     */
    public int getInteractionStatus() {
        throw new UnsupportedOperationException("Not supported by FW");
    }

    /**
     * Tts语音播报状态
     *
     * @return 1: 开启，0: 关闭
     */
    public int getTtsStatus() {
        throw new UnsupportedOperationException("Not supported by FW");
    }
    /**
     * 设置Tts语音播报状态
     *
     * @param status 1: 开启，0: 关闭
     */
    public void setTtsStatus(int status) {
        throw new UnsupportedOperationException("Not supported by FW");
    }

    /**
     * 设置语音助手总开关
     *
     * @param status 1: 开启，0: 关闭
     */
    public void setInteractionStatus(int status) {
        throw new UnsupportedOperationException("Not supported by FW");
    }


    public void registerCallback(Executor executor, @NonNull AudioAiServiceCallback callback) {
        throw new UnsupportedOperationException("Not supported by FW");
    }

    public void registerCallback(@NonNull AudioAiServiceCallback callback) {
        throw new UnsupportedOperationException("Not supported by FW");
    }

    public void unregisterCallback(@NonNull AudioAiServiceCallback callback) {
        throw new UnsupportedOperationException("Not supported by FW");
    }

    public void registerCallback(@NonNull InteractionAiServiceCallback callback) {
        throw new UnsupportedOperationException("Not supported by FW");
    }

    public void unregisterCallback(@NonNull InteractionAiServiceCallback callback) {
        throw new UnsupportedOperationException("Not supported by FW");
    }

    @Override
    public void onWakeUp(String meta) {

    }

    @Override
    public void onStreamStoped(String meta) {

    }

    @Override
    public void onAudioMeta(String metaJson) {

    }

    @Override
    public void onInstruct(String instructJson) {

    }

    @Override
    public void onChatMsg(String msgJson) {

    }

    @Override
    public void onTtS(byte[] audioData, String meta) {

    }

    @Override
    public void onError(int errorCode, String errorMsg) {

    }

    @Override
    public void onMicAmp(String json) {

    }

    @Override
    public void onFirstStageWakeUp(String meta) {

    }

    @NonNull
    public void interactionTrigger(boolean trigger) {
        // 手动触发小星同学
        throw new UnsupportedOperationException("Not supported by FW");
    }

    @Override
    public void onAudioAsrResult(String result, String roleres) {

    }

    @Override
    public void onAudioTranslateResult(String result, String roleres) {

    }

    @Override
    public void onAudioAiError(int errorCode, @NonNull String errorMsg) {
        throw new UnsupportedOperationException("Not supported by FW");
    }

    public void init(@NonNull String userId,boolean isPrd) {
        throw new UnsupportedOperationException("Not supported by FW");
    }

    public void unInit() {
        throw new UnsupportedOperationException("Not supported by FW");
    }

    @NonNull
    public List<String> getAsrLangs() {
        throw new UnsupportedOperationException("Not supported by FW");
    }

    @NonNull
    public List<String> getTranslateOrigLangs() {
        throw new UnsupportedOperationException("Not supported by FW");
    }

    @NonNull
    public List<String> getTranslateTargetLangs() {
        throw new UnsupportedOperationException("Not supported by FW");
    }

    // 是否生成记录
    public void setSummaryEnabled(boolean enabled) {
        throw new UnsupportedOperationException("Not supported by FW");
    }

    // 设置文件名称
    public void setAsrName(String asrName) {
        throw new UnsupportedOperationException("Not supported by FW");
    }

    // 设置时区
    public void setTimezone(String timezone) {
        throw new UnsupportedOperationException("Not supported by FW");
    }

    public void setAsrMode(@Mode int mode) {
        throw new UnsupportedOperationException("Not supported by FW");
    }

    public void setTranslateEnabled(boolean enable) {
        throw new UnsupportedOperationException("Not supported by FW");
    }

    public void setAsrLang(@NonNull String lang) {
        throw new UnsupportedOperationException("Not supported by FW");
    }

    public void setTranslateLang(@NonNull String origLang, @NonNull String targetLang) {
        throw new UnsupportedOperationException("Not supported by FW");
    }

    public void startAsr() {
        throw new UnsupportedOperationException("Not supported by FW");
    }

    public void stopAsr() {
        throw new UnsupportedOperationException("Not supported by FW");
    }


    public void interactionInit(String serviceId, boolean isPrd) {
        throw new UnsupportedOperationException("Not supported by FW");
    }

    // Method to unregister the service
    public void interactionUnInit() {
        throw new UnsupportedOperationException("Not supported by FW");
    }

    public void interactionStart() {
        throw new UnsupportedOperationException("Not supported by FW");
    }

    public void interactionStop() {
        throw new UnsupportedOperationException("Not supported by FW");
    }

    public void setPlayStatus(int status) {
        // 设置小星播报状态，开始播报为1，停止播报为0
        throw new UnsupportedOperationException("Not supported by FW");
    }

    public String getSessionId() {
        throw new UnsupportedOperationException("Not supported by FW");
    }
}
