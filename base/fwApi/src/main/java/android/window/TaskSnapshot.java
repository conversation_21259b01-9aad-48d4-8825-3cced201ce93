package android.window;

import android.graphics.ColorSpace;
import android.graphics.Point;
import android.hardware.HardwareBuffer;
import android.os.Parcel;
import android.os.Parcelable;

/**
 * author : <PERSON><PERSON><PERSON>
 * time   :2025/01/04
 */


public class TaskSnapshot implements Parcelable {
    private  HardwareBuffer mSnapshot;
    private  ColorSpace mColorSpace;
    private  Point mTaskSize;

    protected TaskSnapshot(Parcel in) {
    }

    @Override
    public void writeToParcel(Parcel dest, int flags) {
    }

    @Override
    public int describeContents() {
        return 0;
    }

    public static final Creator<TaskSnapshot> CREATOR = new Creator<TaskSnapshot>() {
        @Override
        public TaskSnapshot createFromParcel(Parcel in) {
            return new TaskSnapshot(in);
        }

        @Override
        public TaskSnapshot[] newArray(int size) {
            return new TaskSnapshot[size];
        }
    };

    public HardwareBuffer getHardwareBuffer() {
        return this.mSnapshot;
    }

    public ColorSpace getColorSpace() {
        return this.mColorSpace;
    }

    public Point getTaskSize() {
        return this.mTaskSize;
    }
}
