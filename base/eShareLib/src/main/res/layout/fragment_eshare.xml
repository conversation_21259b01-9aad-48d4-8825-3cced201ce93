<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:ignore="PxUsage,RtlHardcoded">

    <View
        android:id="@+id/guideTopView"
        android:layout_width="match_parent"
        android:layout_height="280px"
        android:background="#5879FC"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"/>

    <!--  右上角的设置  -->
    <TextView
        android:id="@+id/miracastSwitchLabelTv"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginRight="20px"
        android:text="@string/str_title_miracast"
        android:textColor="@color/white"
        android:textSize="24px"
        android:textStyle="bold"
        app:layout_constraintBottom_toBottomOf="@id/miracastSwitch"
        app:layout_constraintRight_toLeftOf="@id/miracastSwitch"
        app:layout_constraintTop_toTopOf="@id/miracastSwitch" />

    <com.czur.starry.device.baselib.widget.toggle.SwitchIcon
        android:id="@+id/miracastSwitch"
        android:layout_width="94px"
        android:layout_height="44px"
        android:layout_marginTop="33px"
        android:layout_marginRight="60px"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/miracastDisableHintTv"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:alpha="0.5"
        android:text="@string/str_miracast_disable_hint"
        android:textColor="@color/white"
        android:textSize="@dimen/miracastDisableHintTvSize"
        app:layout_constraintLeft_toLeftOf="@id/miracastSwitchLabelTv"
        app:layout_constraintTop_toBottomOf="@id/miracastSwitch" />

    <TextView
        android:id="@+id/airplaySwitchLabelTv"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/str_title_airplay"
        android:textColor="@color/white"
        android:textSize="24px"
        android:textStyle="bold"
        app:layout_constraintBottom_toBottomOf="@id/airplaySwitch"
        app:layout_constraintLeft_toLeftOf="@id/miracastSwitchLabelTv"
        app:layout_constraintTop_toTopOf="@id/airplaySwitch" />

    <com.czur.starry.device.baselib.widget.toggle.SwitchIcon
        android:id="@+id/airplaySwitch"
        android:layout_width="94px"
        android:layout_height="44px"
        android:layout_marginTop="12px"
        android:layout_marginRight="60px"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@id/miracastDisableHintTv"
        />

    <TextView
        android:id="@+id/dlnaSwitchLabelTv"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/str_title_dlna"
        android:textColor="@color/white"
        android:textSize="24px"
        android:textStyle="bold"
        app:layout_constraintBottom_toBottomOf="@id/dlnaSwitch"
        app:layout_constraintLeft_toLeftOf="@id/miracastSwitchLabelTv"
        app:layout_constraintTop_toTopOf="@id/dlnaSwitch" />

    <com.czur.starry.device.baselib.widget.toggle.SwitchIcon
        android:id="@+id/dlnaSwitch"
        android:layout_width="94px"
        android:layout_height="44px"
        android:layout_marginTop="12px"
        android:layout_marginRight="60px"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@id/airplaySwitch"/>

    <!--  更多设置  -->
    <TextView
        android:id="@+id/moreSettingTv"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="22px"
        android:text="@string/str_more_setting"
        android:textColor="@color/white"
        android:textSize="24px"
        android:textStyle="bold"
        app:layout_constraintLeft_toLeftOf="@id/miracastSwitchLabelTv"
        app:layout_constraintTop_toBottomOf="@id/dlnaSwitch" />

    <ImageView
        android:id="@+id/moreSettingIv"
        android:layout_width="16px"
        android:layout_height="28px"
        android:layout_marginLeft="36px"
        android:src="@drawable/ic_more_setting"
        app:layout_constraintLeft_toRightOf="@id/moreSettingTv"
        app:layout_constraintTop_toTopOf="@id/moreSettingTv"
        app:layout_constraintBottom_toBottomOf="@id/moreSettingTv"/>


    <androidx.constraintlayout.helper.widget.Layer
        android:id="@+id/moreSettingLayer"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        app:constraint_referenced_ids="moreSettingTv,moreSettingIv" />

    <!--  标题  -->
    <androidx.constraintlayout.utils.widget.ImageFilterView
        android:id="@+id/deviceImageFilter"
        android:layout_width="780px"
        android:layout_height="72px"
        android:layout_marginTop="60px"
        android:alpha="0.2"
        android:background="@color/black"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:roundPercent="1"
        />

    <TextView
        android:id="@+id/deviceNameLabelTv"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginLeft="32px"
        android:text="@string/str_device_name"
        android:textColor="@color/white"
        android:textSize="20px"
        android:textStyle="normal"
        app:layout_constraintLeft_toLeftOf="@id/deviceImageFilter"
        app:layout_constraintBottom_toBottomOf="@id/deviceImageFilter"
        app:layout_constraintTop_toTopOf="@id/deviceImageFilter" />

    <ImageView
        android:id="@+id/deviceNameEditIv"
        android:layout_width="32px"
        android:layout_height="32px"
        android:layout_marginLeft="20px"
        android:src="@drawable/ic_device_name_edit"
        app:layout_constraintBottom_toBottomOf="@id/deviceImageFilter"
        app:layout_constraintLeft_toRightOf="@id/deviceImageFilter"
        app:layout_constraintTop_toTopOf="@id/deviceImageFilter" />

    <EditText
        android:id="@+id/deviceNameEt"
        android:layout_width="602px"
        android:layout_height="46px"
        android:layout_marginRight="32px"
        android:gravity="center"
        android:imeOptions="actionDone"
        android:includeFontPadding="false"
        android:inputType="textNoSuggestions"
        android:nextFocusDown="@id/deviceNameEt"
        android:scrollHorizontally="true"
        android:singleLine="true"
        android:textColor="@color/white"
        android:textSize="30px"
        android:background="@null"
        app:layout_constraintRight_toRightOf="@id/deviceImageFilter"
        app:layout_constraintBottom_toBottomOf="@id/deviceImageFilter"
        app:layout_constraintTop_toTopOf="@id/deviceImageFilter"
        tools:background="#3317283F" />

    <TextView
        android:id="@+id/deviceNameTv"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginLeft="15px"
        android:layout_marginRight="15px"
        android:includeFontPadding="false"
        android:textColor="@color/white"
        android:textSize="30px"
        android:textStyle="bold"
        android:maxWidth="620px"
        android:ellipsize="middle"
        android:singleLine="true"
        app:layout_constraintLeft_toRightOf="@id/deviceNameLabelTv"
        app:layout_constraintRight_toRightOf="@id/deviceImageFilter"
        app:layout_constraintTop_toTopOf="@id/deviceImageFilter"
        app:layout_constraintBottom_toBottomOf="@id/deviceImageFilter"
        tools:text="FELIX" />


    <ImageView
        android:id="@+id/editConfirmIv"
        android:layout_width="32px"
        android:layout_height="32px"
        android:layout_marginLeft="20px"
        android:src="@drawable/ic_edit_confirm"
        app:layout_constraintBottom_toBottomOf="@id/deviceImageFilter"
        app:layout_constraintLeft_toRightOf="@id/deviceImageFilter"
        app:layout_constraintTop_toTopOf="@id/deviceImageFilter" />

    <ImageView
        android:id="@+id/editCancelIv"
        android:layout_width="32px"
        android:layout_height="32px"
        android:layout_marginLeft="20px"
        android:src="@drawable/ic_edit_cancel"
        app:layout_constraintLeft_toRightOf="@id/editConfirmIv"
        app:layout_constraintTop_toTopOf="@id/editConfirmIv" />

    <androidx.constraintlayout.widget.Group
        android:id="@+id/editGroup"
        android:layout_width="0px"
        android:layout_height="0px"
        app:constraint_referenced_ids="deviceNameEt,editConfirmIv,editCancelIv" />

    <!--  网络信息  -->
    <androidx.constraintlayout.utils.widget.ImageFilterView
        android:id="@+id/wifiImageFilter"
        android:layout_width="780px"
        android:layout_height="72px"
        android:layout_marginTop="16px"
        android:alpha="0.2"
        android:background="@color/black"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/deviceImageFilter"
        app:layout_constraintRight_toRightOf="parent"
        app:roundPercent="1"
        />

    <TextView
        android:id="@+id/networkInfoLabelTv"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginLeft="32px"
        android:text="@string/str_info_ssid_label"
        android:textColor="@color/white"
        android:textSize="20px"
        android:textStyle="normal"
        app:layout_constraintLeft_toLeftOf="@id/wifiImageFilter"
        app:layout_constraintBottom_toBottomOf="@id/wifiImageFilter"
        app:layout_constraintTop_toTopOf="@id/wifiImageFilter" />

    <Space
        android:id="@+id/networkSpace"
        android:layout_width="wrap_content"
        android:layout_height="46px"
        android:maxWidth="602px"
        android:layout_marginRight="32px"
        app:layout_constraintRight_toRightOf="@id/wifiImageFilter"
        app:layout_constraintBottom_toBottomOf="@id/wifiImageFilter"
        app:layout_constraintTop_toTopOf="@id/wifiImageFilter"
        app:layout_constraintLeft_toRightOf="@id/networkInfoLabelTv"
        tools:background="#3317283F" />

    <TextView
        android:id="@+id/ssidNameTv"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:textColor="@color/white"
        android:textSize="30px"
        android:gravity="center"
        android:textStyle="bold"
        android:ellipsize="middle"
        android:singleLine="true"
        android:maxWidth="300dp"
        app:layout_constraintLeft_toLeftOf="@id/networkSpace"
        app:layout_constraintTop_toTopOf="@id/networkSpace"
        app:layout_constraintBottom_toBottomOf="@id/networkSpace"
        app:layout_constraintRight_toLeftOf="@id/ipTv"
        tools:text="CZUR-5Gw-ThisIsAV22222222222222222222222" />

    <TextView
        android:id="@+id/ipTv"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:textColor="@color/white"
        android:textSize="18px"
        android:gravity="center"
        android:textStyle="normal"
        android:layout_marginLeft="20px"
        app:layout_constraintLeft_toRightOf="@id/ssidNameTv"
        app:layout_constraintRight_toRightOf="@id/networkSpace"
        app:layout_constraintTop_toTopOf="@id/networkSpace"
        app:layout_constraintBottom_toBottomOf="@id/networkSpace"
        tools:text="IP: ***************" />


    <TextView
        android:id="@+id/noWifiTv"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginLeft="15px"
        android:layout_marginRight="15px"
        android:includeFontPadding="false"
        android:textColor="@color/white"
        android:textSize="30px"
        android:textStyle="bold"
        android:text="@string/str_no_network_ssid"
        android:visibility="gone"
        app:layout_constraintLeft_toRightOf="@id/networkInfoLabelTv"
        app:layout_constraintRight_toRightOf="@id/wifiImageFilter"
        app:layout_constraintTop_toTopOf="@id/wifiImageFilter"
        app:layout_constraintBottom_toBottomOf="@id/wifiImageFilter"
        tools:text="@string/str_no_network_ssid" />

    <ImageView
        android:id="@+id/networkSettingIv"
        android:layout_width="32px"
        android:layout_height="32px"
        android:layout_marginLeft="20px"
        android:src="@drawable/ic_setting_network"
        app:layout_constraintBottom_toBottomOf="@id/wifiImageFilter"
        app:layout_constraintLeft_toRightOf="@id/wifiImageFilter"
        app:layout_constraintTop_toTopOf="@id/wifiImageFilter" />

    <!--  二维码  -->
    <ImageView
        android:id="@+id/guideBottomView"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:src="@drawable/img_bottom_bg"
        app:layout_constraintTop_toBottomOf="@id/guideTopView"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        />

    <androidx.constraintlayout.utils.widget.ImageFilterView
        android:id="@+id/qrCodeIv"
        android:layout_width="152px"
        android:layout_height="152px"
        android:layout_marginRight="60px"
        android:layout_marginTop="24px"
        android:background="@color/white"
        android:padding="10px"
        android:indeterminateTint="@color/black"
        android:indeterminateTintMode="src_atop"
        app:layout_constraintTop_toTopOf="@+id/guideBottomView"
        app:layout_constraintRight_toRightOf="parent"
        app:round="10px" />

    <TextView
        style="@style/tv_qrcode_label_Style"
        android:gravity="center"
        android:text="@string/str_qrcode_label"
        android:textColor="@color/text_common"
        android:textSize="16px"
        android:textStyle="bold"
        app:layout_constraintLeft_toLeftOf="@id/qrCodeIv"
        app:layout_constraintRight_toRightOf="@id/qrCodeIv"
        app:layout_constraintTop_toBottomOf="@+id/qrCodeIv" />

    <TextView
        android:id="@+id/guideTv"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="40px"
        android:layout_marginLeft="132px"
        android:text="@string/str_use_guide"
        android:textColor="@color/text_common"
        android:textSize="52px"
        android:textStyle="bold"
        app:layout_constraintLeft_toLeftOf="@id/guideTopView"
        app:layout_constraintTop_toBottomOf="@id/guideTopView" />

    <TextView
        android:id="@+id/p2pHintSpaceTv"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginLeft="46px"
        android:layout_marginTop="8px"
        android:ellipsize="end"
        android:includeFontPadding="false"
        android:maxWidth="350px"
        android:singleLine="true"
        android:text="@string/str_float_tips_device_count_hint"
        android:textColor="@color/transparent"
        android:textSize="24px"
        android:textStyle="bold"
        app:layout_constraintLeft_toLeftOf="@id/guideTv"
        app:layout_constraintTop_toBottomOf="@id/guideTv"/>

    <TextView
        android:id="@+id/p2pHintTv"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginLeft="46px"
        android:layout_marginTop="8px"
        android:ellipsize="end"
        android:includeFontPadding="false"
        android:maxWidth="350px"
        android:text="@string/str_float_tips_device_count_hint"
        android:textColor="@color/text_common"
        android:textStyle="normal"
        android:textSize="24px"
        app:layout_constraintLeft_toLeftOf="@id/guideTv"
        app:layout_constraintTop_toBottomOf="@id/guideTv"/>

    <TextView
        android:id="@+id/p2pHintSummaryTv"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/str_float_tips_device_content_hint"
        android:textColor="@color/text_common"
        android:textSize="24px"
        android:textStyle="normal"
        app:layout_constraintTop_toTopOf="@id/p2pHintTv"
        app:layout_constraintBottom_toBottomOf="@id/p2pHintTv"
        app:layout_constraintLeft_toRightOf="@id/p2pHintTv" />


    <ImageView
        android:id="@+id/tipsIcon"
        android:layout_width="30px"
        android:layout_height="30px"
        android:layout_marginRight="16px"
        android:src="@drawable/ic_device_count_hint_blue"
        app:layout_constraintRight_toLeftOf="@id/p2pHintSpaceTv"
        app:layout_constraintTop_toTopOf="@id/p2pHintSpaceTv"
        app:layout_constraintBottom_toBottomOf="@id/p2pHintSpaceTv"/>

    <TextView
        android:id="@+id/p2pHintSSIDTv"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:alpha="0.5"
        android:textColor="@color/white"
        android:textSize="20px"
        android:visibility="gone"
        app:layout_constraintBottom_toTopOf="@id/p2pHintPwdTv"
        app:layout_constraintRight_toRightOf="@id/p2pHintTv" />

    <TextView
        android:id="@+id/p2pHintPwdTv"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:alpha="0.5"
        android:textColor="@color/white"
        android:textSize="20px"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="@id/guideTv"
        app:layout_constraintRight_toRightOf="@id/p2pHintTv" />

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/guideCategoryRv"
        android:layout_width="472px"
        android:layout_height="538px"
        android:layout_marginLeft="178px"
        android:layout_marginBottom="38px"
        app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent" />

    <FrameLayout
        android:id="@+id/contentFl"
        android:layout_width="1209px"
        android:layout_height="538px"
        android:layout_marginBottom="38px"
        app:layout_constraintLeft_toRightOf="@id/guideCategoryRv"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintRight_toRightOf="parent"/>

</androidx.constraintlayout.widget.ConstraintLayout>