<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="70px"
    tools:background="@color/base_bg_color"
    tools:ignore="PxUsage,RtlHardcoded">

    <View
        android:id="@+id/guideCategorySelView"
        android:layout_width="400px"
        android:layout_height="60px"
        android:background="@drawable/drawable_guide_category_sel"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <ImageView
        android:id="@+id/guideCategoryIconIv"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginLeft="61px"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:src="@drawable/ic_category_apple" />

    <TextView
        android:id="@+id/guideCategoryTitleTv"
        android:layout_width="380px"
        android:layout_height="wrap_content"
        android:layout_marginLeft="112px"
        android:textColor="@color/text_common"
        android:textSize="24px"
        android:textStyle="bold"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <View
        android:id="@+id/viewHover"
        android:layout_width="match_parent"
        android:layout_height="60px"
        android:background="@drawable/drawable_guide_category_cursor"
        android:visibility="invisible"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

</androidx.constraintlayout.widget.ConstraintLayout>