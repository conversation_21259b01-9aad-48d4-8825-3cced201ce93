package com.czur.starry.device.sharescreen.esharelib.ui

import android.graphics.Color
import android.os.Bundle
import com.czur.czurutils.img.QrCodeUtil
import com.czur.starry.device.baselib.base.v2.fragment.floating.CZVBFloatingFragment
import com.czur.starry.device.baselib.utils.launch
import com.czur.starry.device.sharescreen.esharelib.R
import com.czur.starry.device.sharescreen.esharelib.databinding.FloatQrCodeBinding
import com.czur.starry.device.sharescreen.esharelib.util.logoBitmap

/**
 * Created by 陈丰尧 on 2021/12/27
 */
class GuideNoNetFloat(
    private val title: String,
    private val url: String,
    private val textSize: Float = 24F,
) : CZVBFloatingFragment<FloatQrCodeBinding>() {
    override fun FloatingFragmentParams.initFloatingParams() {
        floatingRepeatMode = FloatingRepeatMode.Single("GuideNoNetFloat")
    }

    override fun FloatQrCodeBinding.initBindingViews() {
        closeBtn.setOnClickListener {
            dismiss()
        }

        qrCodeTitleTv.text = title
        qrCodeTitleTv.textSize = textSize
        qrCodeGuideTv.setText(R.string.str_guide_no_network)
    }

    override fun initData(savedInstanceState: Bundle?) {
        super.initData(savedInstanceState)
        launch {
            val bitmap = QrCodeUtil.generateQrCodeBmp(url) {
                this.pointColor = 0xFF5879FC.toInt()
                this.bgColor = Color.WHITE
                this.edge = 400
                this.logoConfig {
                    this.logoBmp = logoBitmap
                    delPadding = true
                }
            }
            binding.qrCodeIv.setImageBitmap(bitmap)
        }
    }
}