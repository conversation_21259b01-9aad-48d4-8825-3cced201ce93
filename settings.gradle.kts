pluginManagement {
    repositories {
        maven { url = uri("https://mirrors.tencent.com/nexus/repository/gradle-plugins") }
        mavenCentral()
        maven { url = uri("https://maven.aliyun.com/repository/public") }
        maven { url = uri("https://maven.aliyun.com/repository/google") }
        maven { url = uri("https://maven.aliyun.com/repository/jcenter") }
        maven { url = uri("https://maven.aliyun.com/nexus/content/groups/public") }
        gradlePluginPortal()
        google()
    }
}

dependencyResolutionManagement {
    repositoriesMode.set(RepositoriesMode.FAIL_ON_PROJECT_REPOS)
    repositories {
//        maven { url = uri("https://mirrors.cloud.tencent.com/nexus/repository/maven-public") }
        maven { url = uri("https://maven.aliyun.com/repository/public") }
        maven { url = uri("https://maven.aliyun.com/repository/google") }
        maven { url = uri("https://maven.aliyun.com/repository/jcenter") }
        maven { url = uri("https://maven.aliyun.com/nexus/content/groups/public") }
        mavenCentral()
        maven {
            url = uri("https://nexus.czur.com/repository/czur_android_host")
            credentials {
                username = "appgroup"
                password = "yswkNP4M43YqeUcc"
            }
        }
        maven { url = uri("https://jitpack.io") }
        google()
    }
}

rootProject.name = "starry"
include(":apps:noticeCenter")
include(":apps:personalcenter")
include(":apps:startupsettings")
include(":apps:czkeystone")
include(":screensharing")
include(
    "apps:settings",
    "apps:launcher",
    ":baselib",
    "apps:filemanager",
    "thirdlibs:popwindow",
    "apps:updateota"
)
include(":base:filelib")
include(":base:meetlib")
include(":base:noticeLib")
include(":base:otalib")
include(":aars:jsBridge")
include(":aars:eShare")
include(":aars:renderscriptToolkit")
include(":apps:hdmiin")
include(":base:hdmiLib")
include(":apps:diagnosis")
include(":base:tempOSS")
include(":thirdlibs:photoview")
include(":base:DiagnosisLib")
include(":apps:appstore")
include(":apps:sharescreen")
include(":base:eShareLib")
include(":apps:starryPad")
include(":apps:memoryservice")
include(":apps:localmeetingrecord")
include(":apps:wallpaperdisplay")
include(":base:BluetoothLib")
include(":base:SettingsLib")
include(":base:UILib")
include(":base:fwApi")
include(":base:starryPadLib")
include(":apps:smallroundscreen")
include(":apps:voiceassistant")
include(":apps:transcription")