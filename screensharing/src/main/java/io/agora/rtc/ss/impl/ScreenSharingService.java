package io.agora.rtc.ss.impl;

import static io.agora.rtc2.Constants.CHANNEL_PROFILE_LIVE_BROADCASTING;
import static io.agora.rtc2.Constants.CLIENT_ROLE_BROADCASTER;
import static io.agora.rtc2.video.VideoEncoderConfiguration.DEGRADATION_PREFERENCE.MAINTAIN_BALANCED;
import static io.agora.rtc2.video.VideoEncoderConfiguration.DEGRADATION_PREFERENCE.MAINTAIN_FRAMERATE;
import static io.agora.rtc2.video.VideoEncoderConfiguration.DEGRADATION_PREFERENCE.MAINTAIN_QUALITY;

import android.app.Service;
import android.content.Context;
import android.content.Intent;
import android.os.IBinder;
import android.os.RemoteCallbackList;
import android.os.RemoteException;
import android.util.DisplayMetrics;
import android.util.Log;
import android.view.WindowManager;

import com.czur.czurutils.ProcessUtilsKt;
import com.czur.czurutils.log.CZURLogSaveConfig;
import com.czur.czurutils.log.CZURLogUtilsKt;

import java.io.File;

import io.agora.rtc.ss.CZURLogBridge;
import io.agora.rtc.ss.Constant;
import io.agora.rtc.ss.aidl.INotification;
import io.agora.rtc.ss.aidl.IScreenSharing;
import io.agora.rtc2.ChannelMediaOptions;
import io.agora.rtc2.Constants;
import io.agora.rtc2.IRtcEngineEventHandler;
import io.agora.rtc2.RtcEngine;
import io.agora.rtc2.video.ScreenCaptureParameters;
import io.agora.rtc2.video.VideoEncoderConfiguration;
import kotlin.Unit;
import kotlin.jvm.functions.Function1;

public class ScreenSharingService extends Service {

    private static final String LOG_TAG = ScreenSharingService.class.getSimpleName();


    private RtcEngine mRtcEngine;
    private Intent projectionIntent;

    // 可能是由于隐藏导航栏的原因, 导致屏幕宽高通过api获取的值不对,
    // 具体原因 时间所限 暂时搁置调查
    // 设备的分辨率是固定的, 所以这里先固定写死
    private static final int SCREEN_WIDTH = 1920;
    private static final int SCREEN_HEIGHT = 1080;

    private RemoteCallbackList<INotification> mCallbacks
            = new RemoteCallbackList<>();

    private String token;
    private int uid;
    private String channelName;

    private final IScreenSharing.Stub mBinder = new IScreenSharing.Stub() {
        public void registerCallback(INotification cb) {
            if (cb != null) mCallbacks.register(cb);
        }

        public void unregisterCallback(INotification cb) {
            if (cb != null) mCallbacks.unregister(cb);
        }

        public void startShare() {
            joinChannel();
        }

        public void stopShare() {
            stopCapture();
        }

        public void renewToken(String token) {
            refreshToken(token);
        }
    };

    private void deInitModules() {
        mRtcEngine.leaveChannel();
        RtcEngine.destroy();
        mRtcEngine = null;
    }


    private void prepareCapture(ScreenCaptureParameters params) {
        CZURLogBridge.logD("startCapture: ");
        mRtcEngine.startScreenCapture(projectionIntent, params);
    }

    private void stopCapture() {
        CZURLogBridge.logD("stopCapture: ");
        mRtcEngine.stopScreenCapture();
        stopSelf();
    }

    private void refreshToken(String token) {
        if (mRtcEngine != null) {
            mRtcEngine.renewToken(token);
        } else {
            CZURLogBridge.logE("rtc engine is null");
        }
    }

    @Override
    public IBinder onBind(Intent intent) {
        projectionIntent = intent.getParcelableExtra(Constant.INTENT);
        token = intent.getStringExtra(Constant.ACCESS_TOKEN);
        channelName = intent.getStringExtra(Constant.CHANNEL_NAME);

        uid = intent.getIntExtra(Constant.UID, 0);
        setUpEngine(intent);
        ScreenCaptureParameters params = setUpVideoConfig(intent);
        prepareCapture(params);
        return mBinder;
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
        deInitModules();
    }

    private void joinChannel() {
        ChannelMediaOptions option = new ChannelMediaOptions();
        option.publishScreenTrack = true;
        option.publishCameraTrack = false;
        // 不订阅任何数据流
        option.autoSubscribeAudio = false;
        option.autoSubscribeVideo = false;


        mRtcEngine.joinChannel(token, channelName, uid, option);
    }

    private void setUpEngine(Intent intent) {
        String appId = intent.getStringExtra(Constant.APP_ID);
        CZURLogBridge.logD("setUpEngine: appID:" + appId);
        try {
            mRtcEngine = RtcEngine.create(getApplicationContext(), appId, new IRtcEngineEventHandler() {
                @Override
                public void onJoinChannelSuccess(String channel, int uid, int elapsed) {
                    CZURLogBridge.logD("onJoinChannelSuccess " + channel + " " + elapsed);
                }

                @Override
                public void onWarning(int warn) {
                    CZURLogBridge.logD("onWarning " + warn);
                }

                @Override
                public void onError(int err) {
                    CZURLogBridge.logD("onError " + err);
                }

                @Override
                public void onRequestToken() {
                    final int N = mCallbacks.beginBroadcast();
                    for (int i = 0; i < N; i++) {
                        try {
                            mCallbacks.getBroadcastItem(i).onError(Constants.ERR_INVALID_TOKEN);
                        } catch (RemoteException e) {
                            // The RemoteCallbackList will take care of removing
                            // the dead object for us.
                        }
                    }
                    mCallbacks.finishBroadcast();
                }

                @Override
                public void onTokenPrivilegeWillExpire(String token) {
                    final int N = mCallbacks.beginBroadcast();
                    for (int i = 0; i < N; i++) {
                        try {
                            mCallbacks.getBroadcastItem(i).onTokenWillExpire();
                        } catch (RemoteException e) {
                            // The RemoteCallbackList will take care of removing
                            // the dead object for us.
                        }
                    }
                    mCallbacks.finishBroadcast();
                }

                @Override
                public void onConnectionStateChanged(int state, int reason) {
                    switch (state) {
                        case Constants.CONNECTION_STATE_FAILED:
                            final int N = mCallbacks.beginBroadcast();
                            for (int i = 0; i < N; i++) {
                                try {
                                    mCallbacks.getBroadcastItem(i).onError(Constants.CONNECTION_STATE_FAILED);
                                } catch (RemoteException e) {
                                    // The RemoteCallbackList will take care of removing
                                    // the dead object for us.
                                }
                            }
                            mCallbacks.finishBroadcast();
                            break;
                        default:
                            break;
                    }
                }
            });
        } catch (Exception e) {
            CZURLogBridge.logE("setUpEngine Error", e);

            throw new RuntimeException("NEED TO check rtc sdk init fatal error\n" + Log.getStackTraceString(e));
        }
        mRtcEngine.setLogFile("/sdcard/ss_svr.log");
        mRtcEngine.setChannelProfile(CHANNEL_PROFILE_LIVE_BROADCASTING);
        mRtcEngine.enableVideo();

        mRtcEngine.setClientRole(CLIENT_ROLE_BROADCASTER);

        mRtcEngine.muteAllRemoteAudioStreams(true);
        mRtcEngine.muteAllRemoteVideoStreams(true);
        mRtcEngine.disableAudio();

        // 改善分享开启的时候, 会模糊的问题
        mRtcEngine.setParameters("{\"che.video.android_bitrate_adjustment_type\":2}");
    }

    private ScreenCaptureParameters setUpVideoConfig(Intent intent) {
        ScreenCaptureParameters captureParameters = new ScreenCaptureParameters();
        int width = intent.getIntExtra(Constant.WIDTH, SCREEN_WIDTH);
        int height = intent.getIntExtra(Constant.HEIGHT, SCREEN_HEIGHT);
        int frameRate = intent.getIntExtra(Constant.FRAME_RATE, 5);
        int bitRate = intent.getIntExtra(Constant.BITRATE, 0);
        int orientationMode = intent.getIntExtra(Constant.ORIENTATION_MODE, 0);
        int degradationPreferValue = intent.getIntExtra(Constant.DEGRADATION_PREFER, MAINTAIN_QUALITY.getValue());
        VideoEncoderConfiguration.DEGRADATION_PREFERENCE degradation;
        switch (degradationPreferValue) {
            case 1:
                degradation = MAINTAIN_FRAMERATE;
                CZURLogBridge.logV("setUpVideoConfig: 帧率优先");
                break;
            case 2:
                degradation = MAINTAIN_BALANCED;
                CZURLogBridge.logV("setUpVideoConfig: 平衡");
                break;
            default:
                degradation = MAINTAIN_QUALITY;
                CZURLogBridge.logV("setUpVideoConfig: 分辨率优先");
                break;

        }
        VideoEncoderConfiguration.FRAME_RATE fr;
        VideoEncoderConfiguration.ORIENTATION_MODE om;

        switch (frameRate) {
            case 1:
                fr = VideoEncoderConfiguration.FRAME_RATE.FRAME_RATE_FPS_1;
                break;
            case 7:
                fr = VideoEncoderConfiguration.FRAME_RATE.FRAME_RATE_FPS_7;
                break;
            case 10:
                fr = VideoEncoderConfiguration.FRAME_RATE.FRAME_RATE_FPS_10;
                break;
            case 15:
                fr = VideoEncoderConfiguration.FRAME_RATE.FRAME_RATE_FPS_15;
                break;
            case 24:
                fr = VideoEncoderConfiguration.FRAME_RATE.FRAME_RATE_FPS_24;
                break;
            case 30:
                fr = VideoEncoderConfiguration.FRAME_RATE.FRAME_RATE_FPS_30;
                break;
            default:
                CZURLogBridge.logD("setUpVideoConfig: 使用特殊帧率" + frameRate);
                fr = null;
                break;
        }

        switch (orientationMode) {
            case 1:
                om = VideoEncoderConfiguration.ORIENTATION_MODE.ORIENTATION_MODE_FIXED_LANDSCAPE;
                break;
            case 2:
                om = VideoEncoderConfiguration.ORIENTATION_MODE.ORIENTATION_MODE_FIXED_PORTRAIT;
                break;
            default:
                om = VideoEncoderConfiguration.ORIENTATION_MODE.ORIENTATION_MODE_ADAPTIVE;
                break;
        }

        VideoEncoderConfiguration videoConfiguration = new VideoEncoderConfiguration();
        CZURLogBridge.logD("setUpVideoConfig: 分辨率:" + width + "*" + height);
        // 设置分辨率
        videoConfiguration.dimensions = new VideoEncoderConfiguration.VideoDimensions(width, height);
        // 设置帧率
        if (fr != null) {
            videoConfiguration.frameRate = fr.getValue();
        } else {
            videoConfiguration.frameRate = frameRate;
        }
        CZURLogBridge.logD("setUpVideoConfig: 帧率:" + videoConfiguration.frameRate);
        // 设置比特率
        videoConfiguration.bitrate = bitRate;
        CZURLogBridge.logD("setUpVideoConfig: 比特率:" + videoConfiguration.bitrate);
        CZURLogBridge.logD("setUpVideoConfig: 最低码率:1710");
        videoConfiguration.minBitrate = 1710;   // 设置最码率
        // 设置屏幕方向
        videoConfiguration.orientationMode = om;
        CZURLogBridge.logD("setUpVideoConfig: 屏幕方向:" + om);
        // 设置分辨率优先
        videoConfiguration.degradationPrefer = degradation;
        CZURLogBridge.logD("setUpVideoConfig: 弱网偏好:" + degradation);

        mRtcEngine.setVideoEncoderConfiguration(videoConfiguration);

        // 分辨率
        captureParameters.setVideoDimensions(new VideoEncoderConfiguration.VideoDimensions(width, height));
        // 帧率
        captureParameters.setFrameRate(frameRate);
        // 比特率
        captureParameters.setBitrate(bitRate);
        return captureParameters;
    }
}
